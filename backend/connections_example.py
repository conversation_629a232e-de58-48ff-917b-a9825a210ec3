import asyncio
import time
from typing import Any

import httpx
from langchain_core.tools import StructuredTool
from mcp.types import CallToolResult, Tool

from app.modules.connectors.mcp_client.custom import (
    NonTextContent,
    _convert_call_tool_result,
)

# Base URL for the API
BASE_URL = "http://localhost:3124"


async def get_connection_tools(config: dict[str, Any]) -> list[Tool]:
    """
    Fetch available tools from a connection using POST method.

    Args:
        config: Connection configuration with command, args, and env

    Returns:
        List of available tools
    """
    async with httpx.AsyncClient() as client:
        response = await client.post(
            f"{BASE_URL}/connections/tools", json={"connection_config": config}
        )
        response.raise_for_status()

        tools = response.json().get("tools", [])
        return [Tool(**tool_dict) for tool_dict in tools]


async def convert_connection_tool_to_langchain_tool(
    tool: Tool, connection_config: dict
) -> StructuredTool:
    """
    Convert an MCP tool to a Langchain tool.
    Ref: https://github.com/langchain-ai/langchain-mcp-adapters/blob/main/langchain_mcp_adapters/tools.py#L100
    """

    async def call_tool(
        **arguments: dict[str, Any],
    ) -> tuple[str | list[str], list[NonTextContent] | None]:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{BASE_URL}/connections/execute",
                json={
                    "connection_config": connection_config,
                    "tool_name": tool.name,
                    "arguments": arguments,
                },
            )
            response.raise_for_status()
            call_tool_result_response = response.json().get("tool_result", {})
            call_tool_result = CallToolResult(**call_tool_result_response)
            return _convert_call_tool_result(call_tool_result)

    return StructuredTool(
        name=tool.name,
        description=tool.description or "",
        args_schema=tool.inputSchema,
        coroutine=call_tool,
        response_format="content_and_artifact",
        metadata=tool.annotations.model_dump() if tool.annotations else None,
    )


async def main():
    # Example connection configuration
    config = {
        "command": "uvx",
        "args": ["postgres-mcp", "--access-mode=unrestricted"],
        "env": {"DATABASE_URI": "postgresql://postgres:123456789x%40X@db:5432/app"}
    }

    try:
        # Get available tools
        print("Fetching available tools...")
        start_time = time.time()
        connection_tools = await get_connection_tools(config)
        fetch_time = time.time() - start_time
        print(f"Fetched tools in {fetch_time:.2f} seconds")

        langchain_tools = []
        for tool in connection_tools:
            langchain_tools.append(
                await convert_connection_tool_to_langchain_tool(tool, config)
            )

        langchain_tools_dict = {tool.name: tool for tool in langchain_tools}

        # Example tool call (replace with an actual tool from your response)
        tool_name = "list_schemas"
        tool_args = {}

        print(f"\nCalling tool '{tool_name}'...")
        start_time = time.time()
        tool_result = await langchain_tools_dict[tool_name].ainvoke(tool_args)
        call_time = time.time() - start_time
        print(f"Tool result: {tool_result}")
        print(f"Tool call completed in {call_time:.2f} seconds")

    except httpx.HTTPStatusError as e:
        print(f"HTTP error occurred: {e.response.status_code} - {e.response.text}")
    except Exception as e:
        print(f"An error occurred: {str(e)}")


if __name__ == "__main__":
    asyncio.run(main())
