import uuid
from typing import Any

from fastapi import APIRouter, HTTPException
from starlette.responses import <PERSON><PERSON><PERSON>esponse

from app.api.deps import CurrentUser, SessionAsyncDep
from app.logger import logger
from app.models import (
    ConnectionCreate,
    ConnectionPublic,
    ConnectionsPublic,
    ConnectionUpdate,
    Message,
)
from app.services.connection_service import ConnectionService

router = APIRouter()


@router.post("/", response_model=ConnectionPublic)
async def create_connection(
    *,
    session: SessionAsyncDep,
    current_user: CurrentUser,
    conn_create: ConnectionCreate,
) -> Any:
    try:
        conn_service = ConnectionService(session)
        mcp_server = await conn_service.create_connection(
            workspace_id=current_user.current_workspace_id, data=conn_create
        )
        return mcp_server
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception:
        logger.exception("Error creating MCP server.")
        raise HTTPException(status_code=500, detail="Integration service temporarily unavailable")


@router.get("/", response_model=ConnectionsPublic)
async def get_connections(
    *,
    session: SessionAsyncDep,
    current_user: CurrentUser,
) -> Any:
    try:
        conn_service = ConnectionService(session)
        conns = await conn_service.get_connections(current_user.current_workspace_id)
        return conns
    except Exception:
        logger.exception("Error retrieving connections.")
        raise HTTPException(status_code=500, detail="Integration service temporarily unavailable")


@router.get("/builtin", response_model=ConnectionsPublic)
async def get_builtin_connections(
    *,
    session: SessionAsyncDep,
    current_user: CurrentUser,
) -> Any:
    """Get all available builtin connections."""
    try:
        conn_service = ConnectionService(session)
        builtin_conns = await conn_service.get_builtin_connections()
        return builtin_conns
    except Exception:
        logger.exception("Error retrieving builtin connections.")
        raise HTTPException(status_code=500, detail="Integration service temporarily unavailable")


@router.post("/builtin/{builtin_id}/install", response_model=ConnectionPublic)
async def install_builtin_connection(
    *,
    session: SessionAsyncDep,
    current_user: CurrentUser,
    builtin_id: uuid.UUID,
) -> Any:
    """Install a builtin connection to the user's workspace."""
    try:
        conn_service = ConnectionService(session)
        installed_conn = await conn_service.install_builtin_connection(
            workspace_id=current_user.current_workspace_id,
            builtin_connection_id=builtin_id
        )
        if not installed_conn:
            raise HTTPException(status_code=404, detail="Builtin connection not found")
        return installed_conn
    except ValueError as e:
        logger.exception(f"Error installing builtin connection: {e}")
        raise HTTPException(status_code=400, detail="Error installing builtin connection")
    except Exception:
        logger.exception("Error installing builtin connection.")
        raise HTTPException(status_code=500, detail="Integration service temporarily unavailable")


@router.get("/{conn_id}", response_model=ConnectionPublic)
async def get_connection(
    *,
    session: SessionAsyncDep,
    current_user: CurrentUser,
    conn_id: uuid.UUID,
) -> Any:
    try:
        conn_service = ConnectionService(session)
        conn = await conn_service.get_connection(current_user.current_workspace_id, conn_id)
        if not conn:
            raise HTTPException(status_code=404, detail="Connection not found")
        return conn
    except HTTPException:
        raise
    except Exception:
        logger.exception("Error retrieving connection.")
        raise HTTPException(status_code=500, detail="Integration service temporarily unavailable")


@router.put("/{conn_id}", response_model=ConnectionPublic)
async def update_connection(
    *,
    session: SessionAsyncDep,
    current_user: CurrentUser,
    conn_id: uuid.UUID,
    conn_update: ConnectionUpdate,
) -> Any:
    try:
        conn_service = ConnectionService(session)
        updated_server = await conn_service.update_connection(
            workspace_id=current_user.current_workspace_id,
            conn_id=conn_id,
            data=conn_update,
        )
        if not updated_server:
            raise HTTPException(status_code=404, detail="Connection not found")
        return updated_server
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except HTTPException:
        raise
    except Exception:
        logger.exception("Error updating connection.")
        raise HTTPException(status_code=500, detail="Integration service temporarily unavailable")


@router.delete("/{conn_id}", response_model=Message)
async def delete_connection(
    *,
    session: SessionAsyncDep,
    current_user: CurrentUser,
    conn_id: uuid.UUID,
) -> Any:
    try:
        conn_service = ConnectionService(session)
        success = await conn_service.delete_connection(current_user.current_workspace_id, conn_id)
        if not success:
            raise HTTPException(status_code=404, detail="Connection not found")
        return JSONResponse(
            content={"message": "Connection successfully deleted"},
            status_code=200,
        )
    except HTTPException:
        raise
    except Exception:
        logger.exception("Error deleting connection.")
        raise HTTPException(status_code=500, detail="Integration service temporarily unavailable")


@router.post("/{conn_id}/refresh", response_model=ConnectionPublic)
async def install_builtin_connection(
    *,
    session: SessionAsyncDep,
    current_user: CurrentUser,
    builtin_id: uuid.UUID,
) -> Any:
    """Install a builtin connection to the user's workspace."""
    try:
        conn_service = ConnectionService(session)
        installed_conn = await conn_service.install_builtin_connection(
            workspace_id=current_user.current_workspace_id,
            builtin_connection_id=builtin_id
        )
        if not installed_conn:
            raise HTTPException(status_code=404, detail="Builtin connection not found")
        return installed_conn
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception:
        logger.exception("Error installing builtin connection.")
        raise HTTPException(status_code=500, detail="Integration service temporarily unavailable")
