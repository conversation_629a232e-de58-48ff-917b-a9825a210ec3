from uuid import UUID

from sqlalchemy.orm import selectinload
from sqlmodel import col, select
from sqlmodel.ext.asyncio.session import AsyncSession

from app.core.exceptions import RepositoryError
from app.logger import logger
from app.models import AgentBuiltInTool, BuiltInTool, WorkspaceBuiltInTool


class BuiltInToolRepository:
    """Repository for managing WorkspaceBuiltInTool entities."""

    def __init__(self, session: AsyncSession):
        """Initialize with an async database session."""
        self.session = session

    async def get_workspace_builtin_tools(
        self, workspace_id: UUID
    ) -> list[WorkspaceBuiltInTool]:
        """Get all built-in tools associated with a workspace."""
        try:
            # Create a query to select WorkspaceBuiltInTool with related BuiltInTool
            statements = (
                select(WorkspaceBuiltInTool)
                .where(WorkspaceBuiltInTool.workspace_id == workspace_id)
                .options(selectinload(WorkspaceBuiltInTool.builtin_tool))
            )

            # Execute the query
            result = await self.session.exec(statements)
            return list(result.all())
        except Exception as e:
            logger.error(e)
            raise RepositoryError(
                status_code=500,
                message="Failed to get workspace builtin tools.",
            )

    async def get_workspace_builtin_tool(
        self, workspace_id: UUID, workspace_builtin_tool_id: UUID
    ) -> WorkspaceBuiltInTool | None:
        """Get a specific built-in tool by ID for a workspace."""
        try:
            query = (
                select(WorkspaceBuiltInTool)
                .where(
                    WorkspaceBuiltInTool.workspace_id == workspace_id,
                    WorkspaceBuiltInTool.id == workspace_builtin_tool_id,
                )
                .options(selectinload(WorkspaceBuiltInTool.builtin_tool))
            )
            result = await self.session.exec(query)
            return result.first()
        except Exception as e:
            logger.error(e)
            raise RepositoryError(
                status_code=500,
                message="Failed to get workspace builtin tool.",
            )

    async def update_workspace_builtin_tool(
        self,
        workspace_id: UUID,
        workspace_builtin_tool_id: UUID,
        required_permission: bool,
    ) -> bool | None:
        """Update a built-in tool's active status and permission requirement for a workspace.

        Args:
            workspace_builtin_tool_id: ID of the workspace built-in tool to update
            workspace_id: ID of the workspace
            required_permission: Whether the tool requires permission (optional)

        Returns:
            True if update was successful, False if tool not found
        """
        try:
            workspace_builtin_tool = await self.get_workspace_builtin_tool(
                workspace_id, workspace_builtin_tool_id
            )
            if not workspace_builtin_tool:
                raise RepositoryError(
                    status_code=404,
                    message="Workspace builtin tool not found.",
                )

            workspace_builtin_tool.required_permission = required_permission

            self.session.add(workspace_builtin_tool)
            await self.session.commit()
            await self.session.refresh(workspace_builtin_tool)
            return True
        except Exception as e:
            logger.error(e)
            await self.session.rollback()
            raise RepositoryError(
                status_code=500,
                message="Failed to update workspace builtin tool.",
            )

    async def get_all_agent_builtin_tools(
        self, agents_ids: list[UUID]
    ) -> dict[UUID, list[BuiltInTool]]:
        """Get all builtin tools associated with agents using the many-to-many relationship."""
        try:
            statement = (
                select(AgentBuiltInTool, BuiltInTool)
                .join(
                    WorkspaceBuiltInTool,
                    col(AgentBuiltInTool.workspace_builtin_tool_id)
                    == col(WorkspaceBuiltInTool.id),
                )
                .join(
                    BuiltInTool,
                    col(WorkspaceBuiltInTool.builtin_tool_id) == col(BuiltInTool.id),
                )
                .where(col(AgentBuiltInTool.agent_id).in_(agents_ids))
            )
            result = await self.session.exec(statement)

            # Group builtin tools by agent ID
            agent_tools: dict[UUID, list[BuiltInTool]] = {}
            for agent_builtin_tool, tool in result:
                if agent_builtin_tool.agent_id not in agent_tools:
                    agent_tools[agent_builtin_tool.agent_id] = []
                agent_tools[agent_builtin_tool.agent_id].append(tool)
            return agent_tools
        except Exception as e:
            logger.error(e)
            raise RepositoryError(
                status_code=500,
                message="Failed to get all agent builtin tools.",
            )

    async def init_default_workspace_builtin_tools(self, workspace_id: UUID) -> None:
        """Initialize all builtin tools for a workspace.

        Args:
            workspace_id: ID of the workspace to initialize tools for
        """
        try:
            # Get all available builtin tools
            all_builtin_tools_result = await self.session.exec(select(BuiltInTool))
            all_builtin_tools = list(all_builtin_tools_result.all())

            if not all_builtin_tools:
                logger.info("No builtin tools found in system")
                return

            # Get existing workspace builtin tools
            existing_tools_result = await self.session.exec(
                select(WorkspaceBuiltInTool).where(
                    WorkspaceBuiltInTool.workspace_id == workspace_id
                )
            )
            existing_tools = list(existing_tools_result.all())
            existing_tool_ids = {tool.builtin_tool_id for tool in existing_tools}

            # Create missing workspace builtin tools
            new_workspace_tools = []
            for builtin_tool in all_builtin_tools:
                if builtin_tool.id not in existing_tool_ids:
                    workspace_tool = WorkspaceBuiltInTool(
                        workspace_id=workspace_id,
                        builtin_tool_id=builtin_tool.id,
                        required_permission=builtin_tool.default_required_permission,
                    )
                    new_workspace_tools.append(workspace_tool)
                    self.session.add(workspace_tool)

            # Commit new tools
            if new_workspace_tools:
                await self.session.commit()

        except Exception as e:
            logger.error(f"Failed to initialize workspace builtin tools: {e}")
            await self.session.rollback()
            raise RepositoryError(
                status_code=500,
                message="Failed to initialize workspace builtin tools.",
            )
