#!/usr/bin/env python3
"""
Builtin Connection Migration Script

This script manages builtin connections in the database by:
1. Reading predefined builtin connections from configuration
2. Comparing with existing builtin connections in database
3. Creating, updating, or removing connections as needed
4. Testing connections and populating tool information

Should be run during application startup via prestart.sh
"""

import asyncio
import sys
from datetime import datetime
from uuid import uuid4

from sqlmodel import select

from app.core.db_session import get_task_session
from app.logger import logger

# Avoid circular import by importing models directly and delaying other imports
from app.models import (
    Connection,
    ConnectionBase,
    ConnectionStatus,
    ConnectionTransport,
    ConnectionType,
)

# Define builtin connections configuration
BUILTIN_CONNECTIONS = [
    {
        "name": "PostgreSQL",
        "prefix": "postgres",
        "type": ConnectionType.BUILTIN,
        "transport_type": ConnectionTransport.STREAMABLE_HTTP,
        "config": {
            "command": "uvx",
            "args": ["postgres-mcp", "--access-mode=unrestricted"],
            "env": {"DATABASE_URI": "postgresql://postgres:123456789x%40X@db:5432/app"},
        },
        "is_active": True,
        "tool_permissions": [],
    },
]


class BuiltinConnectionMigrator:
    def __init__(self):
        pass

    async def _test_connection_directly(self, connection_base: ConnectionBase) -> tuple:
        """Test connection directly using MCP manager client to avoid circular imports."""
        try:
            # Import only what we need to avoid circular dependencies
            from app.modules.connectors.connection_client import MCPManagerClient

            mcp_manager_client = MCPManagerClient()

            if connection_base.type == ConnectionType.BUILTIN:
                # Use MCP manager client for builtin connections
                connection_result = await mcp_manager_client.test_connection(connection_base)

                return (
                    connection_result["status"],
                    connection_result["status_message"],
                    connection_result["tool_list"],
                    connection_result["tool_schemas"]
                )
            else:
                # For non-builtin connections, we'll skip testing in migration
                return (
                    ConnectionStatus.ERROR,
                    "Non-builtin connections not supported in migration",
                    [],
                    {}
                )

        except Exception as e:
            logger.error(f"Error testing connection {connection_base.name}: {str(e)}")
            return (
                ConnectionStatus.ERROR,
                f"Connection test failed: {str(e)}",
                [],
                {}
            )

    async def migrate_builtin_connections(self) -> None:
        """Main migration function that synchronizes builtin connections."""
        try:
            async with get_task_session() as session:
                # Get existing builtin connections from database
                existing_connections = await self._get_existing_builtin_connections(session)

                # Create lookup dict by name for existing connections
                existing_by_name = {conn.name: conn for conn in existing_connections}

                # Get builtin connection names from configuration
                builtin_names = {conn_config["name"] for conn_config in BUILTIN_CONNECTIONS}

                # Process each builtin connection
                for conn_config in BUILTIN_CONNECTIONS:
                    name = conn_config["name"]
                    if name in existing_by_name:
                        # Update existing connection if changed
                        await self._update_connection_if_changed(
                            session, existing_by_name[name], conn_config
                        )
                    else:
                        # Create new builtin connection
                        await self._create_builtin_connection(session, conn_config)

                # Remove connections that are no longer in builtin config
                for existing_conn in existing_connections:
                    if existing_conn.name not in builtin_names:
                        await self._remove_builtin_connection(session, existing_conn)

                await session.commit()
                logger.info("Builtin connection migration completed successfully")

        except Exception as e:
            logger.error(f"Error during builtin connection migration: {str(e)}")
            raise

    async def _get_existing_builtin_connections(self, session) -> list[Connection]:
        """Get all existing builtin connections from database."""
        try:
            # Query for builtin connections in this workspace
            statement = select(Connection).where(
                Connection.workspace_id is None,
                Connection.type == ConnectionType.BUILTIN
            )
            result = await session.exec(statement)
            connections = result.all()

            logger.info(f"Found {len(connections)} existing builtin connections")
            return connections

        except Exception as e:
            logger.error(f"Error retrieving existing builtin connections: {str(e)}")
            raise

    async def _test_and_populate_connection_info(
        self, connection: Connection, conn_config: dict
    ) -> None:
        """Test connection and populate tool information."""
        try:
            # Create ConnectionBase for testing
            test_conn = ConnectionBase(
                name=conn_config["name"],
                prefix=conn_config["prefix"],
                type=conn_config["type"],
                transport_type=conn_config["transport_type"],
                config=conn_config["config"],
                is_active=conn_config["is_active"],
                tool_permissions=conn_config["tool_permissions"],
                tool_enabled=[],  # Will be populated after connection test
            )

            # Test the connection directly
            status, status_message, tool_list, tool_schemas = await self._test_connection_directly(test_conn)

            # Update connection with test results
            connection.status = status
            connection.status_message = status_message
            connection.tool_list = tool_list or []
            connection.tool_schemas = tool_schemas or {}
            connection.status_updated_at = datetime.now()

            # Enable all tools by default if connection is successful
            if status == ConnectionStatus.CONNECTED:
                connection.tool_enabled = tool_list or []
                logger.info(f"Successfully tested builtin connection '{conn_config['name']}' with {len(tool_list)} tools")
            else:
                connection.tool_enabled = []
                logger.warning(f"Builtin connection '{conn_config['name']}' test failed: {status_message}")

        except Exception as e:
            logger.error(f"Error testing builtin connection '{conn_config['name']}': {str(e)}")
            connection.status = ConnectionStatus.ERROR
            connection.status_message = f"Connection test failed: {str(e)}"
            connection.tool_list = []
            connection.tool_schemas = {}
            connection.tool_enabled = []
            connection.status_updated_at = datetime.now()

    async def _create_builtin_connection(self, session, conn_config: dict) -> None:
        """Create a new builtin connection."""
        try:
            connection = Connection(
                id=uuid4(),
                workspace_id=None,
                name=conn_config["name"],
                prefix=conn_config["prefix"],
                type=conn_config["type"],
                transport_type=conn_config["transport_type"],
                config=conn_config["config"],
                is_active=conn_config["is_active"],
                tool_permissions=conn_config["tool_permissions"],
                tool_enabled=[],  # Will be populated after connection test
                status=ConnectionStatus.ERROR,  # Will be updated during connection test
                status_message="",
                tool_list=[],
                tool_schemas={},
            )

            session.add(connection)

            # Test the connection and populate tool information
            await self._test_and_populate_connection_info(connection, conn_config)

            logger.info(f"Created new builtin connection: {conn_config['name']}")

        except Exception as e:
            logger.error(f"Error creating builtin connection {conn_config['name']}: {str(e)}")
            raise

    async def _update_connection_if_changed(
        self, session, existing_conn: Connection, conn_config: dict
    ) -> None:
        """Update existing connection if configuration has changed."""
        try:
            # Check if any fields have changed
            changes_detected = (
                existing_conn.prefix != conn_config["prefix"]
                or existing_conn.transport_type != conn_config["transport_type"]
                or existing_conn.config != conn_config["config"]
                or existing_conn.is_active != conn_config["is_active"]
                or existing_conn.tool_permissions != conn_config["tool_permissions"]
            )

            if changes_detected:
                # Update the connection fields
                existing_conn.prefix = conn_config["prefix"]
                existing_conn.transport_type = conn_config["transport_type"]
                existing_conn.config = conn_config["config"]
                existing_conn.is_active = conn_config["is_active"]
                existing_conn.tool_permissions = conn_config["tool_permissions"]

                session.add(existing_conn)

                # Test the connection and update tool information
                await self._test_and_populate_connection_info(existing_conn, conn_config)

                logger.info(f"Updated builtin connection: {conn_config['name']}")
            else:
                # Even if config hasn't changed, refresh connection status and tools
                await self._test_and_populate_connection_info(existing_conn, conn_config)
                logger.debug(f"Refreshed builtin connection: {conn_config['name']}")

        except Exception as e:
            logger.error(f"Error updating builtin connection {conn_config['name']}: {str(e)}")
            raise

    async def _remove_builtin_connection(self, session, existing_conn: Connection) -> None:
        """Remove a builtin connection that's no longer in configuration."""
        try:
            await session.delete(existing_conn)
            logger.info(f"Removed obsolete builtin connection: {existing_conn.name}")

        except Exception as e:
            logger.error(f"Error removing builtin connection {existing_conn.name}: {str(e)}")
            raise

async def main():
    """Main function for running the migration."""
    try:
        logger.info("Starting builtin connection migration...")

        # Run migration
        migrator = BuiltinConnectionMigrator()
        await migrator.migrate_builtin_connections()

        logger.info("Builtin connection migration completed successfully")

    except Exception as e:
        logger.error(f"Builtin connection migration failed: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
