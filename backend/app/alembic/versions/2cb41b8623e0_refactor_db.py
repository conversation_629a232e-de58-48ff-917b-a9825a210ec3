"""refactor db

Revision ID: 2cb41b8623e0
Revises: fe665fc4697d
Create Date: 2025-07-17 15:40:14.898878

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '2cb41b8623e0'
down_revision = 'fe665fc4697d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('builtintool',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('name', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('display_name', sqlmodel.sql.sqltypes.AutoString(), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('default_required_permission', sa.<PERSON>(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.<PERSON>umn('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_builtintool_name'), 'builtintool', ['name'], unique=True)
    op.create_table('workspacebuiltintool',
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('required_permission', sa.Boolean(), nullable=False),
    sa.Column('workspace_id', sa.Uuid(), nullable=False),
    sa.Column('builtin_tool_id', sa.Uuid(), nullable=False),
    sa.ForeignKeyConstraint(['builtin_tool_id'], ['builtintool.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['workspace_id'], ['workspace.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('agent_builtin_tools',
    sa.Column('agent_id', sa.Uuid(), nullable=False),
    sa.Column('workspace_builtin_tool_id', sa.Uuid(), nullable=False),
    sa.ForeignKeyConstraint(['agent_id'], ['agent.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['workspace_builtin_tool_id'], ['workspacebuiltintool.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('agent_id', 'workspace_builtin_tool_id')
    )
    op.create_table('agent_mcp_servers',
    sa.Column('agent_id', sa.Uuid(), nullable=False),
    sa.Column('mcp_server_id', sa.Uuid(), nullable=False),
    sa.ForeignKeyConstraint(['agent_id'], ['agent.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['mcp_server_id'], ['mcpserver.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('agent_id', 'mcp_server_id')
    )
    op.drop_table('agentconnectorbuiltinconnector')
    op.drop_table('agentconnector')
    op.drop_table('workspacebuiltinconnector')
    op.drop_index('ix_builtinconnector_name', table_name='builtinconnector')
    op.drop_table('builtinconnector')
    # Add columns as nullable first
    op.add_column('agent', sa.Column('alias', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True))
    op.add_column('agent', sa.Column('role', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True))
    op.add_column('agent', sa.Column('goal', sqlmodel.sql.sqltypes.AutoString(length=2048), nullable=True))
    
    # Update existing records with default values
    op.execute("UPDATE agent SET alias = 'Agent' WHERE alias IS NULL")
    op.execute("UPDATE agent SET role = 'Assistant' WHERE role IS NULL")
    op.execute("UPDATE agent SET goal = 'Help users with their tasks' WHERE goal IS NULL")
    
    # Now make them NOT NULL
    op.alter_column('agent', 'alias', nullable=False)
    op.alter_column('agent', 'role', nullable=False)
    op.alter_column('agent', 'goal', nullable=False)
    op.drop_column('agent', 'description')
    op.drop_index('ix_mcpserver_is_builtin', table_name='mcpserver')
    op.drop_column('mcpserver', 'is_builtin')
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('mcpserver', sa.Column('is_builtin', sa.BOOLEAN(), autoincrement=False, nullable=False))
    op.create_index('ix_mcpserver_is_builtin', 'mcpserver', ['is_builtin'], unique=False)
    op.add_column('agent', sa.Column('description', sa.TEXT(), autoincrement=False, nullable=True))
    op.drop_column('agent', 'goal')
    op.drop_column('agent', 'role')
    op.drop_column('agent', 'alias')
    op.create_table('agentconnectorbuiltinconnector',
    sa.Column('agent_connector_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('workspace_builtin_connector_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['agent_connector_id'], ['agentconnector.id'], name='agentconnectorbuiltinconnector_agent_connector_id_fkey', ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['workspace_builtin_connector_id'], ['workspacebuiltinconnector.id'], name='agentconnectorbuiltinconnecto_workspace_builtin_connector__fkey', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('agent_connector_id', 'workspace_builtin_connector_id', name='agentconnectorbuiltinconnector_pkey')
    )
    op.create_table('agentconnector',
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('agent_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('mcp_servers', postgresql.ARRAY(sa.VARCHAR()), autoincrement=False, nullable=True),
    sa.ForeignKeyConstraint(['agent_id'], ['agent.id'], name='agentconnector_agent_id_fkey', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name='agentconnector_pkey')
    )
    op.create_table('builtinconnector',
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('name', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('display_name', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('description', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('default_required_permission', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.Column('updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.PrimaryKeyConstraint('id', name='builtinconnector_pkey'),
    postgresql_ignore_search_path=False
    )
    op.create_index('ix_builtinconnector_name', 'builtinconnector', ['name'], unique=True)
    op.create_table('workspacebuiltinconnector',
    sa.Column('id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('workspace_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('connector_id', sa.UUID(), autoincrement=False, nullable=False),
    sa.Column('is_active', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('required_permission', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['connector_id'], ['builtinconnector.id'], name='workspacebuiltinconnector_connector_id_fkey', ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['workspace_id'], ['workspace.id'], name='workspacebuiltinconnector_workspace_id_fkey', ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name='workspacebuiltinconnector_pkey')
    )
    op.drop_table('agent_mcp_servers')
    op.drop_table('agent_builtin_tools')
    op.drop_table('workspacebuiltintool')
    op.drop_index(op.f('ix_builtintool_name'), table_name='builtintool')
    op.drop_table('builtintool')
    # ### end Alembic commands ###
