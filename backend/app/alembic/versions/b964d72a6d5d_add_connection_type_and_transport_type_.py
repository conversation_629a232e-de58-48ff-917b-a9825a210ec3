"""Add connection type and transport type fields

Revision ID: b964d72a6d5d
Revises: 81bc3c3e0114
Create Date: 2025-07-21 09:17:14.141025

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes
from alembic_postgresql_enum import TableReference
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'b964d72a6d5d'
down_revision = '81bc3c3e0114'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    
    # Create the new ConnectionType enum
    sa.Enum('BUILTIN', 'MCP', name='connectiontype').create(op.get_bind())
    
    # Add transport_type column as nullable first using a copy of the existing type
    op.add_column('connection', sa.Column('transport_type_temp', sa.Text, nullable=True))
    
    # Copy existing type values as text to temp column
    op.execute("UPDATE connection SET transport_type_temp = type::text")
    
    # Create the actual transport_type column
    op.add_column('connection', sa.Column('transport_type', postgresql.ENUM('STREAMABLE_HTTP', 'SSE', name='connectiontransport', create_type=False), nullable=True))
    
    # Populate transport_type from temp column
    op.execute("UPDATE connection SET transport_type = transport_type_temp::connectiontransport WHERE transport_type_temp IN ('STREAMABLE_HTTP', 'SSE')")
    op.execute("UPDATE connection SET transport_type = 'STREAMABLE_HTTP'::connectiontransport WHERE transport_type_temp = 'STDIO'")
    
    # Drop the temp column
    op.drop_column('connection', 'transport_type_temp')
    
    # Make transport_type non-nullable now that it's populated
    op.alter_column('connection', 'transport_type', nullable=False)
    
    # Add temp column for type conversion
    op.add_column('connection', sa.Column('type_temp', sa.Text, nullable=True))
    
    # Set all existing connections to MCP type in temp column
    op.execute("UPDATE connection SET type_temp = 'MCP'")
    
    # Drop the old type column
    op.drop_column('connection', 'type')
    
    # Create new type column with correct enum
    op.add_column('connection', sa.Column('type', sa.Enum('BUILTIN', 'MCP', name='connectiontype'), nullable=True))
    
    # Populate new type column
    op.execute("UPDATE connection SET type = type_temp::connectiontype")
    
    # Drop temp column
    op.drop_column('connection', 'type_temp')
    
    # Make type non-nullable
    op.alter_column('connection', 'type', nullable=False)
               
    # Remove STDIO from the transport enum
    op.sync_enum_values(
        enum_schema='public',
        enum_name='connectiontransport',
        new_values=['STREAMABLE_HTTP', 'SSE'],
        affected_columns=[TableReference(table_schema='public', table_name='connection', column_name='transport_type')],
        enum_values_to_rename=[],
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.sync_enum_values(
        enum_schema='public',
        enum_name='connectiontransport',
        new_values=['STREAMABLE_HTTP', 'SSE', 'STDIO'],
        affected_columns=[TableReference(table_schema='public', table_name='connection', column_name='transport_type')],
        enum_values_to_rename=[],
    )
    op.alter_column('connection', 'type',
               existing_type=sa.Enum('BUILTIN', 'MCP', name='connectiontype'),
               type_=postgresql.ENUM('STREAMABLE_HTTP', 'SSE', 'STDIO', name='connectiontransport'),
               existing_nullable=False)
    op.drop_column('connection', 'transport_type')
    sa.Enum('BUILTIN', 'MCP', name='connectiontype').drop(op.get_bind())
    # ### end Alembic commands ###
