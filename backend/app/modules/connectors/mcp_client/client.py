from langchain_core.tools import BaseTool
from langchain_mcp_adapters.client import MultiServerMCPClient

from app.core.config import settings
from app.core.exceptions import MCPClientError
from app.logger import logger
from app.models import ConnectionBase

from .custom import load_mcp_tools


class MCPClientConnector:
    """
    Enhanced MCP Client Connector supporting multiple server connections

    This connector allows you to connect to multiple MCP servers simultaneously
    and manage their sessions and tools in a unified interface.
    """

    def __init__(self, mcp_servers: list[ConnectionBase] | None = None):
        """
        Initialize MCPClientConnector with optional server connections

        Args:
            connections: Dictionary mapping server names to their connection configs
        """
        self.mcp_servers: dict[str, ConnectionBase] = {
            server.name: server for server in mcp_servers or []
        }
        self.client = MultiServerMCPClient(
            connections={
                server.name: {
                    "url": server.config.get("url", ""),
                    "timeout": server.config.get(
                        "timeout", settings.DEFAULT_MCP_TIMEOUT
                    ),
                    "sse_read_timeout": server.config.get(
                        "sse_read_timeout", settings.DEFAULT_MCP_SSE_READ_TIMEOUT
                    ),
                    "headers": server.config.get("headers", {}),
                    "transport": server.type,
                }
                for server in self.mcp_servers.values()
            }
        )
        self.connection_status: dict[str, dict] = {}
        for server in self.mcp_servers.values():
            self.connection_status[server.name] = {
                "status": None,
                "status_message": None,
            }
        self.cached_tools: dict[str, list[BaseTool]] = {}

    async def list_tools(
        self, server_name: str, filter_enabled: bool = True
    ) -> list[BaseTool]:
        """
        List tools from an MCP server
        """
        if server_name not in self.client.connections:
            self.connection_status[server_name]["status"] = "error"
            self.connection_status[server_name]["status_message"] = (
                f"Server {server_name} not found"
            )
            raise MCPClientError(f"Server {server_name} not found")

        if server_name in self.cached_tools:
            return self.cached_tools[server_name]

        try:
            async with self.client.session(server_name) as session:
                tools = await load_mcp_tools(session)

                prefix_tools = []
                for tool in tools:
                    tool.name = f"{self.mcp_servers[server_name].prefix}__{tool.name}"
                    prefix_tools.append(tool)

                # Filter enabled tools
                if filter_enabled:
                    tool_enabled = self.mcp_servers[server_name].tool_enabled
                    prefix_tools = [
                        tool for tool in prefix_tools if tool.name in tool_enabled
                    ]

                self.connection_status[server_name]["status"] = "connected"
                self.connection_status[server_name]["status_message"] = (
                    "Tools listed successfully"
                )

                self.cached_tools[server_name] = prefix_tools

                # Remove the coroutine from the tools
                for tool in prefix_tools:
                    tool.coroutine = None

            return prefix_tools
        except Exception as e:
            logger.exception(f"Error listing tools for server {server_name}: {e}")
            self.connection_status[server_name]["status"] = "error"
            self.connection_status[server_name]["status_message"] = (
                f"Error listing tools for server {server_name}"
            )
            raise MCPClientError(f"Error listing tools for server {server_name}")

    async def get_tool_schemas(
        self, server_name: str, filter_enabled: bool = True
    ) -> dict[str, dict]:
        """
        Get tool schemas from an MCP server

        Returns:
            Dictionary mapping tool names to their JSON schemas
        """
        if server_name not in self.client.connections:
            self.connection_status[server_name]["status"] = "error"
            self.connection_status[server_name]["status_message"] = (
                f"Server {server_name} not found"
            )
            raise MCPClientError(f"Server {server_name} not found")

        try:
            async with self.client.session(server_name) as session:
                tools = await load_mcp_tools(session)

                tool_schemas = {}
                for tool in tools:
                    prefixed_name = f"{self.mcp_servers[server_name].prefix}__{tool.name}"

                    # Get the schema from the tool's args_schema
                    if hasattr(tool, 'args_schema') and tool.args_schema:
                        # Convert Pydantic model to JSON schema
                        if hasattr(tool.args_schema, 'model_json_schema'):
                            schema = tool.args_schema.model_json_schema()
                        elif hasattr(tool.args_schema, 'schema'):
                            schema = tool.args_schema.schema()
                        else:
                            # Fallback to basic schema structure
                            schema = {
                                "type": "object",
                                "properties": {},
                                "description": getattr(tool, 'description', '')
                            }
                    else:
                        # Default schema for tools without args_schema
                        schema = {
                            "type": "object",
                            "properties": {},
                            "description": getattr(tool, 'description', '')
                        }

                    tool_schemas[prefixed_name] = schema

                # Filter enabled tools
                if filter_enabled:
                    tool_enabled = self.mcp_servers[server_name].tool_enabled
                    tool_schemas = {
                        name: schema for name, schema in tool_schemas.items()
                        if name in tool_enabled
                    }

                return tool_schemas
        except Exception as e:
            logger.exception(f"Error getting tool schemas for server {server_name}: {e}")
            self.connection_status[server_name]["status"] = "error"
            self.connection_status[server_name]["status_message"] = (
                f"Error getting tool schemas for server {server_name}"
            )
            raise MCPClientError(f"Error getting tool schemas for server {server_name}")

    async def list_all_tools_by_server_names(
        self, server_names: list[str] | None = None
    ) -> list[BaseTool]:
        """
        List all tools by server names
        """
        all_tools: list[BaseTool] = []
        for server_name in server_names:
            try:
                all_tools.extend(await self.list_tools(server_name))
            except MCPClientError:
                continue
        return all_tools

    async def call_tool(self, tool_name: str, **kwargs):
        """
        Call a tool by parsing server name from prefixed tool name

        Args:
            tool_name: Prefixed tool name in format "{server_prefix}__{actual_tool_name}"
            **kwargs: Tool arguments

        Returns:
            Tool execution result
        """
        # Parse server prefix and actual tool name
        if "__" not in tool_name:
            raise MCPClientError(
                f"Invalid tool name format: {tool_name}. Expected format: 'prefix__tool_name'"
            )

        prefix, actual_tool_name = tool_name.split("__", 1)

        # Find server by prefix
        server_name = None
        for name, server in self.mcp_servers.items():
            if server.prefix == prefix:
                server_name = name
                break

        if server_name is None:
            raise MCPClientError(f"Tool {tool_name} not found")

        try:
            async with self.client.session(server_name) as session:
                # Get the tool and invoke it
                tools = await load_mcp_tools(session)

                # Find the actual tool by original name
                target_tool = None
                for tool in tools:
                    if tool.name == actual_tool_name:
                        target_tool = tool
                        break

                if target_tool is None:
                    raise MCPClientError(f"Tool {tool_name} not found")

                # Call the tool
                result = await target_tool.ainvoke(kwargs)

            return result

        except Exception as e:
            raise MCPClientError(f"Error calling tool {tool_name}: {str(e)}")

    def get_connection_status(self) -> dict[str, dict]:
        """
        Get the connection status for all servers

        Returns:
            Dictionary mapping server names to their connection status
        """
        return self.connection_status
