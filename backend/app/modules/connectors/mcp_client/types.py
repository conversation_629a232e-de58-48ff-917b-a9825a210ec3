"""
Type definitions for MCP client connector.
"""

from enum import Enum
from typing import Any, Literal, TypedDict


class ConnectionStatus(str, Enum):
    """Status of server connection"""

    DISCONNECTED = "disconnected"
    CONNECTED = "connected"
    ERROR = "error"


class ConnectionStatusInfo(TypedDict):
    """Information about the connection status"""

    status: ConnectionStatus
    error_message: str | None
    connected_at: float | None


class StdioConnection(TypedDict):
    """Configuration for stdio-based MCP server connection"""

    transport: Literal["stdio"]
    command: str  # The executable to run
    args: list[str]  # Command line arguments
    env: dict[str, str] | None  # Environment variables
    encoding: str  # Text encoding for messages
    encoding_error_handler: Literal[
        "strict", "ignore", "replace"
    ]  # Encoding error handler


class SSEConnection(TypedDict):
    """Configuration for SSE-based MCP server connection"""

    transport: Literal["sse"]
    url: str  # SSE endpoint URL
    headers: dict[str, Any] | None  # HTTP headers
    timeout: float  # HTTP timeout
    sse_read_timeout: float  # SSE read timeout


# Type for connection configurations
ConnectionConfig = dict[str, StdioConnection | SSEConnection]
ConnectionStatusMap = dict[str, ConnectionStatusInfo]
