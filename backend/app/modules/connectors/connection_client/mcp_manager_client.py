from datetime import datetime
from typing import Any

import httpx
from langchain_core.tools import StructuredTool
from mcp.types import CallToolR<PERSON>ult, Tool

from app.core.config import settings
from app.core.exceptions import MCPClientError
from app.logger import logger
from app.models import ConnectionBase, ConnectionStatus
from app.modules.connectors.mcp_client.custom import (
    NonTextContent,
    _convert_call_tool_result,
)


class MCPManagerClient:
    def __init__(self, base_url: str = None):
        self.base_url = base_url or settings.MCP_MANAGER_BASE_URL
        self.connection_status: dict[str, dict] = {}

    async def get_connection_tools(self, connection: ConnectionBase) -> list[Tool]:
        config = connection.config or {}
        command = config.get("command")
        args = config.get("args", [])
        env = config.get("env", {})

        connection_config = {"command": command, "args": args, "env": env}

        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    f"{self.base_url}/connections/tools",
                    json={"connection_config": connection_config},
                )
                response.raise_for_status()

                tools_data = response.json().get("tools", [])
                tools = [Tool(**tool_dict) for tool_dict in tools_data]

                self.connection_status[connection.name] = {
                    "status": ConnectionStatus.CONNECTED,
                    "status_message": f"Successfully loaded {len(tools)} tools",
                    "status_updated_at": datetime.now(),
                }

                logger.info(
                    f"Successfully fetched {len(tools)} tools for connection {connection.name}"
                )
                return tools

        except httpx.HTTPStatusError as e:
            error_msg = f"HTTP error {e.response.status_code}: {e.response.text}"
            logger.error(
                f"Error fetching tools for connection {connection.name}: {error_msg}"
            )

            self.connection_status[connection.name] = {
                "status": ConnectionStatus.ERROR,
                "status_message": error_msg,
                "status_updated_at": datetime.now(),
            }
            raise MCPClientError(f"Failed to fetch tools: {error_msg}")

        except Exception as e:
            error_msg = (
                f"Error fetching tools for connection {connection.name}: {str(e)}"
            )
            logger.error(error_msg)

            self.connection_status[connection.name] = {
                "status": ConnectionStatus.ERROR,
                "status_message": str(e),
                "status_updated_at": datetime.now(),
            }
            raise MCPClientError(error_msg)

    async def get_tool_schemas(self, connection: ConnectionBase) -> dict[str, dict]:
        tools = await self.get_connection_tools(connection)

        tool_schemas = {}
        for tool in tools:
            prefixed_name = f"{connection.prefix}__{tool.name}"

            if tool.inputSchema:
                schema = tool.inputSchema
            else:
                schema = {
                    "type": "object",
                    "properties": {},
                    "description": tool.description or "",
                }

            tool_schemas[prefixed_name] = schema

        return tool_schemas

    async def convert_connection_tool_to_langchain_tool(
        self, tool: Tool, connection: ConnectionBase
    ) -> StructuredTool:
        config = connection.config or {}
        command = config.get("command")
        args = config.get("args", [])
        env = config.get("env", {})

        connection_config = {"command": command, "args": args, "env": env}

        async def call_tool(
            **arguments: dict[str, Any],
        ) -> tuple[str | list[str], list[NonTextContent] | None]:
            try:
                async with httpx.AsyncClient(timeout=60.0) as client:
                    response = await client.post(
                        f"{self.base_url}/connections/execute",
                        json={
                            "connection_config": connection_config,
                            "tool_name": tool.name,
                            "arguments": arguments,
                        },
                    )
                    response.raise_for_status()

                    call_tool_result_response = response.json().get("tool_result", {})
                    call_tool_result = CallToolResult(**call_tool_result_response)
                    return _convert_call_tool_result(call_tool_result)

            except Exception as e:
                logger.error(f"Error executing tool {tool.name}: {str(e)}")
                raise MCPClientError(f"Tool execution failed: {str(e)}")

        return StructuredTool(
            name=f"{connection.prefix}__{tool.name}",
            description=tool.description or "",
            args_schema=tool.inputSchema,
            coroutine=call_tool,
            response_format="content_and_artifact",
            metadata=tool.annotations.model_dump() if tool.annotations else None,
        )

    async def test_connection(self, connection: ConnectionBase) -> dict[str, Any]:
        try:
            tools = await self.get_connection_tools(connection)
            tool_schemas = await self.get_tool_schemas(connection)

            return {
                "status": ConnectionStatus.CONNECTED,
                "status_message": f"Successfully connected and loaded {len(tools)} tools",
                "tool_list": [f"{connection.prefix}__{tool.name}" for tool in tools],
                "tool_schemas": tool_schemas,
                "status_updated_at": datetime.now(),
            }

        except MCPClientError as e:
            return {
                "status": ConnectionStatus.ERROR,
                "status_message": str(e),
                "tool_list": [],
                "tool_schemas": {},
                "status_updated_at": datetime.now(),
            }

    def get_connection_status(self, connection_name: str) -> dict[str, Any] | None:
        return self.connection_status.get(connection_name)

    def get_all_connection_statuses(self) -> dict[str, dict[str, Any]]:
        return self.connection_status.copy()
