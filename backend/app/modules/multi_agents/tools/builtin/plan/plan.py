import json
from datetime import datetime
from typing import Literal

from langchain_core.tools import ArgsSchema, BaseTool

from .model import PlanManager
from .schema import PlanInput, PlanStep

_PLANNING_TOOL_DESCRIPTION = """
A planning tool for managing and tracking complex tasks through structured plans.
Each plan contains steps with status tracking and progress monitoring.

Key Features:
1. Plan Operations:
   - Create and update plans
   - List all plans and their status
   - Get/set active plan
   - Track step completion

2. Status Tracking:
   - 🟢 ACTIVE: Current working plan
   - [✓] Completed step
   - [!] Blocked step
   - [ ] Not started step

Commands:
1. create: New plan with steps
2. update: Modify plan/steps
3. list: View all plans
4. get: View specific plan
5. get_active: View current plan
6. set_active: Change active plan
7. mark_step: Update step status

Guidelines:
- Steps must be completed in sequence
- Document blockers in notes
- Use notes for context
"""


class PlanningTool(BaseTool):
    """A tool for managing multi-step plans and tracking their progress."""

    name: str = "planning"
    description: str = _PLANNING_TOOL_DESCRIPTION
    args_schema: ArgsSchema | None = PlanInput
    plan_manager: PlanManager | None = None

    def __init__(self, plan_manager: PlanManager | None = None, **kwargs):
        super().__init__(**kwargs)
        self.plan_manager = plan_manager

    def _run(
        self,
        command: Literal[
            "create", "update", "list", "get", "set_active", "mark_step", "get_active"
        ],
        plan_id: str | None = None,
        title: str | None = None,
        steps: list[PlanStep] | None = None,
        step_index: list[int] | None = None,
        step_status: list[Literal["not_started", "completed", "blocked"]] | None = None,
        step_notes: list[str] | None = None,
    ) -> str:
        """Execute the planning tool with the given command and parameters"""
        if not self.plan_manager:
            return json.dumps(
                {
                    "status": "error",
                    "message": "Plan manager not initialized. Please provide a state with plan manager.",
                }
            )

        if command == "create":
            return self._create_plan(plan_id, title, steps)
        elif command == "update":
            return self._update_plan(plan_id, title, steps)
        elif command == "list":
            return self._list_plans()
        elif command == "get":
            return self._get_plan(plan_id)
        elif command == "set_active":
            return self._set_active_plan(plan_id)
        elif command == "mark_step":
            return self._mark_step(plan_id, step_index, step_status, step_notes)
        elif command == "get_active":
            return self._get_active_plan()
        else:
            return json.dumps(
                {
                    "status": "error",
                    "message": f"Unrecognized command: {command}. Allowed commands are: create, update, list, get, set_active, mark_step, get_active",
                }
            )

    def _get_plan_status(self, plan: dict) -> tuple[str, str]:
        """Get the status of a plan.

        Args:
            plan: The plan dictionary to check

        Returns:
            tuple[str, str]: A tuple containing (plan_status, reason)
        """
        step_statuses = plan["step_statuses"]

        if "blocked" in step_statuses:
            return "blocked", "One or more steps are blocked"

        if all(status == "completed" for status in step_statuses):
            return "completed", "All steps are completed"

        return "in_progress", "Plan has steps that are not completed"

    def _create_plan(
        self, plan_id: str | None, title: str | None, steps: list[PlanStep] | None
    ) -> str:
        """Create a new plan with the given ID, title, and steps."""
        if not plan_id:
            return json.dumps(
                {
                    "status": "error",
                    "message": "Parameter `plan_id` is required for command: create",
                }
            )

        plans, _ = self.plan_manager.get_state()
        if plan_id in plans:
            return json.dumps(
                {
                    "status": "error",
                    "message": f"A plan with ID '{plan_id}' already exists. Use 'update' to modify existing plans.",
                }
            )

        if not title:
            return json.dumps(
                {
                    "status": "error",
                    "message": "Parameter `title` is required for command: create",
                }
            )

        if (
            not steps
            or not isinstance(steps, list)
            or not all(isinstance(step, PlanStep) for step in steps)
        ):
            return json.dumps(
                {
                    "status": "error",
                    "message": "Parameter `steps` must be a non-empty list of PlanStep objects for command: create",
                }
            )

        # Create a new plan with initialized step statuses
        plan = {
            "plan_id": plan_id,
            "title": title,
            "steps": [step.step for step in steps],
            "step_statuses": ["not_started"] * len(steps),
            "step_notes": [""] * len(steps),
            "created_at": datetime.utcnow().isoformat(),
            "updated_at": datetime.utcnow().isoformat(),
        }

        plans[plan_id] = plan
        self.plan_manager.current_plan_id = plan_id  # Set as active plan

        plan_status, reason = self._get_plan_status(plan)
        formatted_plan = self._format_plan(plan)
        formatted_plan["plan_status"] = plan_status
        formatted_plan["status_reason"] = reason

        return json.dumps(
            {
                "status": "success",
                "message": "Plan created successfully",
                "data": {"plan_id": plan_id, "plan": formatted_plan},
            }
        )

    def _update_plan(
        self, plan_id: str | None, title: str | None, steps: list[PlanStep] | None
    ) -> str:
        """Update an existing plan with new title or steps."""
        if not plan_id:
            return json.dumps(
                {
                    "status": "error",
                    "message": "Parameter `plan_id` is required for command: update",
                }
            )

        plans, _ = self.plan_manager.get_state()
        if plan_id not in plans:
            return json.dumps(
                {"status": "error", "message": f"No plan found with ID: {plan_id}"}
            )

        plan = plans[plan_id]

        if title:
            plan["title"] = title
            plan["updated_at"] = datetime.utcnow().isoformat()

        if steps:
            if not isinstance(steps, list) or not all(
                isinstance(step, PlanStep) for step in steps
            ):
                return json.dumps(
                    {
                        "status": "error",
                        "message": "Parameter `steps` must be a list of PlanStep objects for command: update",
                    }
                )

            # Preserve existing step statuses for unchanged steps
            old_steps = plan["steps"]
            old_statuses = plan["step_statuses"]
            old_notes = plan["step_notes"]

            # Create new step statuses and notes
            new_statuses = []
            new_notes = []

            for i, step in enumerate(steps):
                # If the step exists at the same position in old steps, preserve status and notes
                if i < len(old_steps) and step.step == old_steps[i]:
                    new_statuses.append(old_statuses[i])
                    new_notes.append(old_notes[i])
                else:
                    new_statuses.append("not_started")
                    new_notes.append("")

            plan["steps"] = [step.step for step in steps]
            plan["step_statuses"] = new_statuses
            plan["step_notes"] = new_notes
            plan["updated_at"] = datetime.utcnow().isoformat()

        plan_status, reason = self._get_plan_status(plan)
        formatted_plan = self._format_plan(plan)
        formatted_plan["plan_status"] = plan_status
        formatted_plan["status_reason"] = reason

        return json.dumps(
            {
                "status": "success",
                "message": "Plan updated successfully",
                "data": {"plan_id": plan_id, "plan": formatted_plan},
            }
        )

    def _list_plans(self) -> str:
        """List all available plans."""
        plans, current_plan_id = self.plan_manager.get_state()
        if not plans:
            return json.dumps(
                {
                    "status": "success",
                    "message": "No plans available",
                    "data": {"plans": []},
                }
            )

        plans_list = []
        for plan_id, plan in plans.items():
            completed = sum(
                1 for status in plan["step_statuses"] if status == "completed"
            )
            total = len(plan["steps"])
            plan_status, reason = self._get_plan_status(plan)
            is_active = plan_id == current_plan_id
            plans_list.append(
                {
                    "plan_id": plan_id,
                    "title": plan["title"],
                    "is_active": is_active,
                    "active_indicator": "🟢 ACTIVE" if is_active else "",
                    "plan_status": plan_status,
                    "status_reason": reason,
                    "created_at": plan.get("created_at", ""),
                    "updated_at": plan.get("updated_at", ""),
                    "progress": {
                        "completed": completed,
                        "total": total,
                        "percentage": round((completed / total) * 100, 1)
                        if total > 0
                        else 0,
                    },
                }
            )

        # Sort plans by updated_at timestamp (most recent last)
        plans_list.sort(key=lambda x: x["updated_at"])

        return json.dumps(
            {
                "status": "success",
                "message": "Plans retrieved successfully",
                "data": {"plans": plans_list},
            }
        )

    def _get_plan(self, plan_id: str | None) -> str:
        """Get details of a specific plan."""
        plans, current_plan_id = self.plan_manager.get_state()
        if not plan_id:
            # If no plan_id is provided, use the current active plan
            if not current_plan_id:
                return json.dumps(
                    {
                        "status": "error",
                        "message": "No active plan. Please specify a plan_id or set an active plan.",
                    }
                )
            plan_id = current_plan_id

        if plan_id not in plans:
            return json.dumps(
                {"status": "error", "message": f"No plan found with ID: {plan_id}"}
            )

        plan = plans[plan_id]
        plan_status, reason = self._get_plan_status(plan)
        formatted_plan = self._format_plan(plan)
        formatted_plan["plan_status"] = plan_status
        formatted_plan["status_reason"] = reason

        return json.dumps(
            {
                "status": "success",
                "message": "Plan retrieved successfully",
                "data": {"plan_id": plan_id, "plan": formatted_plan},
            }
        )

    def _set_active_plan(self, plan_id: str | None) -> str:
        """Set a plan as the active plan."""
        if not plan_id:
            return json.dumps(
                {
                    "status": "error",
                    "message": "Parameter `plan_id` is required for command: set_active",
                }
            )

        plans, _ = self.plan_manager.get_state()
        if plan_id not in plans:
            return json.dumps(
                {"status": "error", "message": f"No plan found with ID: {plan_id}"}
            )

        self.plan_manager.current_plan_id = plan_id
        plan = plans[plan_id]
        plan_status, reason = self._get_plan_status(plan)
        formatted_plan = self._format_plan(plan)
        formatted_plan["plan_status"] = plan_status
        formatted_plan["status_reason"] = reason

        return json.dumps(
            {
                "status": "success",
                "message": "Active plan set successfully",
                "data": {"plan_id": plan_id, "plan": formatted_plan},
            }
        )

    def _mark_step(
        self,
        plan_id: str | None,
        step_index: int | list[int] | None,
        step_status: str | list[str] | None,
        step_notes: str | list[str] | None,
    ) -> str:
        """Mark one or multiple steps with specific statuses and optional notes."""
        plans, current_plan_id = self.plan_manager.get_state()
        if not plan_id:
            if not current_plan_id:
                return json.dumps(
                    {
                        "status": "error",
                        "message": "No active plan. Please specify a plan_id or set an active plan.",
                    }
                )
            plan_id = current_plan_id

        if plan_id not in plans:
            return json.dumps(
                {"status": "error", "message": f"No plan found with ID: {plan_id}"}
            )

        if step_index is None:
            return json.dumps(
                {
                    "status": "error",
                    "message": "Parameter `step_index` is required for command: mark_step",
                }
            )

        plan = plans[plan_id]
        total_steps = len(plan["steps"])

        # Convert single values to lists for uniform handling
        indices = [step_index] if isinstance(step_index, int) else step_index
        statuses = [step_status] if isinstance(step_status, str) else step_status
        notes = [step_notes] if isinstance(step_notes, str) else step_notes

        # Validate indices
        for idx in indices:
            if idx < 0 or idx >= total_steps:
                return json.dumps(
                    {
                        "status": "error",
                        "message": f"Invalid step_index: {idx}. Valid indices range from 0 to {total_steps - 1}.",
                    }
                )

        # Validate statuses if provided
        if statuses:
            for status in statuses:
                if status and status not in ["not_started", "completed", "blocked"]:
                    return json.dumps(
                        {
                            "status": "error",
                            "message": f"Invalid step_status: {status}. Valid statuses are: not_started, completed, blocked",
                        }
                    )

        # Check if any step is being marked as completed
        if statuses and any(status == "completed" for status in statuses):
            first_completed_idx = next(
                (i for i, status in enumerate(statuses) if status == "completed"), None
            )
            if first_completed_idx is not None:
                first_completed_step = indices[first_completed_idx]
                for i in range(first_completed_step):
                    if plan["step_statuses"][i] != "completed":
                        return json.dumps(
                            {
                                "status": "error",
                                "message": f"Cannot mark step {first_completed_step} as completed. Step {i} must be completed first.",
                            }
                        )

        # Check if any step is being marked as blocked
        if statuses and any(status == "blocked" for status in statuses):
            first_blocked_idx = next(
                (i for i, status in enumerate(statuses) if status == "blocked"), None
            )
            if first_blocked_idx is not None:
                first_blocked_step = indices[first_blocked_idx]
                for i in range(first_blocked_step + 1, total_steps):
                    if i not in indices:
                        plan["step_statuses"][i] = "blocked"

        # Update each step
        for i, idx in enumerate(indices):
            if statuses and i < len(statuses):
                plan["step_statuses"][idx] = statuses[i]
            if notes and i < len(notes):
                plan["step_notes"][idx] = notes[i]

        # Update the timestamp
        plan["updated_at"] = datetime.utcnow().isoformat()

        plan_status, reason = self._get_plan_status(plan)
        formatted_plan = self._format_plan(plan)
        formatted_plan["plan_status"] = plan_status
        formatted_plan["status_reason"] = reason

        return json.dumps(
            {
                "status": "success",
                "message": "Steps updated successfully",
                "data": {
                    "plan_id": plan_id,
                    "updated_steps": indices,
                    "plan": formatted_plan,
                },
            }
        )

    def _format_plan(self, plan: dict) -> dict:
        """Format a plan for display in JSON format.

        Args:
            plan: The plan dictionary to format

        Returns:
            str: JSON formatted string representation of the plan
        """
        # Calculate progress statistics
        total_steps = len(plan["steps"])
        completed = sum(1 for status in plan["step_statuses"] if status == "completed")
        blocked = sum(1 for status in plan["step_statuses"] if status == "blocked")
        not_started = sum(
            1 for status in plan["step_statuses"] if status == "not_started"
        )
        percentage = (completed / total_steps) * 100 if total_steps > 0 else 0

        # Create structured data
        formatted_plan = {
            "plan_id": plan["plan_id"],
            "title": plan["title"],
            "progress": {
                "completed": completed,
                "total": total_steps,
                "percentage": round(percentage, 1),
                "status_summary": {
                    "completed": completed,
                    "blocked": blocked,
                    "not_started": not_started,
                },
            },
            "steps": [
                {
                    "index": i,
                    "description": step,
                    "status": status,
                    "status_symbol": {
                        "not_started": "[ ]",
                        "completed": "[✓]",
                        "blocked": "[!]",
                    }.get(status, "[ ]"),
                    "notes": notes,
                }
                for i, (step, status, notes) in enumerate(
                    zip(
                        plan["steps"],
                        plan["step_statuses"],
                        plan["step_notes"],
                        strict=False,
                    )
                )
            ],
        }

        return formatted_plan

    def _get_active_plan(self) -> str:
        """Get the currently active plan.

        Returns:
            str: JSON string containing the active plan details or an error message if no active plan
        """
        plans, current_plan_id = self.plan_manager.get_state()

        if not current_plan_id:
            return json.dumps(
                {"status": "error", "message": "No active plan found.", "data": None}
            )

        if current_plan_id not in plans:
            return json.dumps(
                {
                    "status": "error",
                    "message": f"Active plan with ID '{current_plan_id}' not found in plans.",
                    "data": None,
                }
            )

        plan = plans[current_plan_id]
        plan_status, reason = self._get_plan_status(plan)
        formatted_plan = self._format_plan(plan)
        formatted_plan["plan_status"] = plan_status
        formatted_plan["status_reason"] = reason

        return json.dumps(
            {
                "status": "success",
                "message": "Active plan retrieved successfully",
                "data": {"plan_id": current_plan_id, "plan": formatted_plan},
            }
        )

    def get_list_plans(self) -> str:
        """Get a list of all plans."""
        return json.loads(self._list_plans())["data"]

    def get_active_plan(self) -> str:
        """Get the active plan."""
        return json.loads(self._get_active_plan())["data"]
