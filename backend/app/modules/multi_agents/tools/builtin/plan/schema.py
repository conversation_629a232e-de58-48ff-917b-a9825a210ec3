from datetime import datetime
from typing import Literal

from pydantic import BaseModel, Field


class PlanStep(BaseModel):
    """A single step in a plan.

    Attributes:
        step: Clear, actionable task description.
             Example: "Set up project structure with requirements.txt"
    """

    step: str


class PlanInput(BaseModel):
    """Input schema for the planning tool operations.

    Commands:
    1. create: New plan
       Required: plan_id, title, steps
       Example: Create project setup plan

    2. update: Modify plan
       Required: plan_id
       Optional: title, steps

    3. list: Show all plans
       Shows: Progress, status, active indicator

    4. get: View plan details
       Required: plan_id

    5. get_active: View current plan
       Shows: Active plan details

    6. set_active: Switch active plan
       Required: plan_id

    7. mark_step: Update step status
       Required: plan_id, step_index, step_status
       Optional: step_notes
       Note: Sequential completion required

    Attributes:
        command: Operation to perform
        plan_id: Plan identifier
        title: Plan title
        steps: Sequential steps
        step_index: Steps to update (0-based)
        step_status: New status
        step_notes: Context or notes
    """

    command: Literal[
        "create", "update", "list", "get", "set_active", "mark_step", "get_active"
    ] = Field(
        description="Command to execute: create, update, list, get, set_active, mark_step, get_active"
    )
    plan_id: str | None = Field(
        default=None,
        description="Plan identifier. Required for: create, update, get, set_active. Example: 'setup-project'",
    )
    title: str | None = Field(
        default=None,
        description="Plan title. Required for create. Example: 'Project Setup Plan'",
    )
    steps: list[PlanStep] | None = Field(
        default=None,
        description="Sequential steps to complete. Required for create. Each step should be actionable.",
    )
    step_index: list[int] | None = Field(
        default=None,
        description="Step indices to update (0-based). Required for mark_step. Sequential completion required.",
    )
    step_status: list[Literal["not_started", "completed", "blocked"]] | None = Field(
        default=None,
        description="Step status: not_started, completed, blocked. Used with mark_step.",
    )
    step_notes: list[str] | None = Field(
        default=None,
        description="Step notes. Optional for mark_step. Use for context and blockers.",
    )

    class Config:
        """Pydantic model configuration."""

        extra = "ignore"
        validate_assignment = True
        arbitrary_types_allowed = True
        use_enum_values = True
        validate_default = True
        populate_by_name = True
        json_encoders = {
            datetime: lambda v: v.isoformat(),
        }
