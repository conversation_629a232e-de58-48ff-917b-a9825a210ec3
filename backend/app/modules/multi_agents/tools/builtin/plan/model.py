from pydantic import BaseModel


class PlanManager(BaseModel):
    """Manages the state of plans and their progress.

    This class handles the storage and retrieval of plans, as well as managing
    the active plan state. It provides methods for serializing and deserializing
    the plan state for persistence.
    """

    plans: dict[str, dict] = {}  # Dictionary to store plans by plan_id
    current_plan_id: str | None = None  # Track the current active plan

    def get_state(self) -> tuple[dict[str, dict], str | None]:
        """Get the current state of the plan manager.

        Returns:
            Tuple containing:
            - Dictionary of plans
            - Current active plan ID
        """
        return self.plans, self.current_plan_id
