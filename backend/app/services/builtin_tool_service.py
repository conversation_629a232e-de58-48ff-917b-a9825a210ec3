from uuid import UUID

from sqlmodel.ext.asyncio.session import As<PERSON><PERSON>ess<PERSON>

from app.repositories import BuiltInToolRepository
from app.schemas.builtin_tools import (
    AgentBuiltInToolResponse,
    AgentBuiltInToolsResponse,
    BuiltInToolResponse,
    WorkspaceBuiltInToolResponse,
)


class BuiltInToolService:
    """Service for managing built-in tools and their permission requirements in workspaces."""

    def __init__(self, async_session: AsyncSession):
        """Initialize with an async database session."""
        self.async_session = async_session
        self.repository = BuiltInToolRepository(async_session)

    async def get_workspace_builtin_tools(
        self, workspace_id: UUID
    ) -> list[WorkspaceBuiltInToolResponse]:
        """
        List built-in tools for a specific workspace.

        Args:
            workspace_id: ID of the workspace

        Returns:
            List of workspace builtin tools, sorted by permission requirement and tool name
        """
        workspace_builtin_tools = await self.repository.get_workspace_builtin_tools(
            workspace_id=workspace_id
        )

        # Convert to response objects and sort by required_permission and name
        tools = [
            WorkspaceBuiltInToolResponse(
                id=tool.id,
                required_permission=tool.required_permission,
                builtin_tool=BuiltInToolResponse(
                    name=tool.builtin_tool.name,
                    display_name=tool.builtin_tool.display_name,
                    description=tool.builtin_tool.description,
                ),
            )
            for tool in workspace_builtin_tools
        ]

        return sorted(
            tools,
            key=lambda x: (not x.required_permission, x.builtin_tool.name.lower()),
        )

    async def update_workspace_builtin_tool(
        self,
        workspace_id: UUID,
        workspace_builtin_tool_id: UUID,
        required_permission: bool,
    ) -> bool | None:
        """Update a built-in tool's active status and permission requirement for a workspace."""
        return await self.repository.update_workspace_builtin_tool(
            workspace_id=workspace_id,
            workspace_builtin_tool_id=workspace_builtin_tool_id,
            required_permission=required_permission,
        )

    async def init_default_workspace_built_in_tools(self, workspace_id: UUID) -> None:
        """
        Create default built-in tools for a new workspace.

        Args:
            workspace_id: UUID of the workspace to create tools for
        """
        # Get all built-in tools
        await self.repository.init_default_workspace_builtin_tools(workspace_id)

    async def get_all_agent_builtin_tools(
        self, agents_ids: list[UUID]
    ) -> list[AgentBuiltInToolsResponse]:
        """
        Get all built-in tools associated with agents, including permission requirements.

        Args:
            agents_ids: List of agent IDs to get tools for

        Returns:
            List of agent built-in tools with their permission requirements
        """
        # Get all agent tools from repository
        agent_tools = await self.repository.get_all_agent_builtin_tools(agents_ids)

        # Format the response
        result = []
        for agent_id, tools in agent_tools.items():
            tools_response = [
                AgentBuiltInToolResponse(
                    name=tool.name,
                    display_name=tool.display_name,
                    description=tool.description,
                    required_permission=tool.default_required_permission,
                )
                for tool in tools
            ]

            result.append(
                AgentBuiltInToolsResponse(agent_id=agent_id, tools=tools_response)
            )

        return result
