from datetime import datetime, timezone
from uuid import UUID

from sqlmodel.ext.asyncio.session import AsyncSession

from app.logger import logger
from app.models import (
    Connection,
    ConnectionBase,
    ConnectionConfig,
    ConnectionCreate,
    ConnectionPublic,
    ConnectionsPublic,
    ConnectionStatus,
    ConnectionStatusResponse,
    ConnectionTestParams,
    ConnectionTestResult,
    ConnectionType,
    ConnectionUpdate,
    ConnectionUpdateParams,
)
from app.modules.connectors.connection_client import MCPManagerClient
from app.modules.connectors.mcp_client.client import MCPClientConnector
from app.repositories.connection import ConnectionRepository


class ConnectionService:
    def __init__(self, async_session: AsyncSession):
        self.conn_repository = ConnectionRepository(async_session)
        self.mcp_manager_client = MCPManagerClient()

    async def connect(
        self, conn: ConnectionBase
    ) -> tuple[ConnectionStatusResponse, dict[str, dict]]:
        tool_schemas = {}
        tools = []

        try:
            if conn.type == ConnectionType.BUILTIN:
                # Use MCP manager client for builtin connections
                connection_result = await self.mcp_manager_client.test_connection(conn)

                return (
                    ConnectionStatusResponse(
                        status=connection_result["status"],
                        status_message=connection_result["status_message"],
                        tool_list=connection_result["tool_list"],
                    ),
                    connection_result["tool_schemas"]
                )
            else:
                # Use MCP client for mcp connections (HTTP/SSE)
                mcp_client = MCPClientConnector(mcp_servers=[conn])
                tools = await mcp_client.list_tools(conn.name, filter_enabled=False)
                tool_schemas = await mcp_client.get_tool_schemas(conn.name, filter_enabled=False)

                connection_status = mcp_client.get_connection_status()
                return (
                    ConnectionStatusResponse(
                        status=connection_status[conn.name]["status"],
                        status_message=connection_status[conn.name]["status_message"],
                        tool_list=[tool.name for tool in tools],
                    ),
                    tool_schemas
                )
        except Exception as e:
            logger.error(f"Error connecting to connection {conn.name}: {str(e)}")
            return (
                ConnectionStatusResponse(
                    status=ConnectionStatus.ERROR,
                    status_message=f"Connection failed: {str(e)}",
                    tool_list=[],
                ),
                {}
            )

    def validate_connection_config(self, config: dict, connection_type: ConnectionType = None) -> bool:
        try:
            if connection_type == ConnectionType.BUILTIN:
                # Validate builtin configuration
                required_fields = ["command"]
                if not all(field in config for field in required_fields):
                    logger.error(f"Builtin configuration missing required fields. Required: {required_fields}")
                    return False

                # Validate command is a string
                if not isinstance(config.get("command"), str):
                    logger.error("Builtin command must be a string")
                    return False

                # Validate args is a list if provided
                if "args" in config and not isinstance(config["args"], list):
                    logger.error("Builtin args must be a list")
                    return False

                # Validate env is a dict if provided
                if "env" in config and not isinstance(config["env"], dict):
                    logger.error("Builtin env must be a dict")
                    return False
            else:
                # Validate HTTP/SSE configuration using existing ConnectionConfig
                ConnectionConfig(**config)
        except Exception as e:
            logger.error(f"Invalid connection configuration: {config} - {str(e)}")
            return False
        return True

    async def _test_connection(
        self,
        test_params: ConnectionTestParams,
    ) -> ConnectionTestResult:
        try:
            current_server = await self.conn_repository.get_connection(
                conn_id=test_params.server_id, workspace_id=test_params.workspace_id
            )
            if not current_server:
                return ConnectionTestResult()

            # Build test server configuration
            if test_params.updates:
                updates = test_params.updates
                test_server = ConnectionBase(
                    name=updates.name or current_server.name,
                    prefix=updates.prefix or current_server.prefix,
                    type=updates.type or current_server.type,
                    transport_type=updates.transport_type or current_server.transport_type,
                    config=updates.config or current_server.config,
                    is_active=True,
                    tool_permissions=updates.tool_permissions or current_server.tool_permissions,
                    tool_enabled=updates.tool_enabled or current_server.tool_enabled,
                )
            else:
                # Use current server configuration as-is
                test_server = ConnectionBase(
                    name=current_server.name,
                    prefix=current_server.prefix,
                    type=current_server.type,
                    transport_type=current_server.transport_type,
                    config=current_server.config,
                    is_active=True,
                    tool_permissions=current_server.tool_permissions,
                    tool_enabled=current_server.tool_enabled,
                )

            connection_result, tool_schemas = await self.connect(test_server)
            return ConnectionTestResult(
                status=connection_result.status,
                status_message=connection_result.status_message,
                tool_list=connection_result.tool_list,
                tool_schemas=tool_schemas,
                status_updated_at=datetime.now(timezone.utc),
            )
        except Exception as e:
            return ConnectionTestResult(
                status=ConnectionStatus.ERROR,
                status_message=f"Connection failed: {str(e)}",
                tool_list=[],
                tool_schemas={},
                status_updated_at=datetime.now(timezone.utc),
            )

    async def create_connection(
        self,
        workspace_id: UUID,
        data: ConnectionCreate,
    ) -> Connection:
        # Prevent users from creating builtin connections manually
        if data.type == ConnectionType.BUILTIN:
            raise ValueError("Builtin connections cannot be created manually. They are managed by the system.")

        if data.config and not self.validate_connection_config(data.config, data.type):
            raise ValueError("Invalid connection configuration")

        conn = ConnectionBase(
            name=data.name,
            prefix=data.prefix,
            type=data.type,
            transport_type=data.transport_type,
            config=data.config,
            is_active=data.is_active,
            tool_permissions=data.tool_permissions,
            tool_enabled=data.tool_enabled,
        )

        if data.is_active:
            connection_result, tool_schemas = await self.connect(conn)
            if connection_result.status == ConnectionStatus.CONNECTED:
                conn.status = ConnectionStatus.CONNECTED
                conn.status_message = connection_result.status_message
                conn.tool_list = connection_result.tool_list
                conn.tool_schemas = tool_schemas
                conn.status_updated_at = datetime.now(timezone.utc)
            else:
                conn.status = ConnectionStatus.ERROR
                conn.status_message = connection_result.status_message
                conn.status_updated_at = datetime.now(timezone.utc)

        return await self.conn_repository.create_connection(
            workspace_id=workspace_id, conn_data=conn
        )

    async def get_connections(self, workspace_id: UUID) -> ConnectionsPublic:
        conns = await self.conn_repository.get_connections(
            workspace_id=workspace_id
        )
        if conns is None:
            return ConnectionsPublic(data=[], count=0)

        server_responses = [
            ConnectionPublic.model_validate(server) for server in conns
        ]
        return ConnectionsPublic(
            data=server_responses, count=len(server_responses)
        )

    async def get_connection(
        self, workspace_id: UUID, conn_id: UUID
    ) -> ConnectionPublic | None:
        server = await self.conn_repository.get_connection(
            workspace_id=workspace_id, conn_id=conn_id
        )
        if server is None:
            return None
        return ConnectionPublic.model_validate(server)

    async def update_connection(
        self,
        workspace_id: UUID,
        conn_id: UUID,
        data: ConnectionUpdate,
    ) -> ConnectionPublic | None:
        if data.config and not self.validate_connection_config(data.config, data.type):
            raise ValueError("Invalid Connection configuration")

        connection_status = None
        connection_status_message = None
        connection_tool_list = None
        connection_tool_schemas = None
        connection_status_updated_at = None

        if data.is_active is True:
            test_params = ConnectionTestParams(
                server_id=conn_id,
                workspace_id=workspace_id,
                updates=data
            )
            test_result = await self._test_connection(test_params)
            connection_status = test_result.status
            connection_status_message = test_result.status_message
            connection_tool_list = test_result.tool_list
            connection_tool_schemas = test_result.tool_schemas
            connection_status_updated_at = test_result.status_updated_at

        # Create combined connection data with status information
        combined_data = ConnectionUpdate(
            name=data.name,
            prefix=data.prefix,
            type=data.type,
            transport_type=data.transport_type,
            config=data.config,
            is_active=data.is_active,
            tool_permissions=data.tool_permissions,
            tool_enabled=data.tool_enabled,
            status=connection_status,
            status_message=connection_status_message,
            tool_list=connection_tool_list,
            tool_schemas=connection_tool_schemas,
            status_updated_at=connection_status_updated_at,
        )

        # Single database operation to update all fields including connection info
        update_params = ConnectionUpdateParams(
            conn_id=conn_id,
            workspace_id=workspace_id,
            connection_data=combined_data
        )
        updated_conn = await self.conn_repository.update_connection(update_params)

        if updated_conn is None:
            return None

        return ConnectionPublic.model_validate(updated_conn)

    async def delete_connection(self, workspace_id: UUID, server_id: UUID) -> bool:
        return await self.conn_repository.delete_connection(
            server_id=server_id, workspace_id=workspace_id
        )

    async def refresh_connection(
        self, workspace_id: UUID, conn_id: UUID
    ) -> ConnectionPublic | None:
        conn = await self.conn_repository.get_connection(
            conn_id=conn_id, workspace_id=workspace_id
        )
        if conn is None:
            return None

        test_params = ConnectionTestParams(
            server_id=conn_id,
            workspace_id=workspace_id,
            updates=None  # Use existing connection config as-is
        )
        test_result = await self._test_connection(test_params)
        connection_status = test_result.status
        connection_status_message = test_result.status_message
        connection_tool_list = test_result.tool_list
        connection_tool_schemas = test_result.tool_schemas
        connection_status_updated_at = test_result.status_updated_at

        # Create combined connection data with status information
        combined_data = ConnectionUpdate(
            name=conn.name,
            prefix=conn.prefix,
            type=conn.type,
            transport_type=conn.transport_type,
            config=conn.config,
            is_active=conn.is_active,
            tool_permissions=conn.tool_permissions,
            tool_enabled=conn.tool_enabled,
            status=connection_status,
            status_message=connection_status_message,
            tool_list=connection_tool_list,
            tool_schemas=connection_tool_schemas,
            status_updated_at=connection_status_updated_at,
        )

        # Single database operation to update all fields including connection info
        update_params = ConnectionUpdateParams(
            conn_id=conn_id,
            workspace_id=workspace_id,
            connection_data=combined_data
        )
        updated_server = await self.conn_repository.update_connection(update_params)

        if updated_server is None:
            return None

        return ConnectionPublic.model_validate(updated_server)

    async def get_builtin_connections(self) -> ConnectionsPublic:
        """Get all available builtin connections from the default
        workspace."""
        try:
            # Query builtin connections from the default workspace
            # (workspace_id = NULL or specific default)
            conns = await self.conn_repository.get_connections_by_type(
                workspace_id=None,  # Default workspace for builtin
                connection_type=ConnectionType.BUILTIN
            )

            if conns is None:
                return ConnectionsPublic(data=[], count=0)

            builtin_responses = [
                ConnectionPublic.model_validate(conn) for conn in conns
            ]
            return ConnectionsPublic(
                data=builtin_responses, count=len(builtin_responses)
            )
        except Exception as e:
            logger.error(f"Error getting builtin connections: {str(e)}")
            return ConnectionsPublic(data=[], count=0)

    async def install_builtin_connection(
        self, workspace_id: UUID, builtin_connection_id: UUID
    ) -> ConnectionPublic | None:
        """Install a builtin connection by cloning it to the user's
        workspace."""
        try:
            # Get the builtin connection from default workspace
            builtin_conn = await self.conn_repository.get_connection(
                workspace_id=None,  # Default workspace
                conn_id=builtin_connection_id
            )

            if not builtin_conn or builtin_conn.type != ConnectionType.BUILTIN:
                raise ValueError("Builtin connection not found")

            # Create a new connection based on the builtin template
            cloned_data = ConnectionBase(
                name=builtin_conn.name,
                prefix=builtin_conn.prefix,
                type=ConnectionType.BUILTIN,
                transport_type=builtin_conn.transport_type,
                config=builtin_conn.config,
                is_active=True,  # Enable by default
                tool_permissions=builtin_conn.tool_permissions or [],
                tool_enabled=[]  # Will be populated after connection test
            )

            # Test the connection to get available tools
            connection_result, tool_schemas = await self.connect(cloned_data)

            if connection_result.status == ConnectionStatus.CONNECTED:
                # Enable all available tools by default
                cloned_data.tool_enabled = connection_result.tool_list or []
                cloned_data.status = ConnectionStatus.CONNECTED
                cloned_data.status_message = connection_result.status_message
                cloned_data.tool_list = connection_result.tool_list
                cloned_data.tool_schemas = tool_schemas
                cloned_data.status_updated_at = datetime.now(timezone.utc)
            else:
                cloned_data.status = ConnectionStatus.ERROR
                cloned_data.status_message = connection_result.status_message
                cloned_data.status_updated_at = datetime.now(timezone.utc)

            # Create the connection in user's workspace
            installed_conn = await self.conn_repository.create_connection(
                workspace_id=workspace_id, conn_data=cloned_data
            )

            return ConnectionPublic.model_validate(installed_conn)

        except Exception as e:
            logger.error(f"Error installing builtin connection {builtin_connection_id}: {str(e)}")
            raise ValueError(f"Failed to install builtin connection: {str(e)}")
