from uuid import UUID

from pydantic import BaseModel


class BuiltInToolResponse(BaseModel):
    name: str
    display_name: str
    description: str


class BuiltInToolUpdate(BaseModel):
    required_permission: bool


class WorkspaceBuiltInToolResponse(BaseModel):
    id: UUID
    required_permission: bool
    builtin_tool: BuiltInToolResponse


class AgentBuiltInToolResponse(BaseModel):
    name: str
    display_name: str
    description: str | None = None
    required_permission: bool


class AgentBuiltInToolsResponse(BaseModel):
    agent_id: UUID
    tools: list[AgentBuiltInToolResponse]
