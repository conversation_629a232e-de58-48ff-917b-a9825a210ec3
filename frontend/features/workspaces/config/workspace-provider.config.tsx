import { CloudProvider } from '@/openapi-ts/gens';
import AwsLogo from '@/public/aws-logo.svg';
import AzureLogo from '@/public/azure-logo.svg';
import GcpLogo from '@/public/gcp-logo.svg';
import { createUtilityConfig } from '@/utils/option-config';

export const WORKSPACE_PROVIDER_CONFIG = createUtilityConfig({
  [CloudProvider.AWS]: {
    label: 'AWS',
    fullLabel: 'Amazon Web Services',
    icon: <AwsLogo className="size-6" />,
  },
  [CloudProvider.GCP]: {
    label: 'GCP',
    fullLabel: 'Google Cloud Platform',
    icon: <GcpLogo className="size-6" />,
  },
  [CloudProvider.AZURE]: {
    label: 'Azure',
    fullLabel: 'Microsoft Azure',
    icon: <AzureLogo className="size-6" />,
    isComingSoon: true,
  },
} satisfies Record<
  CloudProvider,
  {
    label: string;
    fullLabel: string;
    icon: React.ReactNode;
    isComingSoon?: boolean;
  }
>);
