import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { If } from '@/components/ui/common/if';
import { formatFullDateTime } from '@/lib/date-utils';
import { cn } from '@/lib/utils';
import { SchemaWorkspacePublic } from '@/openapi-ts/gens';

import { WORKSPACE_PROVIDER_CONFIG } from '../config/workspace-provider.config';
import { EditWorkspaceForm } from './edit-workspace-form';
import { WorkspaceDeleteConfirm } from './workspace-delete-confirm';

type Props = {
  workspace: SchemaWorkspacePublic;
  isCurrentWorkspace: boolean;
};

export function WorkspaceCard({ workspace, isCurrentWorkspace }: Props) {
  return (
    <Card
      className={cn(
        'relative flex h-full flex-col transition-all duration-200',
        isCurrentWorkspace &&
          'border-primary bg-primary/5 ring-primary/20 shadow-md ring-2',
      )}
    >
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {WORKSPACE_PROVIDER_CONFIG.CONFIG[workspace.provider].icon}
            <span className={cn(isCurrentWorkspace && 'font-semibold')}>
              {workspace.name}
            </span>
            <If condition={isCurrentWorkspace}>
              <Badge
                size="sm"
                className="absolute top-0 left-1/2 -translate-x-1/2 -translate-y-1/2"
              >
                Current
              </Badge>
            </If>
          </div>

          <div className="flex items-center gap-2">
            <If condition={!workspace.is_default}>
              <WorkspaceDeleteConfirm workspaceId={workspace.id}>
                <Button
                  size="sm"
                  variant="destructive"
                  disabled={isCurrentWorkspace}
                >
                  Delete
                </Button>
              </WorkspaceDeleteConfirm>
            </If>
            <EditWorkspaceForm workspace={workspace}>
              <Button size="sm" variant="outlinePrimary">
                Edit
              </Button>
            </EditWorkspaceForm>
          </div>
        </div>
      </CardHeader>
      <CardContent className="flex grow flex-col space-y-2">
        <p className="text-muted-foreground grow text-sm">
          <If
            condition={workspace.description}
            fallback={<span className="italic opacity-70">No description</span>}
          >
            {workspace.description}
          </If>
        </p>
        <If condition={workspace.created_at}>
          {(created_at) => (
            <div className="flex items-center gap-2">
              <div className="flex items-center gap-2">
                <span className="text-muted-foreground text-xs">
                  Created at:
                </span>
                <span className="text-muted-foreground text-xs">
                  {formatFullDateTime(created_at)}
                </span>
              </div>
            </div>
          )}
        </If>
      </CardContent>
    </Card>
  );
}
