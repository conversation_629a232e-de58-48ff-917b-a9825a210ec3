import { api, fetchData } from '@/openapi-ts/openapi-fetch';
import {
  PathsRequestBodyDto,
  PathsRequestQueryDto,
  PathsUpdateRequestBodyDto,
} from '@/openapi-ts/utils.type';

export const workspaceApi = {
  list: (query: PathsRequestQueryDto<'/api/v1/workspaces/'>) =>
    fetchData(api.GET('/api/v1/workspaces/', { query })),

  create: (body: PathsRequestBodyDto<'/api/v1/workspaces/'>) =>
    api.POST('/api/v1/workspaces/', { body }),

  detail: (id: string) => ({
    update: (
      body: PathsUpdateRequestBodyDto<'/api/v1/workspaces/{workspace_id}'>,
    ) =>
      api.PUT('/api/v1/workspaces/{workspace_id}', {
        body,
        params: { path: { workspace_id: id } },
      }),

    delete: () =>
      api.DELETE('/api/v1/workspaces/{workspace_id}', {
        params: { path: { workspace_id: id } },
      }),
  }),
};

export const getAllWorkspaces = () =>
  fetchData(
    workspaceApi.list({
      skip: 0,
      limit: 120,
    }),
  );
