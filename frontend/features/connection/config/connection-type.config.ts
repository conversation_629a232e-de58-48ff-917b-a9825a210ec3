import { ConnectionType } from '@/openapi-ts/gens';
import { createUtilityConfig } from '@/utils/option-config';

export const CONNECTION_TYPE_CONFIG = createUtilityConfig({
  [ConnectionType.builtin]: {
    label: 'Builtin Connection',
    description: 'System-provided connection that can be installed',
    color: 'blue',
  },
  [ConnectionType.mcp]: {
    label: 'MCP Connection',
    description: 'User-created Model Context Protocol connection',
    color: 'green',
  },
} satisfies Record<ConnectionType, { 
  label: string; 
  description: string;
  color: string;
}>);