'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Download, Loader2, Edit, Trash2, X } from 'lucide-react';
import { SchemaConnectionPublic, ConnectionType } from '@/openapi-ts/gens';
import { ReactNode, useCallback } from 'react';

export interface ConnectionActionButtonsProps {
  readonly connection: SchemaConnectionPublic;
  readonly isInstalled: boolean;
  readonly isInstalling?: boolean;
  readonly isUninstalling?: boolean;
  readonly onInstall?: (connectionId: string) => void;
  readonly onUninstall?: (connectionId: string) => void;
  readonly onEdit?: (connection: SchemaConnectionPublic) => void;
  readonly onDelete?: (connection: SchemaConnectionPublic) => void;
  readonly actionButtons?: ReactNode;
}

export function ConnectionActionButtons({
  connection,
  isInstalled,
  isInstalling = false,
  isUninstalling = false,
  onInstall,
  onUninstall,
  onEdit,
  onDelete,
  actionButtons,
}: ConnectionActionButtonsProps) {
  const handleInstall = useCallback(() => {
    onInstall?.(connection.id);
  }, [connection.id, onInstall]);

  const handleUninstall = useCallback(() => {
    onUninstall?.(connection.id);
  }, [connection.id, onUninstall]);

  const handleEdit = useCallback(() => {
    onEdit?.(connection);
  }, [connection, onEdit]);

  const handleDelete = useCallback(() => {
    onDelete?.(connection);
  }, [connection, onDelete]);

  // If custom action buttons are provided, use them
  if (actionButtons) {
    return <>{actionButtons}</>;
  }

  // For uninstalled connections - show Install button
  if (!isInstalled && onInstall) {
    return (
      <Button
        onClick={handleInstall}
        className="flex items-center gap-2"
        size="sm"
        disabled={isInstalling}
        aria-label={`Install ${connection.name} connection`}
      >
        {isInstalling ? (
          <Loader2 className="h-4 w-4 animate-spin" />
        ) : (
          <Download className="h-4 w-4" />
        )}
        {isInstalling ? 'Installing...' : 'Install'}
      </Button>
    );
  }

  // For installed connections
  if (isInstalled) {
    // For MCP connections - show Edit and Delete buttons
    if (connection.type === ConnectionType.mcp) {
      return (
        <div className="flex items-center gap-2">
          {onEdit && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleEdit}
              className="flex items-center gap-2"
              aria-label={`Edit ${connection.name} connection`}
            >
              <Edit className="h-4 w-4" />
              Edit
            </Button>
          )}
          {onDelete && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleDelete}
              className="flex items-center gap-2 text-destructive hover:bg-destructive hover:text-destructive-foreground"
              aria-label={`Delete ${connection.name} connection`}
            >
              <Trash2 className="h-4 w-4" />
              Delete
            </Button>
          )}
        </div>
      );
    }

    // For builtin connections - show Uninstall button
    if (connection.type === ConnectionType.builtin && onUninstall) {
      return (
        <Button
          variant="outline"
          size="sm"
          onClick={handleUninstall}
          className="flex items-center gap-2 text-destructive hover:bg-destructive hover:text-destructive-foreground"
          disabled={isUninstalling}
          aria-label={`Uninstall ${connection.name} connection`}
        >
          {isUninstalling ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <X className="h-4 w-4" />
          )}
          {isUninstalling ? 'Uninstalling...' : 'Uninstall'}
        </Button>
      );
    }
  }

  return null;
}
