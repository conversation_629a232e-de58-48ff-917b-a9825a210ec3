'use client';

import { Switch } from '@/components/ui/switch';
import { SchemaConnectionPublic } from '@/openapi-ts/gens';
import { useCallback } from 'react';
import { cn } from '@/lib/utils';

export interface ConnectionToolItemProps {
  readonly tool: string;
  readonly isEnabled: boolean;
  readonly displayName: string;
  readonly connection: SchemaConnectionPublic;
  readonly isInstalled: boolean;
  readonly onToggle?: (connection: SchemaConnectionPublic, tool: string, enabled: boolean) => void;
  readonly variant: 'installed' | 'mcp';
}

export function ConnectionToolItem({ 
  tool, 
  isEnabled, 
  displayName, 
  connection, 
  isInstalled,
  onToggle, 
  variant 
}: ConnectionToolItemProps) {
  const handleToggle = useCallback(() => {
    if (isInstalled && onToggle) {
      onToggle(connection, tool, !isEnabled);
    }
  }, [connection, tool, isEnabled, onToggle, isInstalled]);

  // For installed builtin connections - clickable buttons
  if (variant === 'installed' && isInstalled) {
    return (
      <button
        key={tool}
        type="button"
        className={cn(
          "flex items-center justify-between p-2 rounded-md border transition-all duration-200",
          "focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2",
          isEnabled
            ? "bg-primary/10 border-primary/20 hover:bg-primary/15"
            : "bg-muted/50 border-border hover:bg-muted"
        )}
        onClick={handleToggle}
        aria-pressed={isEnabled}
        aria-label={`Toggle ${displayName} tool`}
      >
        <div className="flex items-center gap-2 min-w-0 flex-1">
          <span
            className={cn(
              "text-xs font-medium truncate",
              isEnabled ? "text-primary" : "text-muted-foreground"
            )}
          >
            {displayName}
          </span>
        </div>
      </button>
    );
  }

  // For uninstalled builtin connections - disabled display
  if (variant === 'installed' && !isInstalled) {
    return (
      <div
        key={tool}
        className="flex items-center justify-between p-2 rounded-md border bg-muted/30 border-muted cursor-not-allowed opacity-60"
      >
        <div className="flex items-center gap-2 min-w-0 flex-1">
          <span className="text-xs font-medium truncate text-muted-foreground">
            {displayName}
          </span>
        </div>
      </div>
    );
  }

  // For MCP connections - with switch
  return (
    <div
      key={tool}
      className={cn(
        "flex items-center justify-between p-2 rounded-md border transition-colors duration-200",
        isInstalled 
          ? "bg-muted/30 hover:bg-muted/50" 
          : "bg-muted/20 border-muted cursor-not-allowed opacity-60"
      )}
    >
      <div className="flex items-center gap-2 flex-1 min-w-0">
        <span className={cn(
          "text-sm font-medium truncate",
          isInstalled ? "text-foreground" : "text-muted-foreground"
        )}>
          {displayName}
        </span>
      </div>
      {isInstalled && onToggle && (
        <Switch
          checked={isEnabled}
          onCheckedChange={(checked) => onToggle(connection, tool, checked)}
          className="flex-shrink-0 ml-2"
          aria-label={`Toggle ${displayName} tool`}
        />
      )}
    </div>
  );
}
