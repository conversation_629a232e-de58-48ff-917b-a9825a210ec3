import { Button } from '@/components/ui/button';
import { WithTooltip } from '@/components/ui/tooltip';
import { SchemaConnectionPublic } from '@/openapi-ts/gens';
import { RefreshCw } from 'lucide-react';

import { connectionQuery } from '../hooks/connection.query';

interface RefreshConnectionButtonProps {
  connection: SchemaConnectionPublic;
  size?: 'sm' | 'default' | 'lg';
  variant?: 'default' | 'secondary' | 'ghost' | 'outline';
}

export const RefreshConnectionButton = ({
  connection,
  size = 'sm',
  variant = 'ghost',
}: RefreshConnectionButtonProps) => {
  const { mutate: refresh, isPending } = connectionQuery.mutation.useRefresh(
    connection.id,
  );

  return (
    <WithTooltip tooltip="Refresh connection">
      <Button
        size={size}
        variant={variant}
        onClick={() => refresh()}
        disabled={isPending}
      >
        <RefreshCw className={`${isPending ? 'animate-spin' : ''} size-4`} />
      </Button>
    </WithTooltip>
  );
};