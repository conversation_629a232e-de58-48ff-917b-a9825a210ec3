'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { ConnectionType } from '@/openapi-ts/gens';
import { CONNECTION_TYPE_CONFIG } from '../config/connection-type.config';

interface ConnectionHeaderProps {
  readonly type: ConnectionType;
  readonly connectionCount: number;
  readonly onCreateClick?: () => void;
}

export function ConnectionHeader({ type, connectionCount, onCreateClick }: ConnectionHeaderProps) {
  const typeConfig = CONNECTION_TYPE_CONFIG.OBJECT[type];
  const title = `${typeConfig.label}s`;
  const statusText = type === ConnectionType.builtin ? 'available' : 'configured';

  return (
    <div className="flex items-center justify-between">
      <div>
        <h3 className="text-lg font-medium">{title}</h3>
        <p className="text-sm text-muted-foreground">
          {connectionCount} connection{connectionCount !== 1 ? 's' : ''} {statusText}
        </p>
      </div>
      {type === ConnectionType.mcp && onCreateClick && connectionCount > 0 && (
        <Button 
          onClick={onCreateClick}
          className="flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          New Connection
        </Button>
      )}
    </div>
  );
}