'use client';

import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card';
import { SchemaConnectionPublic } from '@/openapi-ts/gens';
import { ReactNode } from 'react';
import { cn } from '@/lib/utils';

// Import new sub-components
import { ConnectionCardHeader } from './connection-card-header';
import { ConnectionActionButtons } from './connection-action-buttons';
import { ConnectionToolsSection } from './connection-tools-section';
import { ConnectionToggle } from './connection-toggle';


export interface ConnectionCardProps {
  readonly connection: SchemaConnectionPublic;
  readonly isInstalled?: boolean;
  readonly installedConnection?: SchemaConnectionPublic | null;
  readonly isInstalling?: boolean;
  readonly className?: string;

  // Action handlers
  readonly onInstall?: (connectionId: string) => void;
  readonly onEdit?: (connection: SchemaConnectionPublic) => void;
  readonly onDelete?: (connection: SchemaConnectionPublic) => void;
  readonly onToolToggle?: (connection: SchemaConnectionPublic, toolName: string, enabled: boolean) => void;
  readonly onConnectionToggle?: (connection: SchemaConnectionPublic, active: boolean) => void;

  // Custom action buttons for different connection types
  readonly actionButtons?: ReactNode;
  readonly showTools?: boolean;
  readonly showConnectionToggle?: boolean;
}







export function ConnectionCard({
  connection,
  isInstalled = false,
  installedConnection = null,
  isInstalling = false,
  className,
  onInstall,
  onEdit,
  onDelete,
  onToolToggle,
  onConnectionToggle,
  actionButtons,
  showTools = true,
  showConnectionToggle = true,
}: ConnectionCardProps) {
  const displayConnection = installedConnection || connection;

  // Always show tools if they exist, regardless of installation status
  const shouldShowTools = showTools && (connection.tool_list?.length || displayConnection.tool_list?.length);

  return (
    <Card className={cn("relative", className)}>
      <CardHeader className="p-0">
        <ConnectionCardHeader
          connection={connection}
          isInstalled={isInstalled}
          isInstalling={isInstalling}
        />
      </CardHeader>

      <CardContent className="space-y-2 px-4 pb-3 pt-0">
        {/* Action Section */}
        <div className="flex items-center justify-between">
          <ConnectionActionButtons
            connection={connection}
            isInstalled={isInstalled}
            isInstalling={isInstalling}
            onInstall={onInstall}
            onEdit={onEdit}
            onDelete={onDelete}
            actionButtons={actionButtons}
          />
        </div>

        {/* Tools Section - Always show if tools exist */}
        {shouldShowTools && (
          <ConnectionToolsSection
            connection={connection}
            displayConnection={displayConnection}
            isInstalled={isInstalled}
            onToolToggle={onToolToggle}
          />
        )}

        {/* Connection Toggle Section - Only for installed connections */}
        <ConnectionToggle
          connection={displayConnection}
          isInstalled={isInstalled}
          onConnectionToggle={onConnectionToggle}
          showConnectionToggle={showConnectionToggle}
        />
      </CardContent>
    </Card>
  );
}
