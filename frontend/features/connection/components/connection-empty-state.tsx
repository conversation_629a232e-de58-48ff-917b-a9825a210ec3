'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { ConnectionType } from '@/openapi-ts/gens';
import { CONNECTION_TYPE_CONFIG } from '../config/connection-type.config';

interface ConnectionEmptyStateProps {
  readonly type: ConnectionType;
  readonly onCreateClick?: () => void;
}

export function ConnectionEmptyState({ type, onCreateClick }: ConnectionEmptyStateProps) {
  const typeConfig = CONNECTION_TYPE_CONFIG.OBJECT[type];
  const emptyMessage = `No ${typeConfig.label.toLowerCase()}s ${type === ConnectionType.builtin ? 'available' : 'configured'}`;
  const emptyDescription = typeConfig.description;

  return (
    <div className="flex flex-col items-center justify-center h-64 border-2 border-dashed border-muted rounded-lg text-center space-y-4">
      <h4 className="text-lg font-medium text-muted-foreground">{emptyMessage}</h4>
      <p className="text-sm text-muted-foreground max-w-md">{emptyDescription}</p>
      {type === ConnectionType.mcp && onCreateClick && (
        <Button
          onClick={onCreateClick}
          className="flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          New Connection
        </Button>
      )}
    </div>
  );
}
