'use client';

import { CardDescription, CardTitle } from '@/components/ui/card';
import { Server } from 'lucide-react';
import { SchemaConnectionPublic } from '@/openapi-ts/gens';
import { ConnectionStatusBadge } from './connection-status-badge';

export interface ConnectionCardHeaderProps {
  readonly connection: SchemaConnectionPublic;
  readonly isInstalled?: boolean;
  readonly isInstalling?: boolean;
}

export function ConnectionCardHeader({ 
  connection, 
  isInstalled = false, 
  isInstalling = false 
}: ConnectionCardHeaderProps) {
  return (
    <div className="flex items-center justify-between px-4 py-3">
      <div className="flex items-center gap-3">
        <div className="p-1.5 rounded-md bg-muted">
          <Server className="h-4 w-4" aria-hidden="true" />
        </div>
        <div>
          <CardTitle className="text-sm font-medium">{connection.name}</CardTitle>
          <div className="flex items-center gap-2">
            <CardDescription className="text-xs">
              <code className="bg-muted px-1 rounded">{connection.prefix}</code>
            </CardDescription>
          </div>
        </div>
      </div>
      <div className="flex items-center gap-2">
        <ConnectionStatusBadge 
          isInstalled={isInstalled} 
          isInstalling={isInstalling} 
        />
      </div>
    </div>
  );
}
