'use client';

import { Wrench } from 'lucide-react';
import { SchemaConnectionPublic, ConnectionType } from '@/openapi-ts/gens';
import { ConnectionToolItem } from './connection-tool-item';
import { useMemo, useCallback } from 'react';
import { cn } from '@/lib/utils';

export interface ConnectionToolsSectionProps {
  readonly connection: SchemaConnectionPublic;
  readonly displayConnection: SchemaConnectionPublic;
  readonly isInstalled: boolean;
  readonly onToolToggle?: (connection: SchemaConnectionPublic, toolName: string, enabled: boolean) => void;
}

export function ConnectionToolsSection({
  connection,
  displayConnection,
  isInstalled,
  onToolToggle,
}: ConnectionToolsSectionProps) {
  const getToolDisplayName = useCallback((tool: string) => {
    return tool.split('__').pop() || tool;
  }, []);

  const variant = useMemo(() => {
    return connection.type === ConnectionType.mcp ? 'mcp' : 'installed';
  }, [connection.type]);

  // Always show tools if they exist, regardless of installation status
  const toolItems = useMemo(() => {
    // For uninstalled builtin connections, show tools from the connection definition
    // For installed connections, show tools from the installed connection
    const sourceConnection = isInstalled ? displayConnection : connection;
    
    if (!sourceConnection.tool_list?.length) return [];

    return sourceConnection.tool_list.map((tool) => {
      // For enabled state, only check if installed
      const isEnabled = isInstalled 
        ? (displayConnection.tool_enabled?.includes(tool) || false)
        : false;
      const displayName = getToolDisplayName(tool);

      return (
        <ConnectionToolItem
          key={tool}
          tool={tool}
          isEnabled={isEnabled}
          displayName={displayName}
          connection={isInstalled ? displayConnection : connection}
          isInstalled={isInstalled}
          onToggle={onToolToggle}
          variant={variant}
        />
      );
    });
  }, [connection, displayConnection, isInstalled, variant, onToolToggle, getToolDisplayName]);

  if (!toolItems.length) return null;

  return (
    <div className="space-y-3 pt-3 border-t">
      <h4 className="text-sm font-medium flex items-center gap-2">
        <Wrench className="h-4 w-4" />
        Available Tools
        {!isInstalled && (
          <span className="text-xs text-muted-foreground font-normal">
            (Install to enable)
          </span>
        )}
      </h4>
      <div
        className={cn(
          "grid gap-2",
          variant === 'installed' ? "grid-cols-2 sm:grid-cols-3" : "grid-cols-1"
        )}
      >
        {toolItems}
      </div>
    </div>
  );
}
