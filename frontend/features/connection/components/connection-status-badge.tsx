'use client';

import { Badge } from '@/components/ui/badge';
import { CheckCircle, Clock } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface ConnectionStatusBadgeProps {
  readonly isInstalled: boolean;
  readonly isInstalling?: boolean;
  readonly className?: string;
}

export function ConnectionStatusBadge({ 
  isInstalled, 
  isInstalling = false, 
  className 
}: ConnectionStatusBadgeProps) {
  if (isInstalling) {
    return (
      <Badge 
        variant="outline" 
        className={cn(
          "flex items-center gap-1 border-yellow-600 text-yellow-600 text-xs py-0 px-2 h-6",
          className
        )}
      >
        <Clock className="h-3 w-3" />
        Installing...
      </Badge>
    );
  }

  if (isInstalled) {
    return (
      <Badge 
        variant="outline" 
        className={cn(
          "flex items-center gap-1 border-green-600 text-green-600 text-xs py-0 px-2 h-6",
          className
        )}
      >
        <CheckCircle className="h-3 w-3" />
        Installed
      </Badge>
    );
  }

  return null;
}
