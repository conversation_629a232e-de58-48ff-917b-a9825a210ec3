'use client';

import { SchemaConnectionPublic, ConnectionType } from '@/openapi-ts/gens';
import { ConnectionCard } from './connection-card';

interface ConnectionGridProps {
  readonly connections: SchemaConnectionPublic[];
  readonly type: ConnectionType;
  readonly installedBuiltinIds: Set<string>;
  readonly isInstalling: boolean;
  readonly getInstalledConnection: (connection: SchemaConnectionPublic) => SchemaConnectionPublic | null;
  readonly onInstall?: (connectionId: string) => void;
  readonly onEdit?: (connection: SchemaConnectionPublic) => void;
  readonly onDelete?: (connection: SchemaConnectionPublic) => void;
  readonly onToolToggle: (connection: SchemaConnectionPublic, toolName: string, enabled: boolean) => void;
  readonly onConnectionToggle: (connection: SchemaConnectionPublic, active: boolean) => void;
}

export function ConnectionGrid({
  connections,
  type,
  installedBuiltinIds,
  isInstalling,
  getInstalledConnection,
  onInstall,
  onEdit,
  onDelete,
  onToolToggle,
  onConnectionToggle,
}: ConnectionGridProps) {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 auto-rows-min">
      {connections.map((connection) => {
        const isInstalled = type === ConnectionType.builtin ? installedBuiltinIds.has(connection.id) : false;
        const installedConnection = type === ConnectionType.builtin && isInstalled ? getInstalledConnection(connection) : null;

        return (
          <ConnectionCard
            key={connection.id}
            connection={connection}
            isInstalled={isInstalled}
            installedConnection={installedConnection}
            isInstalling={isInstalling}
            onInstall={type === ConnectionType.builtin ? onInstall : undefined}
            onEdit={type === ConnectionType.mcp ? onEdit : undefined}
            onDelete={type === ConnectionType.mcp ? onDelete : undefined}
            onToolToggle={onToolToggle}
            onConnectionToggle={onConnectionToggle}
            showTools={true}
            showConnectionToggle={true}
          />
        );
      })}
    </div>
  );
}
