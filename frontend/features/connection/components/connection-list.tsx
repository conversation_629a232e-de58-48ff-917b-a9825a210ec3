'use client';

import { ConnectionType } from '@/openapi-ts/gens';
import { Loader2 } from 'lucide-react';
import { useState } from 'react';

import { ConnectionHeader } from './connection-header';
import { ConnectionEmptyState } from './connection-empty-state';
import { ConnectionGrid } from './connection-grid';
import { CreateConnectionDialog } from './create-connection-dialog';
import { useConnectionList } from '../hooks/use-connection-list';

interface ConnectionListProps {
  readonly className?: string;
  readonly type: ConnectionType;
}

export function ConnectionList({ className, type }: ConnectionListProps) {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  
  const {
    connections,
    installedBuiltinIds,
    getInstalledConnection,
    isInstalling,
    isLoading,
    error,
    handleInstall,
    handleEdit,
    handleDelete,
    handleToolToggle,
    handleConnectionToggle,
  } = useConnectionList({ type });

  const handleCreateClick = () => setIsCreateDialogOpen(true);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center space-y-2">
          <h4 className="text-lg font-medium text-destructive">Error loading connections</h4>
          <p className="text-sm text-muted-foreground">
            {error instanceof Error ? error.message : 'An unexpected error occurred'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      <ConnectionHeader 
        type={type}
        connectionCount={connections.length}
        onCreateClick={handleCreateClick}
      />

      {connections.length > 0 ? (
        <ConnectionGrid
          connections={connections}
          type={type}
          installedBuiltinIds={installedBuiltinIds}
          isInstalling={isInstalling}
          getInstalledConnection={getInstalledConnection}
          onInstall={handleInstall}
          onEdit={handleEdit}
          onDelete={handleDelete}
          onToolToggle={handleToolToggle}
          onConnectionToggle={handleConnectionToggle}
        />
      ) : (
        <ConnectionEmptyState 
          type={type}
          onCreateClick={handleCreateClick}
        />
      )}

      {/* Create Connection Dialog - Only for MCP */}
      {type === ConnectionType.mcp && (
        <CreateConnectionDialog 
          open={isCreateDialogOpen}
          onOpenChange={setIsCreateDialogOpen}
        />
      )}
    </div>
  );
}