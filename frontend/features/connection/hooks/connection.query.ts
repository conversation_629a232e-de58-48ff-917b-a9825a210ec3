import { useCallback, useMemo } from 'react';
import { createQueryKeys } from '@lukemorales/query-key-factory';
import {
  keepPreviousData,
  useMutation,
  useQuery,
  useQueryClient,
} from '@tanstack/react-query';
import { toast } from 'sonner';
import { SchemaConnectionCreate, SchemaConnectionPublic, SchemaConnectionUpdate, ConnectionTransport, ConnectionType, ConnectionStatus } from '@/openapi-ts/gens';
import { useAgentConnectors } from '@/hooks/use-agent-connectors';

import { ConnectionQueryParams } from '../models/connection.type';
import { connectionApi } from '../services/connection.api';

// Type definitions for backward compatibility with agents page
export interface DisplayConnectionInfo {
  id: string;
  name: string;
  prefix: string;
  type: string;
  is_active: boolean;
  status: string;
  status_message?: string;
  tool_list: string[];
  tool_enabled: string[];
  tool_permissions: string[];
  tool_schemas: Record<string, any>;
  config: Record<string, any>;
  
  created_at: string;
  updated_at: string;
}

const connectionQueryKeys = createQueryKeys('connections', {
  list: (params?: ConnectionQueryParams) => ({
    queryKey: [params],
    queryFn: () => connectionApi.list(params),
  }),
  builtin: () => ({
    queryKey: ['builtin'],
    queryFn: () => connectionApi.listBuiltin(),
  }),
  detail: (connectionId: string) => ({
    queryKey: [connectionId],
    queryFn: () => connectionApi.detail(connectionId).get(),
  }),
});

const useList = (params?: ConnectionQueryParams) => {
  return useQuery({
    ...connectionQueryKeys.list(params),
    placeholderData: keepPreviousData,
  });
};

const useBuiltinList = () => {
  return useQuery({
    ...connectionQueryKeys.builtin(),
  });
};

const useDetail = (connectionId: string) => {
  return useQuery({
    ...connectionQueryKeys.detail(connectionId),
    enabled: !!connectionId,
  });
};

const useCreate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: connectionApi.create,
    onSuccess: () => {
      toast.success('Connection created successfully');
      queryClient.invalidateQueries({ queryKey: connectionQueryKeys.list._def });
    },
    onError: (error: any) => {
      toast.error(error?.message || 'Failed to create connection');
    },
  });
};

const useUpdate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: SchemaConnectionUpdate }) => 
      connectionApi.detail(id).update(data),
    onSuccess: (_, { id }) => {
      toast.success('Connection updated successfully');
      queryClient.invalidateQueries({ queryKey: connectionQueryKeys.list._def });
      queryClient.invalidateQueries({ queryKey: connectionQueryKeys.detail(id).queryKey });
    },
    onError: (error: any) => {
      toast.error(error?.message || 'Failed to update connection');
    },
  });
};

const useDelete = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (connectionId: string) => connectionApi.detail(connectionId).delete(),
    onSuccess: () => {
      toast.success('Connection deleted successfully');
      queryClient.invalidateQueries({ queryKey: connectionQueryKeys.list._def });
    },
    onError: (error: any) => {
      toast.error(error?.message || 'Failed to delete connection');
    },
  });
};

const useRefresh = (connectionId: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: connectionApi.detail(connectionId).refresh,
    onSuccess: () => {
      toast.success('Connection refreshed successfully');
      queryClient.invalidateQueries({ queryKey: connectionQueryKeys.detail(connectionId).queryKey });
      queryClient.invalidateQueries({ queryKey: connectionQueryKeys.list._def });
    },
    onError: (error: any) => {
      toast.error(error?.message || 'Failed to refresh connection');
    },
  });
};

const useInstallBuiltin = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: connectionApi.installBuiltin,
    onSuccess: () => {
      toast.success('Builtin connection installed successfully');
      queryClient.invalidateQueries({ queryKey: connectionQueryKeys.list._def });
    },
    onError: (error: any) => {
      toast.error(error?.message || 'Failed to install builtin connection');
    },
  });
};

// Legacy compatibility hook for agents page
export const useConnections = () => {
  const queryClient = useQueryClient();
  
  // Get agent connector data to create the agent-MCP server mapping
  const { data: agentConnectors } = useAgentConnectors();
  
  // Fetch all connections using the new connection API
  const {
    data: connectionsResponse,
    isLoading,
    error,
    refetch: refreshAllConnections,
  } = useList();

  // Extract connections from response
  const connections = connectionsResponse?.data || [];

  // Convert connections to display format for compatibility
  const servers = useMemo(() => connections.map((connection: SchemaConnectionPublic): DisplayConnectionInfo => ({
    id: connection.id,
    name: connection.name,
    prefix: connection.prefix,
    type: connection.type,
    is_active: connection.is_active,
    status: connection.status || 'error',
    status_message: connection.status_message || '',
    tool_list: connection.tool_list || [],
    tool_enabled: connection.tool_enabled || [],
    tool_permissions: connection.tool_permissions || [],
    tool_schemas: connection.tool_schemas || {},
    config: connection.config || {},
    created_at: connection.created_at,
    updated_at: connection.updated_at,
  })), [connections]);

  // Create a map of server names to server details
  const serverMap = useMemo(() => 
    servers.reduce<Record<string, DisplayConnectionInfo>>(
      (acc, server) => {
        acc[server.name] = server;
        return acc;
      },
      {},
    ), [servers]
  );

  // Create a map of agent ID to their MCP servers with full server details
  const agentMcpServerMap = useMemo(() =>
    agentConnectors?.reduce<Record<string, DisplayConnectionInfo[]>>(
      (acc, connector) => {
        if (connector && serverMap) {
          acc[connector.agent_id] = connector.mcp_servers
            .map((serverName) => serverMap[serverName])
            .filter(Boolean); // Filter out any undefined servers
        }
        return acc;
      },
      {},
    ) || {}, [agentConnectors, serverMap]
  );

  // Create mutations
  const createMutation = useMutation({
    mutationFn: connectionApi.create,
    onSuccess: () => {
      toast.success('Server added successfully');
      queryClient.invalidateQueries({ queryKey: ['connections'] });
    },
    onError: () => {
      toast.error('Failed to add server');
    },
  });

  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: SchemaConnectionUpdate }) => 
      connectionApi.detail(id).update(data),
    onSuccess: () => {
      toast.success('Server updated successfully');
      queryClient.invalidateQueries({ queryKey: ['connections'] });
    },
    onError: () => {
      toast.error('Failed to update server');
    },
  });

  const deleteMutation = useMutation({
    mutationFn: (id: string) => connectionApi.detail(id).delete(),
    onSuccess: () => {
      toast.success('Server deleted successfully');
      queryClient.invalidateQueries({ queryKey: ['connections'] });
    },
    onError: () => {
      toast.error('Failed to delete server');
    },
  });

  const refreshMutation = useMutation({
    mutationFn: (id: string) => connectionApi.detail(id).refresh(),
    onSuccess: () => {
      toast.success('Server refreshed successfully');
      queryClient.invalidateQueries({ queryKey: ['connections'] });
    },
    onError: () => {
      toast.error('Failed to refresh server');
    },
  });

  // Wrapper functions to maintain the original API
  const addServer = useCallback(
    async (server: any) => {
      const connectionData: SchemaConnectionCreate = {
        name: server.name,
        prefix: server.prefix,
        type: ConnectionType.mcp, // MCP connections created by users
        transport_type: server.transport_type || ConnectionTransport.streamable_http,
        is_active: server.is_active,
        tool_enabled: server.tool_enabled,
        tool_permissions: server.tool_permissions,
        config: server.config,
        tool_list: server.tool_list || [],
        tool_schemas: server.tool_schemas || {},
        status: server.status || ConnectionStatus.error,
        status_message: server.status_message || '',
        status_updated_at: server.status_updated_at || new Date().toISOString(),
      };
      return new Promise<void>((resolve, reject) => {
        createMutation.mutate(connectionData, {
          onSuccess: () => resolve(),
          onError: (err: Error) => reject(err),
        });
      });
    },
    [createMutation],
  );

  const updateServer = useCallback(
    async (serverId: string, server: any) => {
      const connectionData: SchemaConnectionUpdate = {
        name: server.name,
        prefix: server.prefix,
        type: ConnectionType.mcp, // MCP connections created by users
        transport_type: server.transport_type || ConnectionTransport.streamable_http,
        is_active: server.is_active,
        tool_enabled: server.tool_enabled,
        tool_permissions: server.tool_permissions,
        config: server.config,
        tool_list: server.tool_list || [],
        tool_schemas: server.tool_schemas || {},
        status: server.status || 'disconnected',
        status_message: server.status_message || '',
        status_updated_at: server.status_updated_at || new Date().toISOString(),
      };
      return new Promise<void>((resolve, reject) => {
        updateMutation.mutate({ id: serverId, data: connectionData }, {
          onSuccess: () => resolve(),
          onError: (err: Error) => reject(err),
        });
      });
    },
    [updateMutation],
  );

  const deleteServer = useCallback(
    async (serverId: string) => {
      return new Promise<void>((resolve, reject) => {
        deleteMutation.mutate(serverId, {
          onSuccess: () => resolve(),
          onError: (err: Error) => reject(err),
        });
      });
    },
    [deleteMutation],
  );

  const refreshServer = useCallback(
    async (serverId: string) => {
      return new Promise<void>((resolve, reject) => {
        refreshMutation.mutate(serverId, {
          onSuccess: () => resolve(),
          onError: (err: Error) => reject(err),
        });
      });
    },
    [refreshMutation],
  );

  const refreshAllServers = useCallback(async () => {
    await refreshAllConnections();
  }, [refreshAllConnections]);

  const isRefreshing = useCallback(
    () => {
      return refreshMutation.isPending;
    },
    [refreshMutation.isPending],
  );

  return {
    servers,
    isLoading: isLoading || createMutation.isPending || updateMutation.isPending || deleteMutation.isPending,
    error: error,
    agentMcpServerMap,
    addServer,
    updateServer,
    deleteServer,
    refreshServer,
    refreshAllServers,
    isRefreshing,
  };
};

export const connectionQuery = {
  query: {
    useList,
    useBuiltinList,
    useDetail,
  },
  mutation: {
    useCreate,
    useUpdate,
    useDelete,
    useRefresh,
    useInstallBuiltin,
  },
};