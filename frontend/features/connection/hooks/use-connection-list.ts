import { useMemo } from 'react';
import { SchemaConnectionPublic, ConnectionType, SchemaConnectionUpdate } from '@/openapi-ts/gens';
import { connectionQuery } from './connection.query';

interface UseConnectionListProps {
  type: ConnectionType;
}

export function useConnectionList({ type }: UseConnectionListProps) {
  // Fetch connections based on type
  const { data: builtinResponse, isLoading: isLoadingBuiltin, error: builtinError } = connectionQuery.query.useBuiltinList();
  const { data: userConnectionsResponse, isLoading: isLoadingUser, error: userError } = connectionQuery.query.useList();
  
  // Handle errors and ensure we have valid arrays
  const builtinConnections = Array.isArray(builtinResponse?.data) ? builtinResponse.data : [];
  const userConnections = Array.isArray(userConnectionsResponse?.data) ? userConnectionsResponse.data : [];
  
  // Mutations
  const { mutate: installBuiltin, isPending: isInstalling } = connectionQuery.mutation.useInstallBuiltin();
  const { mutate: uninstallBuiltin, isPending: isUninstalling } = connectionQuery.mutation.useUninstallBuiltin();
  const { mutate: updateConnection } = connectionQuery.mutation.useUpdate();
  const { mutate: deleteConnection } = connectionQuery.mutation.useDelete();

  // Filter connections based on type
  const connections = useMemo(() => {
    if (builtinError || userError) {
      console.error('Connection API errors:', { builtinError, userError });
      return [];
    }
    
    if (type === ConnectionType.builtin) {
      return builtinConnections;
    } else {
      return userConnections.filter((conn) => conn.type === ConnectionType.mcp);
    }
  }, [type, builtinConnections, userConnections, builtinError, userError]);
  
  // For builtin connections, track which ones are installed
  const installedBuiltinIds = useMemo(() => {
    if (type !== ConnectionType.builtin) return new Set<string>();
    
    const installed = new Set<string>();
    userConnections.forEach((conn) => {
      const matchingBuiltin = builtinConnections.find(
        (builtin) => builtin.name === conn.name && builtin.type === ConnectionType.builtin
      );
      if (matchingBuiltin) {
        installed.add(matchingBuiltin.id);
      }
    });
    return installed;
  }, [type, builtinConnections, userConnections]);

  // Get the installed connection data for a builtin
  const getInstalledConnection = (builtinConnection: SchemaConnectionPublic): SchemaConnectionPublic | null => {
    if (type !== ConnectionType.builtin) return null;
    return userConnections.find(
      (conn) => conn.name === builtinConnection.name && conn.type === ConnectionType.builtin
    ) || null;
  };

  // Event handlers
  const handleInstall = (connectionId: string) => {
    installBuiltin(connectionId);
  };

  const handleUninstall = (connectionId: string) => {
    // For builtin connections, we need to find the installed connection and delete it
    const installedConnection = userConnections.find(
      (conn) => {
        const matchingBuiltin = builtinConnections.find(
          (builtin) => builtin.id === connectionId
        );
        return matchingBuiltin && conn.name === matchingBuiltin.name && conn.type === ConnectionType.builtin;
      }
    );

    if (installedConnection) {
      uninstallBuiltin(installedConnection.id);
    }
  };

  const handleEdit = (connection: SchemaConnectionPublic) => {
    console.log('Edit connection:', { connectionId: connection.id });
    // Implement edit logic, e.g., open a dialog
  };

  const handleDelete = (connection: SchemaConnectionPublic) => {
    deleteConnection(connection.id);
  };

  const handleToolToggle = (connection: SchemaConnectionPublic, toolName: string, enabled: boolean) => {
    const currentEnabledTools = connection.tool_enabled || [];
    const newEnabledTools = enabled
      ? [...currentEnabledTools, toolName]
      : currentEnabledTools.filter((t) => t !== toolName);

    const updateData: SchemaConnectionUpdate = {
      tool_enabled: newEnabledTools,
    };
    
    updateConnection({ id: connection.id, data: updateData });
  };

  const handleConnectionToggle = (connection: SchemaConnectionPublic, active: boolean) => {
    const updateData: SchemaConnectionUpdate = { is_active: active };
    updateConnection({ id: connection.id, data: updateData });
  };

  const isLoading = type === ConnectionType.builtin ? (isLoadingBuiltin || isLoadingUser) : isLoadingUser;
  const error = type === ConnectionType.builtin ? (builtinError || userError) : userError;

  return {
    connections,
    installedBuiltinIds,
    getInstalledConnection,
    isInstalling,
    isUninstalling,
    isLoading,
    error,
    handleInstall,
    handleUninstall,
    handleEdit,
    handleDelete,
    handleToolToggle,
    handleConnectionToggle,
  };
}