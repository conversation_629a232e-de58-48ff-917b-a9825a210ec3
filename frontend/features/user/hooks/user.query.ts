import { useRouter } from 'next/navigation';

import { clearAllCache } from '@/utils/server/server-action';
import {
  WithPaginationDefaults,
  handleSkipOfPagination,
} from '@/utils/with-pagination-defaults';
import { createQueryKeys } from '@lukemorales/query-key-factory';
import {
  keepPreviousData,
  useMutation,
  useQuery,
  useQueryClient,
} from '@tanstack/react-query';
import { toast } from 'sonner';

import { UserQueryParams } from '../models/user.type';
import { userApi } from '../services/user.api';

const userQueryKeys = createQueryKeys('users', {
  list: (params: WithPaginationDefaults<UserQueryParams>) => ({
    queryKey: [params],
    queryFn: () => userApi.list(handleSkipOfPagination(params)),
  }),
});

const useUpdateMe = () => {
  const router = useRouter();

  return useMutation({
    mutationFn: userApi.updateMe,
    onSuccess: () => {
      toast.success('User updated successfully');
      clearAllCache();
      router.refresh();
    },
  });
};

const useChangePassword = () => {
  return useMutation({
    mutationFn: userApi.changePassword,
    onSuccess: () => {
      toast.success('Password changed successfully');
    },
  });
};

const useList = (params: WithPaginationDefaults<UserQueryParams>) => {
  return useQuery({
    ...userQueryKeys.list(params),
    placeholderData: keepPreviousData,
  });
};

const useCreate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: userApi.create,
    onSuccess: () => {
      toast.success('User created successfully');
      queryClient.invalidateQueries({ queryKey: userQueryKeys.list._def });
    },
  });
};

export const userQuery = {
  query: {
    useList,
  },
  mutation: {
    useUpdateMe,
    useChangePassword,
    useCreate,
  },
};
