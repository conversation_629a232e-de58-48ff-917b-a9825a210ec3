'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { If } from '@/components/ui/common/if';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { PasswordInput } from '@/components/ui/password-input';
import { zodResolver } from '@hookform/resolvers/zod';
import { CheckIcon } from 'lucide-react';
import { SubmitHandler, useForm, useWatch } from 'react-hook-form';

import { userQuery } from '../hooks/user.query';
import {
  type ChangePasswordSchema,
  changePasswordSchema,
} from '../schema/profile.schema';

export function ChangePasswordCard() {
  const changePasswordMutation = userQuery.mutation.useChangePassword();

  const form = useForm<ChangePasswordSchema>({
    resolver: zodResolver(changePasswordSchema),
    defaultValues: {
      current_password: '',
      new_password: '',
      confirm_password: '',
    },
  });

  const {
    formState: { isSubmitting, errors },
    control,
    reset,
    handleSubmit,
  } = form;

  const [newPassword, confirmPassword] = useWatch({
    control,
    name: ['new_password', 'confirm_password'],
  });

  const isNotLongEnough = newPassword.length < 8;
  const isPasswordMatch = Boolean(
    confirmPassword && newPassword === confirmPassword,
  );

  const onSubmit: SubmitHandler<ChangePasswordSchema> = (data) => {
    changePasswordMutation.mutate(
      {
        current_password: data.current_password,
        new_password: data.new_password,
      },
      {
        onSuccess: () => reset(),
      },
    );
  };

  return (
    <Form {...form}>
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <Card className="max-w-md">
          <CardHeader>
            <CardTitle>Change Password</CardTitle>
            <CardDescription>
              Update your password to keep your account secure.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <FormField
                control={control}
                name="current_password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel required>Current Password</FormLabel>
                    <FormControl>
                      <PasswordInput
                        {...field}
                        placeholder="Enter your current password"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={control}
                name="new_password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel required>New Password</FormLabel>
                    <FormControl>
                      <PasswordInput
                        {...field}
                        placeholder="Enter your new password"
                      />
                    </FormControl>
                    <FormMessage />
                    <If
                      condition={
                        !errors.new_password &&
                        !isNotLongEnough &&
                        isPasswordMatch
                      }
                      fallback={
                        <If
                          condition={isNotLongEnough}
                          fallback={
                            <If condition={!isPasswordMatch}>
                              <FormMessage className="text-destructive">
                                Passwords do not match
                              </FormMessage>
                            </If>
                          }
                        >
                          <FormMessage className="text-destructive">
                            Password must be at least 8 characters long
                          </FormMessage>
                        </If>
                      }
                    >
                      <FormMessage className="text-success flex items-center gap-2">
                        Password matches
                        <CheckIcon className="size-4" />
                      </FormMessage>
                    </If>
                  </FormItem>
                )}
              />

              <FormField
                control={control}
                name="confirm_password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel required>Confirm New Password</FormLabel>
                    <FormControl>
                      <PasswordInput
                        {...field}
                        placeholder="Confirm your new password"
                      />
                    </FormControl>
                    <FormMessage />
                    <If
                      condition={
                        !errors.confirm_password &&
                        !isNotLongEnough &&
                        isPasswordMatch
                      }
                    >
                      <FormMessage className="text-success flex items-center gap-2">
                        Password matches
                        <CheckIcon className="size-4" />
                      </FormMessage>
                    </If>
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
          <CardFooter>
            <Button
              disabled={
                isSubmitting ||
                changePasswordMutation.isPending ||
                !isPasswordMatch ||
                isNotLongEnough
              }
              className="ml-auto"
              type="submit"
            >
              {isSubmitting || changePasswordMutation.isPending
                ? 'Changing...'
                : 'Change Password'}
            </Button>
          </CardFooter>
        </Card>
      </form>
    </Form>
  );
}
