'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { UpdateWrapper } from '@/components/ui/common/update-wrapper';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { differentObject } from '@/utils/object';
import { zodResolver } from '@hookform/resolvers/zod';
import { flow, pick } from 'lodash';
import { SubmitHandler, useForm } from 'react-hook-form';

import { userQuery } from '../hooks/user.query';
import { useUserContext } from '../provider/user-provider';
import { ProfileSchema, profileSchema } from '../schema/profile.schema';

export function ProfileCard() {
  const { user } = useUserContext();

  const defaultValues: ProfileSchema = pick(user, ['full_name', 'email']);

  const form = useForm<ProfileSchema>({
    resolver: zod<PERSON><PERSON>olver(profileSchema),
    defaultValues,
  });

  const { control, handleSubmit } = form;

  const { mutate, isPending } = userQuery.mutation.useUpdateMe();

  const onSubmit: SubmitHandler<ProfileSchema> = flow(
    differentObject(defaultValues),
    mutate,
  );

  return (
    <Form {...form}>
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <Card className="max-w-md">
          <CardHeader>
            <CardTitle>Profile Information</CardTitle>
            <CardDescription>
              Update your personal information and email address.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <FormField
                control={control}
                name="full_name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel required>Full Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Your name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input placeholder="Your email" {...field} disabled />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
          <CardFooter>
            <Button disabled={isPending} className="ml-auto" type="submit">
              <UpdateWrapper isPending={isPending}>Save Changes</UpdateWrapper>
            </Button>
          </CardFooter>
        </Card>
      </form>
    </Form>
  );
}
