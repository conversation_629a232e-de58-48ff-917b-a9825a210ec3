import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

import { builtinToolsApi } from '../services/builtin-tools.api';

export const BUILTIN_TOOLS_QUERY_KEY = 'workspace-builtin-tools';

/**
 * Hook to fetch all workspace built-in tools
 */
export function useBuiltinToolsQuery() {
  return useQuery({
    queryKey: [BUILTIN_TOOLS_QUERY_KEY],
    queryFn: () => builtinToolsApi.list(),
  });
}

/**
 * Hook to update a built-in tool's permission requirement
 */
export function useUpdateToolPermission() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      toolId,
      requiredPermission,
    }: {
      toolId: string;
      requiredPermission: boolean;
    }) => {
      return await builtinToolsApi.detail(toolId).updatePermission({ requiredPermission });
    },
    onSuccess: (_, { requiredPermission }) => {
      queryClient.invalidateQueries({ queryKey: [BUILTIN_TOOLS_QUERY_KEY] });
      toast.success(
        requiredPermission
          ? 'Permission requirement enabled'
          : 'Permission requirement disabled',
      );
    },
    onError: () => {
      toast.error('Failed to update permission requirement');
    },
  });
}
