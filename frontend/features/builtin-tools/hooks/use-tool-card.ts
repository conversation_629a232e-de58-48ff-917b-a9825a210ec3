'use client';

import { useState } from 'react';

import { SchemaWorkspaceBuiltInToolResponse } from '@/openapi-ts/gens';

import { useUpdateToolPermission } from './builtin-tools.query';

export function useToolCard(tool: SchemaWorkspaceBuiltInToolResponse) {
  const { mutate: togglePermission, isPending: isPermissionPending } = useUpdateToolPermission();
  const [requiresPermission, setRequiresPermission] = useState(tool.required_permission);

  // Display properties
  const displayName = tool.builtin_tool.display_name;
  const description = tool.builtin_tool.description;

  // Handle toggling permission requirement
  const handleTogglePermission = () => {
    if (!tool.id) return;

    const newRequiresPermission = !requiresPermission;
    setRequiresPermission(newRequiresPermission);

    togglePermission({
      toolId: tool.id,
      requiredPermission: newRequiresPermission,
    }, {
      onError: () => {
        setRequiresPermission(!newRequiresPermission); // Revert on error
      }
    });
  };

  return {
    // State
    isPermissionPending,
    requiresPermission,

    // Display data
    displayName,
    description,

    // Actions
    handleTogglePermission,
  };
}
