'use client';

import { PageSkeleton } from '@/components/ui/common/page';
import { ToolSummary } from './tool-summary';
import { useBuiltinToolsQuery } from '../hooks/builtin-tools.query';
import { ToolCardContainer } from './tool-card-container';

export function ToolList() {

  // Fetch builtin tools using the query hook
  const { data: builtinTools, isLoading: isBuiltinToolsLoading } = useBuiltinToolsQuery();

  if (builtinTools) {
    return (
      <div className="flex flex-col h-full overflow-y-auto">
        <ToolSummary totalBuiltinCount={builtinTools.length} isLoading={isBuiltinToolsLoading} />
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-6">
          {builtinTools.map((tool) => (
            <ToolCardContainer
              key={tool.id}
              tool={tool}
              isBuiltin={true}
              attachedAgents={[]}
            />
          ))}
        </div>
      </div>
    );
  }

  if (isBuiltinToolsLoading) {
    return <PageSkeleton />;
  }
}
