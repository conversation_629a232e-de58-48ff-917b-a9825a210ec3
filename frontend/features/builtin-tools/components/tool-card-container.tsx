'use client';

import React from 'react';

import { SchemaWorkspaceBuiltInToolResponse } from '@/openapi-ts/gens';

import { useToolCard } from '../hooks/use-tool-card';
import { ToolCard, ToolCardUIProps } from './tool-card';

export interface ToolCardContainerProps {
  tool: SchemaWorkspaceBuiltInToolResponse;
  isBuiltin?: boolean;
  attachedAgents?: Array<{ agentId: string; agentName: string }>;
}

export function ToolCardContainer({
  tool,
  isBuiltin = true,
  attachedAgents = [],
}: ToolCardContainerProps) {
  // Use the custom hook to handle all logic
  const {
    isPermissionPending,
    requiresPermission,
    displayName,
    description,
    handleTogglePermission,
  } = useToolCard(tool);

  // Prepare UI props for the presentational component
  const uiProps: ToolCardUIProps = {
    displayName,
    description,
    isBuiltin,
    requiresPermission,
    attachedAgents,
    isPermissionPending,
    onTogglePermission: handleTogglePermission,
  };

  // Render the pure UI component
  return <ToolCard {...uiProps} />;
}
