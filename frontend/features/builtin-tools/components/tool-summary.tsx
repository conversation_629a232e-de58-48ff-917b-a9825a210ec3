'use client';

import { useEffect, useState } from 'react';

import { Badge } from '@/components/ui/badge';

interface ToolSummaryProps {
  totalBuiltinCount: number;
  isLoading: boolean;
}

export function ToolSummary({
  totalBuiltinCount,
  isLoading,
}: ToolSummaryProps) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Return empty div during SSR
  if (!mounted) {
    return <div className="h-8" />;
  }

  if (isLoading) {
    return (
      <div className="text-muted-foreground flex items-center gap-2 text-sm">
        <span>Loading tools...</span>
      </div>
    );
  }

  return (
    <div className="mt-2 mb-3 space-y-3">
      <div className="flex flex-wrap items-center gap-3">
        <Badge variant="ghost-primary" className="px-3 py-1">
          {totalBuiltinCount} Tools
        </Badge>
      </div>
    </div>
  );
}
