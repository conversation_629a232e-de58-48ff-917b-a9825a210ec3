'use client';

import React from 'react';

import { Badge } from '@/components/ui/badge';

interface ToolInfoRowProps {
  label: string;
  value?: string | boolean;
  isBadge?: boolean;
  variant?: "default" | "secondary" | undefined;
  hasBorder?: boolean;
}

export function ToolInfoRow({
  label,
  value,
  isBadge = false,
  variant = "default",
  hasBorder = true
}: ToolInfoRowProps) {
  const borderClass = hasBorder ? 'border-b' : '';

  return (
    <div className={`flex items-center justify-between ${borderClass} py-2`}>
      <span className="text-muted-foreground text-sm font-medium">
        {label}
      </span>
      {isBadge ? (
        <Badge variant={variant} size="sm">
          {typeof value === 'string' ? value : (value ? 'Active' : 'Inactive')}
        </Badge>
      ) : (
        <span className="text-sm">
          {typeof value === 'string' ? value : (value ? 'Yes' : 'No')}
        </span>
      )}
    </div>
  );
}
