'use client';

import React from 'react';

import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface ToolTitleProps {
  displayName: string;
  className?: string;
}

export function ToolTitle({ displayName, className }: ToolTitleProps) {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <h3 className={`hover:text-foreground cursor-help truncate text-base font-semibold transition-colors ${className}`}>
            {displayName}
          </h3>
        </TooltipTrigger>
        <TooltipContent
          side="top"
          align="start"
          className="border-border bg-popover max-w-[300px] rounded-lg border px-3 py-2 shadow-lg"
          sideOffset={8}
        >
          <p className="text-popover-foreground text-sm font-medium">
            {displayName}
          </p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
