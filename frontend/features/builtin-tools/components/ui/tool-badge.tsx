'use client';

import React from 'react';

import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { Lock, Shield } from 'lucide-react';

interface ToolBadgeProps {
  type: 'builtin' | 'permission';
  className?: string;
}

export function ToolBadge({ type, className }: ToolBadgeProps) {
  if (type === 'builtin') {
    return (
      <Badge
        variant="ghost-primary"
        className={cn('flex h-7 items-center px-2.5 transition-colors', className)}
      >
        <Shield className="mr-1.5 h-3.5 w-3.5 shrink-0" />
        <span className="text-xs font-medium">Builtin</span>
      </Badge>
    );
  }

  if (type === 'permission') {
    return (
      <Badge
        variant="outline"
        className={cn(
          'flex h-7 items-center px-2.5 transition-colors',
          'text-warning border-warning/20 bg-warning/10',
          'dark:border-warning/30 dark:bg-warning/15 dark:text-warning/90',
          className
        )}
      >
        <Lock className="mr-1.5 h-3.5 w-3.5 shrink-0" />
        <span className="text-xs font-medium">Requires Approval</span>
      </Badge>
    );
  }

  return null;
}
