'use client';

import React from 'react';

import { Button } from '@/components/ui/button';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { Loader2, Lock } from 'lucide-react';

interface PermissionButtonProps {
  requiresPermission: boolean;
  isPending: boolean;
  onTogglePermission: () => void;
  className?: string;
  disabled?: boolean;
}

export function PermissionButton({
  requiresPermission,
  isPending,
  onTogglePermission,
  className,
  disabled = false,
}: PermissionButtonProps) {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            size="sm"
            variant={requiresPermission ? 'default' : 'outline'}
            onClick={onTogglePermission}
            disabled={isPending || disabled}
            className={cn(
              'h-7 px-2 text-xs transition-all',
              requiresPermission && 'bg-warning hover:bg-warning',
              className
            )}
          >
            {isPending ? (
              <Loader2 className="mr-1 h-3 w-3 animate-spin" />
            ) : (
              <Lock className="mr-1 h-3 w-3" />
            )}
            <span>
              {requiresPermission
                ? 'Remove Approval'
                : 'Require Approval'}
            </span>
          </Button>
        </TooltipTrigger>
        <TooltipContent
          side="top"
          align="center"
          className={cn(
            'rounded-lg px-3 py-2 shadow-lg',
            requiresPermission
              ? 'border-warning bg-warning border text-white'
              : 'border-border bg-popover border',
          )}
          sideOffset={8}
        >
          <p
            className={cn(
              'text-sm font-medium',
              requiresPermission
                ? 'text-white'
                : 'text-popover-foreground',
            )}
          >
            {requiresPermission
              ? 'Remove approval requirement'
              : 'Require approval for this tool'}
          </p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
