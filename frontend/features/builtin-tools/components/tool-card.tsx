'use client';

import React from 'react';

import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { cn } from '@/lib/utils';

import { AgentAvatar } from './ui/agent-avatar';
import { PermissionButton } from './ui/permission-button';
import { ToolBadge } from './ui/tool-badge';
import { ToolTitle } from './ui/tool-title';

export interface ToolCardUIProps {
  displayName: string;
  description: string;
  isBuiltin?: boolean;
  requiresPermission: boolean;
  attachedAgents?: Array<{ agentId: string; agentName: string }>;
  isPermissionPending: boolean;
  onTogglePermission: () => void;
  onViewDetails?: () => void;
}

export function ToolCard({
  displayName,
  description,
  isBuiltin = true,
  requiresPermission,
  attachedAgents = [],
  isPermissionPending,
  onTogglePermission,
}: ToolCardUIProps) {
  // Style based on permissions
  const cardBorderStyle = cn(
    'hover:shadow-soft overflow-hidden transition-all duration-200',
    'flex min-h-[280px] flex-col justify-between',
    requiresPermission
      ? 'hover:border-warning/50 dark:hover:border-warning/70'
      : 'hover:border-primary/50 dark:hover:border-primary/70',
  );

  return (
    <Card className={cardBorderStyle}>
      <CardContent className="flex-1 p-6 pb-4">
        <div className="flex h-full flex-col gap-4">
          <div className="flex flex-col gap-3">
            <div className="flex flex-wrap items-center gap-2">
              {isBuiltin && <ToolBadge type="builtin" />}
              {requiresPermission && <ToolBadge type="permission" />}
            </div>

            <ToolTitle displayName={displayName} />
          </div>

          <div className="text-muted-foreground line-clamp-2 flex-1 text-sm">
            {description}
          </div>

          {attachedAgents.length > 0 && (
            <div className="mt-auto">
              <div className="flex items-center gap-2">
                <div className="flex flex-wrap gap-1.5">
                  {attachedAgents?.map((agent) => (
                    <AgentAvatar key={agent.agentId} agent={agent} />
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      </CardContent>

      <CardFooter className="bg-muted/5 flex items-center justify-between gap-2 border-t p-6 pt-4">
        <div className="flex gap-1">
          <PermissionButton
            requiresPermission={requiresPermission}
            isPending={isPermissionPending}
            onTogglePermission={onTogglePermission}
          />
        </div>
      </CardFooter>
    </Card>
  );
}
