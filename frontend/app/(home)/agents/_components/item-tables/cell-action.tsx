'use client';

import { useState, useTransition } from 'react';

import { AgentPublic, AgentsService } from '@/client';
import { AlertModal } from '@/components/modal/alert-modal';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { <PERSON><PERSON><PERSON><PERSON> } from '@/components/utils/cache-key';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Settings } from 'lucide-react';
import { toast } from 'sonner';

import { SettingsDialog } from '../settings-dialog';

type CellActionProps = {
  data: AgentPublic;
};

export const CellAction = ({ data }: CellActionProps) => {
  const queryClient = useQueryClient();

  const [loading, startTransition] = useTransition();
  const [open, setOpen] = useState(false);
  const [showSettingsDialog, setShowSettingsDialog] = useState(false);
  const [optimisticActive, setOptimisticActive] = useState<boolean>(
    data.is_active === true,
  );

  const toggleActiveMutation = useMutation({
    mutationFn: (isActive: boolean) =>
      AgentsService.updateAgent({
        id: data.id,
        requestBody: {
          ...data,
          is_active: isActive,
        },
      }),
    onSuccess: (_, variables) => {
      toast.success('Success', {
        description: `Agent ${!variables ? 'deactivated' : 'activated'} successfully.`,
      });
      setOptimisticActive(variables);
    },
    onError: (_, variables) => {
      // Revert optimistic update on error
      setOptimisticActive(!variables);
      toast.error('An error occurred.', {
        description: `Failed to ${!variables ? 'deactivate' : 'activate'} the agent`,
      });
    },
    onSettled: (data, _error, variables) => {
      // Update the cache without refetching to maintain order
      queryClient.setQueriesData(
        { queryKey: [CacheKey.AGENTS] },
        (oldData: any) => {
          if (!oldData) return oldData;

          // Update the agent in the cache with the new is_active status
          const updatedData = { ...oldData };
          if (updatedData.data && Array.isArray(updatedData.data)) {
            updatedData.data = updatedData.data.map((agent: any) => {
              if (agent.id === data?.id) {
                return { ...agent, is_active: variables };
              }
              return agent;
            });
          }
          return updatedData;
        },
      );
    },
  });

  const deleteMutation = useMutation({
    mutationFn: (id: string) => AgentsService.deleteAgent({ id }),
    onSuccess: () => {
      toast('Success', {
        description: `The item was deleted successfully.`,
      });
      setOpen(false);
    },
    onError: () => {
      toast.error('An error occurred.', {
        description: `An error occurred while deleting the item`,
      });
    },
    onSettled: () => {
      queryClient.invalidateQueries({
        queryKey: [CacheKey.Items],
      });
    },
  });

  const onDeleteConfirm = async () => {
    startTransition(async () => {
      await deleteMutation.mutateAsync(data.id);
    });
  };

  const onToggleActive = async (checked: boolean) => {
    // Apply optimistic update immediately for smoother UX
    setOptimisticActive(checked);

    startTransition(async () => {
      await toggleActiveMutation.mutateAsync(checked);
    });
  };

  return (
    <>
      <AlertModal
        isOpen={open}
        onClose={() => setOpen(false)}
        onConfirm={onDeleteConfirm}
        loading={loading}
      />

      <SettingsDialog
        isOpen={showSettingsDialog}
        onClose={() => setShowSettingsDialog(false)}
        agent={data}
      />
      <div className="flex shrink-0 items-center gap-2">
        <Switch
          checked={optimisticActive}
          onCheckedChange={onToggleActive}
          disabled={toggleActiveMutation.isPending}
          aria-label={optimisticActive ? 'Turn Off' : 'Turn On'}
        />
        <Button
          variant="ghost"
          size="icon"
          onClick={() => setShowSettingsDialog(true)}
          title="Settings"
        >
          <Settings className="size-4" />
        </Button>
      </div>
    </>
  );
};
