'use client';

import { useEffect, useState } from 'react';

import { AgentConnectorsService, AgentsService } from '@/client';
import {
  AgentPublic,
  ConnectorWithStatusResponse,
} from '@/client/types.gen';
import { DisplayConnectionInfo, useConnections } from '@/features/connection/hooks/connection.query';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { SidebarDialog, SidebarTab } from '@/components/ui/sidebar-dialog';
import { Textarea } from '@/components/ui/textarea';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { CacheKey } from '@/components/utils/cache-key';
import { useAgentContext } from '@/features/agent/provider/agent-provider';
import { useBuiltinConnectors } from '@/hooks/use-builtin-connectors';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Info, Loader2, MessageSquare, Server, Shield } from 'lucide-react';
import { toast } from 'sonner';

interface SettingsDialogProps {
  isOpen: boolean;
  onClose: () => void;
  agent: AgentPublic;
}

const TABS_CONFIG: SidebarTab[] = [
  {
    id: 'tools',
    name: 'Tools & Services',
    icon: Shield,
    description: "Manage agent's tools and service connections.",
  },
  {
    id: 'instructions',
    name: 'Instructions',
    icon: MessageSquare,
    description: "Define the agent's core behavior and guidelines.",
  },
];

export function SettingsDialog({
  isOpen,
  onClose,
  agent,
}: SettingsDialogProps) {
  const queryClient = useQueryClient();
  const [instructions, setInstructions] = useState<string>(
    agent.instructions || '',
  );
  const [selectedTools, setSelectedTools] = useState<string[]>([]);
  const [selectedMcpServers, setSelectedMcpServers] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState<string>(TABS_CONFIG[0].id);

  const { agentTools, agentMcpServerMap } = useAgentContext();

  const { data: builtinTools, isLoading: isLoadingTools } =
    useBuiltinConnectors();

  const { servers: mcpServers, isLoading: isLoadingServers } = useConnections();

  useEffect(() => {
    const currentTools = agentTools?.[agent.id] || [];
    const currentMcpServers = agentMcpServerMap[agent.id] || [];
    setSelectedTools(currentTools.map((tool) => tool.id));
    setSelectedMcpServers(currentMcpServers.map((server) => server.name));
  }, [agent.id, agentMcpServerMap, agentTools]);

  const updateAgentMutation = useMutation({
    mutationFn: async () =>
      AgentsService.updateAgent({
        id: agent.id,
        requestBody: { instructions },
      }),
    onSuccess: (updatedAgent) => {
      queryClient.setQueryData([CacheKey.AGENTS], (oldData: any) => {
        if (!oldData || !oldData.agents || !Array.isArray(oldData.agents)) {
          return oldData;
        }
        return {
          ...oldData,
          agents: oldData.agents.map((a: AgentPublic) =>
            a.id === agent.id ? { ...a, ...updatedAgent } : a,
          ),
        };
      });
      toast.success('Agent instructions updated successfully');
    },
    onError: () => toast.error('Failed to update agent instructions'),
  });

  const updateConnectorMutation = useMutation({
    mutationFn: async () =>
      AgentConnectorsService.updateAgentConnector({
        agentId: agent.id,
        requestBody: {
          builtin_connector_ids: selectedTools,
          mcp_servers: selectedMcpServers,
        },
      }),
    onSuccess: (updatedConnectors) => {
      queryClient.setQueryData(
        ['agent-connectors-by-workspace', agent.id],
        updatedConnectors,
      );
      toast.success('Agent tools and servers updated successfully');
    },
    onError: () => toast.error('Failed to update agent tools and servers'),
  });

  const handleSave = async () => {
    try {
      if (activeTab === 'tools') {
        await updateConnectorMutation.mutateAsync();
      } else if (activeTab === 'instructions') {
        await updateAgentMutation.mutateAsync();
      }
    } catch (error) {
      console.error(
        'Failed to update agent settings for tab:',
        activeTab,
        error,
      );
    }
  };

  const isLoadingTabData =
    activeTab === 'tools' && (isLoadingTools || isLoadingServers);
  const isSaving =
    updateAgentMutation.isPending || updateConnectorMutation.isPending;

  const renderToolsContent = () => (
    <div className="custom-scrollbar max-h-[45vh] overflow-y-auto">
      <div className="space-y-6">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <h3 className="flex items-center gap-2 text-base font-semibold">
                <Shield className="h-4 w-4 text-blue-500" />
                Built-in Tools
              </h3>
              <p className="text-muted-foreground text-xs">
                Select the built-in tools this agent can use
              </p>
            </div>
            <Badge variant="outline" className="h-6">
              {selectedTools.length} Selected
            </Badge>
          </div>
          <div className="pt-2">
            {isLoadingTools ? (
              <div className="flex items-center gap-2 py-4">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span className="text-muted-foreground text-sm">
                  Loading tools...
                </span>
              </div>
            ) : (
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                {builtinTools?.map((tool: ConnectorWithStatusResponse) => (
                  <div
                    key={tool.id}
                    className="group hover:bg-muted/50 relative flex items-center space-x-2 rounded-lg p-2"
                  >
                    <Checkbox
                      id={`tool-${tool.id}`}
                      checked={selectedTools.includes(tool.id)}
                      onCheckedChange={(checked) =>
                        setSelectedTools(
                          checked
                            ? [...selectedTools, tool.id]
                            : selectedTools.filter((id) => id !== tool.id),
                        )
                      }
                      className="shrink-0"
                    />
                    <div className="flex min-w-0 flex-1 items-center gap-2">
                      <Label
                        htmlFor={`tool-${tool.id}`}
                        className="flex-1 cursor-pointer truncate text-sm"
                        title={tool.display_name || tool.name}
                      >
                        {tool.display_name || tool.name}
                      </Label>
                      {tool.description && (
                        <TooltipProvider delayDuration={0}>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="ghost"
                                className="h-auto shrink-0 p-0 hover:bg-transparent"
                              >
                                <Info className="text-muted-foreground hover:text-foreground h-4 w-4 transition-colors" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent
                              side="top"
                              className="max-w-[280px]"
                            >
                              <p className="text-sm">{tool.description}</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <h3 className="flex items-center gap-2 text-base font-semibold">
                <Server className="h-4 w-4 text-purple-500" />
                Connections
              </h3>
              <p className="text-muted-foreground text-xs">
                Configure which connections this agent can connect to
              </p>
            </div>
            <Badge variant="outline" className="h-6">
              {selectedMcpServers.length} Selected
            </Badge>
          </div>
          <div className="pt-2">
            {isLoadingServers ? (
              <div className="flex items-center gap-2 py-4">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span className="text-muted-foreground text-sm">
                  Loading servers...
                </span>
              </div>
            ) : mcpServers.length === 0 ? (
              <div className="text-muted-foreground py-4 text-sm">
                No MCP servers available
              </div>
            ) : (
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                {mcpServers.map((server: DisplayConnectionInfo) => (
                  <div
                    key={server.name}
                    className="group hover:bg-muted/50 relative flex items-center space-x-2 rounded-lg p-2"
                  >
                    <Checkbox
                      id={`server-${server.name}`}
                      checked={selectedMcpServers.includes(server.name)}
                      onCheckedChange={(checked) =>
                        setSelectedMcpServers(
                          checked
                            ? [...selectedMcpServers, server.name]
                            : selectedMcpServers.filter(
                                (name) => name !== server.name,
                              ),
                        )
                      }
                      className="shrink-0"
                    />
                    <div className="flex min-w-0 flex-1 items-center gap-2">
                      <Label
                        htmlFor={`server-${server.name}`}
                        className="flex-1 cursor-pointer truncate text-sm"
                        title={server.name}
                      >
                        {server.name}
                      </Label>
                      <TooltipProvider delayDuration={0}>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="ghost"
                              className="h-auto shrink-0 p-0 hover:bg-transparent"
                            >
                              <Info className="text-muted-foreground hover:text-foreground h-4 w-4 transition-colors" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent side="top" className="max-w-[280px]">
                            <div className="space-y-1">
                              <p className="text-sm font-medium">
                                Server Details
                              </p>
                              <p className="text-xs">Type: {server.type}</p>
                              <p className="text-xs">
                                Status:{' '}
                                {server.status === 'connected'
                                  ? 'Connected'
                                  : 'Disconnected'}
                              </p>
                              <p className="text-xs">
                                State:{' '}
                                {server.is_active ? 'Active' : 'Inactive'}
                              </p>
                              {server.tool_list.length > 0 && (
                                <p className="text-xs">
                                  Tools: {server.tool_list.join(', ')}
                                </p>
                              )}
                            </div>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );

  const renderInstructionsContent = () => (
    <div className="custom-scrollbar max-h-[60vh] overflow-y-auto">
      <div className="space-y-4">
        <div className="space-y-1">
          <h3 className="flex items-center gap-2 text-base font-semibold">
            <MessageSquare className="h-4 w-4 text-green-500" />
            Agent Instructions
          </h3>
          <p className="text-muted-foreground text-xs">
            Configure the instructions that guide this agent&apos;s behavior
          </p>
        </div>
        <div className="pt-2">
          <Textarea
            value={instructions}
            onChange={(e) => setInstructions(e.target.value)}
            placeholder="Enter instructions for the agent..."
            className="custom-scrollbar min-h-[calc(100dvh-26rem)] resize-none md:min-h-[380px]"
          />
        </div>
      </div>
    </div>
  );

  return (
    <SidebarDialog
      isOpen={isOpen}
      onClose={onClose}
      title={`Agent Settings - ${agent.title}`}
      description="Configure the tools, services, and instructions for this agent."
      tabs={TABS_CONFIG}
      activeTab={activeTab}
      onTabChange={setActiveTab}
      isSaving={isSaving}
      isLoading={isLoadingTabData}
      onSave={handleSave}
    >
      {activeTab === 'tools' && renderToolsContent()}
      {activeTab === 'instructions' && renderInstructionsContent()}
    </SidebarDialog>
  );
}
