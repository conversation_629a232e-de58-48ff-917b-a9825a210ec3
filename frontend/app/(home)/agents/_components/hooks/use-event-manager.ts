import { useEffect, useRef } from 'react';

interface EventManagerProps {
  isSharedView: boolean;
  isSidebarVisible: boolean;
  setSidebarVisible: (visible: boolean) => void;
  renameConversation: (conversationId: string, name: string) => void;
  deleteConversation: (conversationId: string) => void;
  clearStreamingMessages: () => void;
}

export const useEventManager = ({
  isSharedView,
  isSidebarVisible,
  setSidebarVisible,
  renameConversation,
  deleteConversation,
  clearStreamingMessages,
}: EventManagerProps) => {
  // Use refs to store the latest callback functions
  const setSidebarVisibleRef = useRef(setSidebarVisible);
  const renameConversationRef = useRef(renameConversation);
  const deleteConversationRef = useRef(deleteConversation);
  const clearStreamingMessagesRef = useRef(clearStreamingMessages);
  
  // Update refs when the callbacks change
  useEffect(() => {
    setSidebarVisibleRef.current = setSidebarVisible;
    renameConversationRef.current = renameConversation;
    deleteConversationRef.current = deleteConversation;
    clearStreamingMessagesRef.current = clearStreamingMessages;
  }, [setSidebarVisible, renameConversation, deleteConversation, clearStreamingMessages]);

  useEffect(() => {
    if (isSharedView) return;

    const handleToggleSidebar = () => {
      setSidebarVisibleRef.current(!isSidebarVisible);
    };

    const handleRenameConversation = (event: CustomEvent) => {
      const { conversationId, name } = event.detail;
      renameConversationRef.current(conversationId, name);
    };

    const handleDeleteConversation = (event: CustomEvent) => {
      const { conversationId } = event.detail;
      deleteConversationRef.current(conversationId);
    };

    // Add event listeners
    window.addEventListener('toggle-sidebar', handleToggleSidebar);
    window.addEventListener('rename-conversation', handleRenameConversation as EventListener);
    window.addEventListener('delete-conversation', handleDeleteConversation as EventListener);

    // Cleanup function
    return () => {
      clearStreamingMessagesRef.current();
      window.removeEventListener('toggle-sidebar', handleToggleSidebar);
      window.removeEventListener('rename-conversation', handleRenameConversation as EventListener);
      window.removeEventListener('delete-conversation', handleDeleteConversation as EventListener);
    };
  }, [isSharedView, isSidebarVisible]);
};
