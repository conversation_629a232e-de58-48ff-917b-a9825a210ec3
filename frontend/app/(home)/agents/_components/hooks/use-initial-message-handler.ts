import { useEffect, useRef } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { removeUrlParam } from '../utils/conversationUtils';

interface InitialMessageHandlerProps {
  mounted: boolean;
  initialMessage?: string;
  initialMessageSent: boolean;
  selectedConversation: string | null;
  createConversationPending: boolean;
  conversationId?: string;
  isSharedView: boolean;
  handleSendMessage: (content: string) => void;
  setInitialMessageSent: (sent: boolean) => void;
  handleCreateConversation: () => void;
}

export const useInitialMessageHandler = ({
  mounted,
  initialMessage,
  initialMessageSent,
  selectedConversation,
  createConversationPending,
  conversationId,
  isSharedView,
  handleSendMessage,
  setInitialMessageSent,
  handleCreateConversation,
}: InitialMessageHandlerProps) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // Use refs to store the latest callback functions
  const handleSendMessageRef = useRef(handleSendMessage);
  const setInitialMessageSentRef = useRef(setInitialMessageSent);
  const handleCreateConversationRef = useRef(handleCreateConversation);
  
  // Update refs when the callbacks change
  useEffect(() => {
    handleSendMessageRef.current = handleSendMessage;
    setInitialMessageSentRef.current = setInitialMessageSent;
    handleCreateConversationRef.current = handleCreateConversation;
  }, [handleSendMessage, setInitialMessageSent, handleCreateConversation]);

  useEffect(() => {
    if (!mounted || isSharedView) return;

    const sendInitialMessage = () => {
      if (!initialMessage) return;
      
      const timer = setTimeout(() => {
        handleSendMessageRef.current(initialMessage);
        setInitialMessageSentRef.current(true);
        
        const newUrl = removeUrlParam(
          window.location.pathname,
          searchParams,
          'initialMessage'
        );
        router.replace(newUrl);
      }, 600);
      
      return () => clearTimeout(timer);
    };

    // Case 1: Initial message with existing conversation
    if (initialMessage && !initialMessageSent && selectedConversation) {
      return sendInitialMessage();
    }
    
    // Case 2: Initial message without conversation
    if (initialMessage && !initialMessageSent && !selectedConversation && !createConversationPending) {
      handleCreateConversationRef.current();
      return;
    }
    
    // Case 3: No conversation on first visit
    if (!conversationId && !selectedConversation && !createConversationPending) {
      handleCreateConversationRef.current();
      return;
    }
  }, [
    mounted,
    initialMessage,
    initialMessageSent,
    selectedConversation,
    createConversationPending,
    conversationId,
    isSharedView,
    router,
    searchParams,
  ]);
};
