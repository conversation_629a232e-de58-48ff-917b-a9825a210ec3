import { useCallback, useReducer, useEffect } from 'react';
import { useMessageStream } from '@/hooks/use-autonomous-message-stream';
import { useQuota } from '@/hooks/use-quota';

interface ChatState {
  selectedConversation: string | null;
  mounted: boolean;
  initialMessageSent: boolean;
  isSidebarVisible: boolean;
}

type ChatAction = 
  | { type: 'SET_MOUNTED'; payload: boolean }
  | { type: 'SET_SELECTED_CONVERSATION'; payload: string | null }
  | { type: 'SET_INITIAL_MESSAGE_SENT'; payload: boolean }
  | { type: 'SET_SIDEBAR_VISIBLE'; payload: boolean };

const initialState: ChatState = {
  selectedConversation: null,
  mounted: false,
  initialMessageSent: false,
  isSidebarVisible: false,
};

const chatReducer = (state: ChatState, action: ChatAction): ChatState => {
  switch (action.type) {
    case 'SET_MOUNTED':
      return { ...state, mounted: action.payload };
    case 'SET_SELECTED_CONVERSATION':
      return { ...state, selectedConversation: action.payload };
    case 'SET_INITIAL_MESSAGE_SENT':
      return { ...state, initialMessageSent: action.payload };
    case 'SET_SIDEBAR_VISIBLE':
      return { ...state, isSidebarVisible: action.payload };
    default:
      return state;
  }
};

interface AutonomousChatProps {
  conversationId?: string;
  shareId?: string;
  isSharedView: boolean;
}

export const useAutonomousChat = ({ 
  conversationId, 
  shareId, 
  isSharedView 
}: AutonomousChatProps) => {
  const [state, dispatch] = useReducer(chatReducer, {
    ...initialState,
    selectedConversation: conversationId || null,
  });

  const { quotaInfo } = !shareId ? useQuota() : { quotaInfo: null };

  const messageStream = useMessageStream(
    isSharedView ? null : state.selectedConversation
  );

  const {
    isStreaming,
    streamingMessages,
    handleSendMessage: originalHandleSendMessage,
    clearStreamingMessages,
    interruptConfirmation,
    setInterruptConfirmation,
    stopStream,
    currentReport,
    currentDashboard,
  } = messageStream;

  const handleSendMessage = useCallback(
    (content: string) => {
      if (isSharedView) return;
      originalHandleSendMessage(content);
    },
    [originalHandleSendMessage, isSharedView]
  );

  const setMounted = useCallback((mounted: boolean) => {
    dispatch({ type: 'SET_MOUNTED', payload: mounted });
  }, []);

  const setSelectedConversation = useCallback((conversationId: string | null) => {
    dispatch({ type: 'SET_SELECTED_CONVERSATION', payload: conversationId });
  }, []);

  // Clear streaming messages when selected conversation changes
  useEffect(() => {
    clearStreamingMessages();
  }, [state.selectedConversation, clearStreamingMessages]);

  const setInitialMessageSent = useCallback((sent: boolean) => {
    dispatch({ type: 'SET_INITIAL_MESSAGE_SENT', payload: sent });
  }, []);

  const setSidebarVisible = useCallback((visible: boolean) => {
    dispatch({ type: 'SET_SIDEBAR_VISIBLE', payload: visible });
  }, []);

  return {
    // State
    selectedConversation: state.selectedConversation,
    mounted: state.mounted,
    initialMessageSent: state.initialMessageSent,
    isSidebarVisible: state.isSidebarVisible,
    
    // Actions
    setMounted,
    setSelectedConversation,
    setInitialMessageSent,
    setSidebarVisible,
    
    // Message stream
    isStreaming,
    streamingMessages,
    handleSendMessage,
    clearStreamingMessages,
    interruptConfirmation,
    setInterruptConfirmation,
    stopStream,
    currentReport,
    currentDashboard,
    
    // Other
    quotaInfo,
  };
};
