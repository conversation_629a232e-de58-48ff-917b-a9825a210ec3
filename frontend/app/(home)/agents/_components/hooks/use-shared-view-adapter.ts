import { useMemo } from 'react';
import { processMessages } from '../utils/conversationUtils';
import { Message } from '@/types/chat';

interface SharedViewAdapterProps {
  isSharedView: boolean;
  conversationHistory?: any[];
  streamingMessages?: Message[];
}

export const useSharedViewAdapter = ({
  isSharedView,
  conversationHistory,
  streamingMessages,
}: SharedViewAdapterProps) => {
  const messages = useMemo(
    () => processMessages(isSharedView, conversationHistory, streamingMessages),
    [isSharedView, conversationHistory, streamingMessages]
  );

  const createNoOpHandler = <T extends any[], R>(
    originalHandler: (...args: T) => R
  ) => {
    return isSharedView ? ((..._args: T) => {}) as (...args: T) => R : originalHandler;
  };

  const createNoOpValue = <T>(value: T): T => {
    return isSharedView ? (false as T) : value;
  };

  return {
    messages,
    createNoOpHandler,
    createNoOpValue,
  };
};
