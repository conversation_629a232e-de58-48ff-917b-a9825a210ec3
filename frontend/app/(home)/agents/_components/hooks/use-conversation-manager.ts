import { useCallback } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { AutonomousAgentsService } from '@/client';
import { useToast } from '@/hooks/use-toast';
import { convertToSession } from '@/lib/message-converters';
import { Session } from '@/types/chat';

interface ConversationManagerProps {
  id: string;
  isSharedView: boolean;
  resourceId?: string;
  onConversationCreated?: (conversationId: string) => void;
  onConversationDeleted?: (conversationId: string) => void;
}

export const useConversationManager = ({
  id,
  isSharedView,
  resourceId,
  onConversationCreated,
  onConversationDeleted,
}: ConversationManagerProps) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Get conversations query
  const conversationsQuery = useQuery({
    queryKey: ['conversations', id],
    queryFn: async (): Promise<Session[]> => {
      if (!id || isSharedView) return [];
      const apiConversations = await AutonomousAgentsService.getConversations({
        agentId: id,
      });
      return apiConversations.data.map(convertToSession);
    },
    enabled: !!id && !isSharedView,
  });

  // Create conversation mutation
  const createConversationMutation = useMutation({
    mutationFn: async (params: {
      agent_id: string;
      model_provider: string;
      resource_id?: string;
    }) => {
      if (isSharedView) return null;
      return await AutonomousAgentsService.createConversation({
        requestBody: {
          agent_id: params.agent_id,
          model_provider: params.model_provider || 'bedrock',
          resource_id: params.resource_id || null,
        },
      });
    },
    onSuccess: (data, variables) => {
      if (isSharedView || !data?.id) return;
      
      queryClient.invalidateQueries({
        queryKey: ['conversations', variables.agent_id],
      });
      
      onConversationCreated?.(data.id);
    },
    onError: () => {
      toast({
        title: 'Error creating chat',
        description: 'Failed to create new chat. Please try again.',
        variant: 'destructive',
      });
    },
  });

  // Rename conversation mutation
  const renameConversationMutation = useMutation({
    mutationFn: ({ conversationId, name }: { conversationId: string; name: string }) =>
      AutonomousAgentsService.renameConversation({ conversationId, name }),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['conversations', id],
      });
      toast({
        title: 'Success',
        description: 'Conversation renamed successfully',
      });
    },
    onError: () => {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Failed to rename conversation',
      });
    },
  });

  // Delete conversation mutation
  const deleteConversationMutation = useMutation({
    mutationFn: (conversationId: string) =>
      AutonomousAgentsService.deleteConversation({ conversationId }),
    onSuccess: (_, conversationId) => {
      queryClient.invalidateQueries({
        queryKey: ['conversations', id],
      });
      onConversationDeleted?.(conversationId);
    },
    onError: () => {
      toast({
        title: 'Error deleting chat',
        description: 'Failed to delete chat. Please try again.',
        variant: 'destructive',
      });
    },
  });

  // Handler functions
  const createConversation = useCallback(() => {
    if (isSharedView || createConversationMutation.isPending) return;
    
    createConversationMutation.mutate({
      agent_id: id,
      model_provider: 'bedrock',
      resource_id: resourceId,
    });
  }, [createConversationMutation, id, resourceId, isSharedView]);

  const renameConversation = useCallback(
    (conversationId: string, name: string) => {
      if (isSharedView) return;
      renameConversationMutation.mutate({ conversationId, name });
    },
    [renameConversationMutation, isSharedView]
  );

  const deleteConversation = useCallback(
    (conversationId: string) => {
      if (isSharedView) return;
      
      const conversations = conversationsQuery.data || [];
      const conversationTitle = conversations.find((c) => c.id === conversationId)?.title || 'Conversation';
      
      deleteConversationMutation.mutate(conversationId, {
        onSuccess: () => {
          toast({
            title: 'Conversation deleted',
            description: `"${conversationTitle}" has been deleted successfully.`,
            variant: 'default',
          });
        },
      });
    },
    [deleteConversationMutation, conversationsQuery.data, isSharedView, toast]
  );

  return {
    conversations: conversationsQuery.data || [],
    isLoadingConversations: conversationsQuery.isLoading,
    isCreatingConversation: createConversationMutation.isPending,
    createConversation,
    renameConversation,
    deleteConversation,
  };
};
