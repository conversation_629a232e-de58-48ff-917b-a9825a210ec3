import { useEffect, useRef } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { buildUrlWithParams } from '../utils/conversationUtils';

interface UrlManagerProps {
  selectedConversation: string | null;
  conversationId?: string;
  mounted: boolean;
  isSharedView: boolean;
  setSelectedConversation: (id: string | null) => void;
  setInterruptConfirmation: (confirmation: any) => void;
}

export const useUrlManager = ({
  selectedConversation,
  conversationId,
  mounted,
  isSharedView,
  setSelectedConversation,
  setInterruptConfirmation,
}: UrlManagerProps) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // Use refs to store the latest callback functions
  const setSelectedConversationRef = useRef(setSelectedConversation);
  const setInterruptConfirmationRef = useRef(setInterruptConfirmation);
  
  // Update refs when the callbacks change
  useEffect(() => {
    setSelectedConversationRef.current = setSelectedConversation;
    setInterruptConfirmationRef.current = setInterruptConfirmation;
  }, [setSelectedConversation, setInterruptConfirmation]);

  // Handle conversationId changes
  useEffect(() => {
    if (conversationId) {
      setSelectedConversationRef.current(conversationId);
      setInterruptConfirmationRef.current(null);
    }
  }, [conversationId]);

  // Handle URL updates
  useEffect(() => {
    if (selectedConversation && mounted && !isSharedView) {
      const newUrl = buildUrlWithParams(window.location.pathname, {
        conversation: selectedConversation,
        autonomous: 'true',
        ...(searchParams.get('initialMessage') && { initialMessage: searchParams.get('initialMessage')! }),
      });
      router.replace(newUrl, { scroll: false });
    }
  }, [selectedConversation, mounted, isSharedView, router, searchParams]);
};
