import { convertToUIMessage } from '@/lib/message-converters';
import { Message, Session } from '@/types/chat';

export const createSessionFromConversation = (
  conversationId: string,
  conversations: Session[],
  allMessages: Message[]
): Session | undefined => {
  if (!conversationId) return undefined;
  
  const conversation = conversations.find((c) => c.id === conversationId);
  
  return {
    id: conversationId,
    title: conversation?.title || 'New Chat',
    timestamp: new Date(conversation?.timestamp || new Date().toISOString()),
    category: undefined,
    lastMessage: allMessages.length > 0 ? allMessages[allMessages.length - 1] : undefined,
  };
};

export const processMessages = (
  isSharedView: boolean,
  conversationHistory: any[] | undefined,
  streamingMessages: Message[] | undefined
): Message[] => {
  if (isSharedView) {
    return conversationHistory?.map(convertToUIMessage) || [];
  }
  return streamingMessages || [];
};

export const buildUrlWithParams = (
  pathname: string,
  params: Record<string, string>
): string => {
  const urlParams = new URLSearchParams();
  Object.entries(params).forEach(([key, value]) => {
    urlParams.set(key, value);
  });
  return pathname + `?${urlParams.toString()}`;
};

export const removeUrlParam = (
  pathname: string,
  searchParams: URLSearchParams,
  paramToRemove: string
): string => {
  const params = new URLSearchParams(searchParams);
  params.delete(paramToRemove);
  return pathname + (params.toString() ? `?${params.toString()}` : '');
};