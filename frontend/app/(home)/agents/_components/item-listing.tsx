'use client';

import Image from 'next/image';

import { AgentPublic } from '@/client';
import { AgentCard } from '@/components/agents/agent-card';
import { LoadingSkeleton } from '@/components/agents/loading-skeleton';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { AgentCardList } from '@/features/agent/components/agent-card-list';
import { AgentProvider } from '@/features/agent/provider/agent-provider';
import { ChatWithResourceDialog } from '@/features/resource/components/chat-with-resource-dialog/chat-with-resource-dialog';
import { useAgents } from '@/hooks/use-agents';

interface ItemListingPageProps {
  page?: number;
  limit?: number;
}

const ItemListingPage = ({ page = 1, limit = 10 }: ItemListingPageProps) => {
  const { data: agentsResponse, isLoading } = useAgents({
    skip: (page - 1) * limit,
    limit,
  });

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
        {[...Array(6)].map((_, i) => (
          <LoadingSkeleton key={i} />
        ))}
      </div>
    );
  }

  if (!agentsResponse?.data?.length) {
    return (
      <div className="text-muted-foreground flex min-h-[400px] flex-col items-center justify-center">
        <div className="mb-4 h-12 w-12 overflow-hidden rounded-full">
          <Image
            src="/avatars/anna.webp"
            alt="Assistant Avatar"
            width={48}
            height={48}
            className="object-cover"
          />
        </div>
        <p className="text-lg">No agents found</p>
      </div>
    );
  }

  // Sort agents by title to maintain consistent order
  const sortedAgents = [...agentsResponse.data].sort((a, b) => {
    return a.title.localeCompare(b.title);
  });

  const autonomousAgents = sortedAgents.filter(
    (agent) => agent.type === 'autonomous_agent',
  );

  const conversationalAgents = sortedAgents.filter(
    (agent) => agent.type === 'conversation_agent',
  );

  // return <AgentCardList />;

  return (
    <AgentProvider>
      <div className="flex h-[calc(100dvh-10rem)] flex-col gap-4">
        {autonomousAgents.length > 0 && (
          <div className="flex flex-col gap-2">
            <div className="flex items-center gap-2">
              <h2 className="text-xl font-semibold tracking-tight">
                Group Chat
              </h2>
              <Badge variant="secondary" className="text-sm">
                {autonomousAgents.length}
              </Badge>
            </div>
            <ScrollArea className="h-auto">
              <div className="grid grid-cols-1 gap-4 pr-4 sm:grid-cols-2 lg:grid-cols-3">
                {autonomousAgents.map((item: AgentPublic) => (
                  <ChatWithResourceDialog key={item.id} agentId={item.id}>
                    <AgentCard item={item} />
                  </ChatWithResourceDialog>
                ))}
              </div>
            </ScrollArea>
          </div>
        )}

        {conversationalAgents.length > 0 && (
          <div className="flex min-h-0 flex-1 flex-col gap-2">
            <div className="flex items-center gap-2">
              <h2 className="text-xl font-semibold tracking-tight">
                Team Members
              </h2>
              <Badge variant="secondary" className="text-sm">
                {conversationalAgents.length}
              </Badge>
            </div>
            <div className="min-h-0 flex-1">
              <ScrollArea className="h-full">
                <AgentCardList />
              </ScrollArea>
            </div>
          </div>
        )}
      </div>
    </AgentProvider>
  );
};

export default ItemListingPage;
