import { ReactNode, createContext, useContext } from 'react';

import { RecommendationPublic, Report } from '@/client/types.gen';
import { InterruptConfirmation } from '@/hooks/message-stream';
import { Message, Session } from '@/types/chat';

export type ChatType = 'resource' | 'agent';

interface ChatContextType {
  chatType: ChatType;
  messages: Message[];
  onSendMessage: (
    message: string,
    restore?: { messageId: string } | null,
    actionType?: string,
  ) => void;
  isStreaming: boolean;
  currentSession?: Session;
  onNewChat?: () => void;
  onStopStreaming?: () => void;
  confirmation?: InterruptConfirmation | null;
  isCreatingConversation?: boolean;
  streamingRecommendations?: RecommendationPublic[];
  setStreamingRecommendations?: React.Dispatch<
    React.SetStateAction<RecommendationPublic[]>
  >;
  defaultCanvasTab?: 'charts' | 'planning' | 'environment';
  quotaInfo?: {
    quota_used: number;
    quota_limit: number;
    quota_remaining: number;
    usage_percentage: number;
  };
  resourceId?: string;
  isSharedView?: boolean;
  currentReport?: Report | null;
  currentDashboard?: any;
  conversationId?: string;
}

const ChatContext = createContext<ChatContextType | undefined>(undefined);

interface ChatProviderProps {
  children: ReactNode;
  chatType?: ChatType;
  messages: Message[];
  onSendMessage: (
    message: string,
    restore?: { messageId: string } | null,
    actionType?: string,
  ) => void;
  isStreaming: boolean;
  currentSession?: Session;
  onNewChat?: () => void;
  onStopStreaming?: () => void;
  confirmation?: InterruptConfirmation | null;
  isCreatingConversation?: boolean;
  streamingRecommendations?: RecommendationPublic[];
  setStreamingRecommendations?: React.Dispatch<
    React.SetStateAction<RecommendationPublic[]>
  >;
  defaultCanvasTab?: 'charts' | 'planning' | 'environment';
  quotaInfo?: {
    quota_used: number;
    quota_limit: number;
    quota_remaining: number;
    usage_percentage: number;
  };
  resourceId?: string;
  isSharedView?: boolean;
  currentReport?: Report | null;
  currentDashboard?: any;
  conversationId?: string;
}

export function ChatProvider({
  children,
  chatType = 'agent',
  messages,
  onSendMessage,
  isStreaming,
  currentSession,
  onNewChat,
  onStopStreaming,
  confirmation,
  isCreatingConversation = false,
  streamingRecommendations = [],
  setStreamingRecommendations,
  defaultCanvasTab = 'environment',
  quotaInfo,
  resourceId,
  isSharedView = false,
  currentReport,
  currentDashboard,
  conversationId,
}: ChatProviderProps) {
  const value: ChatContextType = {
    chatType,
    messages,
    onSendMessage,
    isStreaming,
    currentSession,
    onNewChat,
    onStopStreaming,
    confirmation,
    isCreatingConversation,
    streamingRecommendations,
    setStreamingRecommendations,
    defaultCanvasTab,
    quotaInfo,
    resourceId,
    isSharedView,
    currentReport,
    currentDashboard,
    conversationId,
  };

  return (
    <ChatContext.Provider value={value}>{children}</ChatContext.Provider>
  );
}

export function useChatContext() {
  const context = useContext(ChatContext);
  if (context === undefined) {
    throw new Error('useChatContext must be used within a ChatProvider');
  }
  return context;
}
