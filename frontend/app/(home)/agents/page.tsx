import PageContainer from '@/components/layout/page-container';
import { PageHeader } from '@/components/layout/page-header';

import ItemListingPage from './_components/item-listing';

export const metadata = {
  title: 'Agents',
};

type PageProps = {
  searchParams: Promise<{ page: string; limit: string }>;
};

export default async function Page(props: PageProps) {
  const searchParams = await props.searchParams;
  return (
    <PageContainer>
      <div className="space-y-4">
        <PageHeader
          title="Agents"
          description="Manage and monitor your cloud agents"
        />
        <ItemListingPage
          page={searchParams.page ? parseInt(searchParams.page) : undefined}
          limit={searchParams.limit ? parseInt(searchParams.limit) : undefined}
        />
      </div>
    </PageContainer>
  );
}
