// constants.ts
export const CHART_COLORS = {
  primary: {
    main: 'var(--color-primary)',
    muted: 'color-mix(in srgb, var(--color-primary), transparent 50%)',
    subtle: 'color-mix(in srgb, var(--color-primary), transparent 80%)',
  },
  secondary: {
    main: 'var(--color-secondary)',
    muted: 'color-mix(in srgb, var(--color-secondary), transparent 50%)',
    subtle: 'color-mix(in srgb, var(--color-secondary), transparent 80%)',
  },
  accent: {
    main: 'var(--color-accent)',
    muted: 'color-mix(in srgb, var(--color-accent), transparent 50%)',
    subtle: 'color-mix(in srgb, var(--color-accent), transparent 80%)',
  },
  // Color palette for multiple data points
  palette: [
    'var(--color-primary)',
    'var(--color-secondary)',
    'var(--color-accent)',
    'var(--color-success)',
    'var(--color-warning)',
  ],
};

export const CHART_STYLES = {
  tooltip: {
    className: 'rounded-lg border bg-background p-2 shadow-md',
    contentStyle: {
      background: 'transparent',
      border: 'none',
      padding: 0,
    },
  },
  grid: {
    stroke: 'var(--color-border)',
    strokeDasharray: '3 3',
  },
  text: {
    fontSize: 12,
    fill: 'var(--color-muted-foreground)',
  },
};
