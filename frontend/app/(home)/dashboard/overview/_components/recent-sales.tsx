'use client';

import { AwsIcon } from '@/components/aws-icons';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import pathsConfig from '@/config/paths.config';
import { formatUSD } from '@/lib/currency';

import { useTopSavings } from '../../hooks/use-top-savings';

// Helper function to map resource type to icon key
const getIconKey = (resourceType: string): string => {
  const typeMap: { [key: string]: string } = {
    ec2: 'ec2',
    rds: 'rds',
    // Add more mappings as needed
  };
  return typeMap[resourceType.toLowerCase()] || 'ec2'; // Default to EC2 if no match
};

// Helper function to get effort badge variant
const getEffortVariant = (
  effort: string,
): 'default' | 'secondary' | 'destructive' | 'outline' => {
  switch (effort.toLowerCase()) {
    case 'low':
      return 'default';
    case 'medium':
      return 'secondary';
    case 'high':
      return 'destructive';
    default:
      return 'outline';
  }
};

// Helper function to get risk badge variant
const getRiskVariant = (
  risk: string,
): 'default' | 'secondary' | 'destructive' | 'outline' => {
  switch (risk.toLowerCase()) {
    case 'low':
      return 'default';
    case 'medium':
      return 'secondary';
    case 'high':
      return 'destructive';
    default:
      return 'outline';
  }
};

interface RecentSalesProps {
  startDate?: string;
  endDate?: string;
}

export function RecentSales({ startDate, endDate }: RecentSalesProps) {
  const { data: topSavings, isLoading } = useTopSavings(startDate, endDate);

  const content = isLoading ? (
    Array(5)
      .fill(0)
      .map((_, i) => (
        <div key={i} className="mb-4 flex items-center space-x-4 p-3">
          <Skeleton className="h-12 w-12 rounded-full" />
          <div className="flex-1 space-y-2">
            <Skeleton className="h-4 w-[200px]" />
            <div className="flex space-x-2">
              <Skeleton className="h-5 w-12 rounded-full" />
              <Skeleton className="h-5 w-12 rounded-full" />
            </div>
          </div>
          <Skeleton className="h-4 w-[100px]" />
        </div>
      ))
  ) : (
    <div className="space-y-3">
      {topSavings?.data?.length ? (
        topSavings?.data.map((recommendation) => (
          <a
            key={recommendation.title}
            href={pathsConfig.app.recommendationDetail(recommendation.id)}
            className="hover:border-border hover:bg-muted/50 flex items-center space-x-4 rounded-lg border border-transparent p-3 transition-colors"
          >
            <div className="flex h-12 w-12 shrink-0 items-center justify-center overflow-hidden rounded-lg">
              {AwsIcon[getIconKey(recommendation.resource.type)]({
                size: 28,
                className: 'text-foreground',
              })}
            </div>

            <div className="flex-1 space-y-2">
              <p className="text-sm leading-none font-medium">
                {recommendation.title}
              </p>
              <div className="flex items-center space-x-2">
                <Badge
                  variant={getEffortVariant(recommendation.effort)}
                  className="px-2 py-0.5 text-xs"
                >
                  Effort: {recommendation.effort}
                </Badge>
                <Badge
                  variant={getRiskVariant(recommendation.risk)}
                  className="px-2 py-0.5 text-xs"
                >
                  Risk: {recommendation.risk}
                </Badge>
              </div>
            </div>

            <div className="text-right">
              <div className="text-sm font-medium text-green-600">
                {formatUSD(recommendation.potential_savings)}
              </div>
              <div className="text-muted-foreground text-xs">/ month</div>
            </div>
          </a>
        ))
      ) : (
        <div className="py-8 text-center">
          <p className="text-muted-foreground text-sm">No data found</p>
        </div>
      )}
    </div>
  );

  return (
    <Card className="h-full rounded-lg">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
        <div>
          <CardTitle className="text-lg font-semibold">
            Top 5 Potential Monthly Savings
          </CardTitle>
          <p className="text-muted-foreground mt-1 text-sm">
            Recommendations with effort and risk assessment
          </p>
        </div>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          className="text-muted-foreground h-5 w-5"
        >
          <path d="M16 6h6M16 10h6M16 14h6M16 18h6M4 6h6M4 10h6M4 14h6M4 18h6" />
          <circle cx="8" cy="6" r="2" />
          <circle cx="8" cy="10" r="2" />
          <circle cx="8" cy="14" r="2" />
          <circle cx="8" cy="18" r="2" />
        </svg>
      </CardHeader>
      <CardContent className="pt-0">{content}</CardContent>
    </Card>
  );
}
