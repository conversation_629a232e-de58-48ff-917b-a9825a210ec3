'use client';

import * as React from 'react';

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
} from '@/components/ui/chart';
import { TrendingDown } from 'lucide-react';
import { Cell, Label, Pie, PieChart } from 'recharts';

import { CHART_COLORS } from './constants';

const totalPotentialSavings = 45231.89;

const chartData = [
  { service: 'EC2', savings: 20354.35, fill: 'var(--color-ec2)' },
  { service: 'RDS', savings: 11307.97, fill: 'var(--color-rds)' },
  { service: 'CloudFront', savings: 6784.78, fill: 'var(--color-cloudfront)' },
  { service: 'S3', savings: 4523.19, fill: 'var(--color-s3)' },
  { service: 'Other', savings: 2261.6, fill: 'var(--color-other)' },
];

const chartConfig = {
  savings: {
    label: 'Monthly Savings ($)',
  },
  ec2: {
    label: 'EC2',
    color: 'var(--color-chart-1)',
  },
  rds: {
    label: 'RDS',
    color: 'var(--color-chart-2)',
  },
  cloudfront: {
    label: 'CloudFront',
    color: 'var(--color-chart-3)',
  },
  s3: {
    label: 'S3',
    color: 'var(--color-chart-4)',
  },
  other: {
    label: 'Other',
    color: 'var(--color-chart-5)',
  },
} satisfies ChartConfig;

export function PieGraph() {
  return (
    <Card className="flex flex-col rounded-lg">
      <CardHeader className="items-center pb-0">
        <CardTitle>AWS Cost Optimization</CardTitle>
        <CardDescription>Monthly Savings ($) by Service</CardDescription>
      </CardHeader>
      <CardContent className="flex-1 pb-0">
        <ChartContainer
          config={chartConfig}
          className="mx-auto aspect-square max-h-[360px]"
        >
          <PieChart>
            <ChartTooltip
              cursor={false}
              content={({ active, payload }) => {
                if (active && payload && payload.length) {
                  const data = payload[0];
                  const value = data.value as number;
                  const percentage = (
                    (value / totalPotentialSavings) *
                    100
                  ).toFixed(1);
                  return (
                    <div className="bg-background w-[150px] rounded-lg border p-3 shadow-lg">
                      <div>{data.name}</div>
                      <div>
                        $
                        {value.toLocaleString(undefined, {
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 2,
                        })}
                      </div>
                      <div>({percentage}%)</div>
                    </div>
                  );
                }
                return null;
              }}
            />
            <Pie
              data={chartData}
              dataKey="savings"
              nameKey="service"
              innerRadius={60}
              strokeWidth={5}
            >
              <Label
                content={({ viewBox }) => {
                  if (viewBox && 'cx' in viewBox && 'cy' in viewBox) {
                    return (
                      <text
                        x={viewBox.cx}
                        y={viewBox.cy}
                        textAnchor="middle"
                        dominantBaseline="middle"
                      >
                        <tspan
                          x={viewBox.cx}
                          y={viewBox.cy}
                          className="fill-foreground text-3xl font-bold"
                        >
                          $
                          {totalPotentialSavings.toLocaleString(undefined, {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2,
                          })}
                        </tspan>
                        <tspan
                          x={viewBox.cx}
                          y={(viewBox.cy || 0) + 24}
                          className="fill-muted-foreground"
                        >
                          Total Savings
                        </tspan>
                      </text>
                    );
                  }
                }}
              />

              {chartData.map((entry, index) => (
                <Cell
                  key={`cell-${index}`}
                  fill={
                    CHART_COLORS.palette[index % CHART_COLORS.palette.length]
                  }
                />
              ))}
            </Pie>
          </PieChart>
        </ChartContainer>
      </CardContent>
      <CardFooter className="flex-col gap-2 text-sm">
        <div className="flex items-center gap-2 leading-none font-medium">
          Potential cost reduction: 28.7% <TrendingDown className="h-4 w-4" />
        </div>
        <div className="text-muted-foreground leading-none">
          Estimated annual savings based on current usage
        </div>
      </CardFooter>
    </Card>
  );
}
