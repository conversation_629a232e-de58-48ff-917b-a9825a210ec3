'use client';

import {
  Card,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON>er,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
} from '@/components/ui/chart';
import { TrendingDown } from 'lucide-react';
import { Area, AreaChart, CartesianGrid, XAxis } from 'recharts';

import { CHART_COLORS } from './constants';

// Generate realistic cost data
const chartData = [
  { month: 'January', originalCost: 10000, reducedCost: 8500 },
  { month: 'February', originalCost: 12000, reducedCost: 9800 },
  { month: 'March', originalCost: 11000, reducedCost: 8900 },
  { month: 'April', originalCost: 13000, reducedCost: 10200 },
  { month: 'May', originalCost: 14000, reducedCost: 10800 },
  { month: 'June', originalCost: 15000, reducedCost: 11500 },
];

const chartConfig = {
  originalCost: {
    label: 'Original Cost',
    color: 'var(--color-chart-1)',
  },
  reducedCost: {
    label: 'Reduced Cost',
    color: 'var(--color-chart-2)',
  },
} satisfies ChartConfig;

export function AreaGraph() {
  const totalOriginalCost = chartData.reduce(
    (sum, data) => sum + data.originalCost,
    0,
  );
  const totalReducedCost = chartData.reduce(
    (sum, data) => sum + data.reducedCost,
    0,
  );
  const totalSavings = totalOriginalCost - totalReducedCost;
  const savingsPercentage = ((totalSavings / totalOriginalCost) * 100).toFixed(
    1,
  );

  return (
    <Card>
      <CardHeader className="rounded-lg">
        <CardTitle>Cost Reduction Analysis</CardTitle>
        <CardDescription>
          Comparing original and reduced costs over the last 6 months
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer
          config={chartConfig}
          className="aspect-auto h-[310px] w-full"
        >
          <AreaChart
            accessibilityLayer
            data={chartData}
            margin={{
              left: 12,
              right: 12,
            }}
          >
            <CartesianGrid vertical={false} />
            <defs>
              <linearGradient id="originalCost" x1="0" y1="0" x2="0" y2="1">
                <stop
                  offset="5%"
                  stopColor={CHART_COLORS.primary.main}
                  stopOpacity={0.2}
                />
                <stop
                  offset="95%"
                  stopColor={CHART_COLORS.primary.main}
                  stopOpacity={0}
                />
              </linearGradient>
            </defs>

            <defs>
              <linearGradient id="reducedCost" x1="0" y1="0" x2="0" y2="1">
                <stop
                  offset="5%"
                  stopColor={CHART_COLORS.accent.main}
                  stopOpacity={0.2}
                />
                <stop
                  offset="95%"
                  stopColor={CHART_COLORS.accent.main}
                  stopOpacity={0}
                />
              </linearGradient>
            </defs>
            <XAxis
              dataKey="month"
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              tickFormatter={(value) => value.slice(0, 3)}
            />
            <ChartTooltip
              cursor={false}
              content={({ active, payload, label }) => {
                if (active && payload && payload.length) {
                  const originalCost = payload.find(
                    (p) => p.name === 'originalCost',
                  )?.value as number;
                  const reducedCost = payload.find(
                    (p) => p.name === 'reducedCost',
                  )?.value as number;
                  const savings = originalCost - reducedCost;
                  const percentage = ((savings / originalCost) * 100).toFixed(
                    1,
                  );

                  return (
                    <div className="bg-background w-[200px] rounded-lg border p-3 shadow-lg">
                      <div>{label}</div>
                      <div>Original Cost: ${originalCost.toLocaleString()}</div>
                      <div>Reduced Cost: ${reducedCost.toLocaleString()}</div>
                      <div>
                        Savings: ${savings.toLocaleString()} ({percentage}%)
                      </div>
                    </div>
                  );
                }
                return null;
              }}
            />
            <Area
              dataKey="originalCost"
              type="monotone"
              stroke={CHART_COLORS.primary.main}
              fill="url(#originalCost)"
              strokeWidth={2}
              dot={{ fill: CHART_COLORS.primary.main }}
              activeDot={{ r: 6, strokeWidth: 0 }}
            />
            <Area
              dataKey="reducedCost"
              type="monotone"
              stroke={CHART_COLORS.accent.main}
              fill="url(#colorValue)"
              strokeWidth={2}
              dot={{ fill: CHART_COLORS.accent.main }}
              activeDot={{ r: 6, strokeWidth: 0 }}
            />
          </AreaChart>
        </ChartContainer>
      </CardContent>
      <CardFooter>
        <div className="flex w-full items-start gap-2 text-sm">
          <div className="grid gap-2">
            <div className="flex items-center gap-2 leading-none font-medium">
              Total cost reduction: {savingsPercentage}%{' '}
              <TrendingDown className="h-4 w-4" />
            </div>
            <div className="text-muted-foreground flex items-center gap-2 leading-none">
              Total savings: ${totalSavings.toLocaleString()}
            </div>
          </div>
        </div>
      </CardFooter>
    </Card>
  );
}
