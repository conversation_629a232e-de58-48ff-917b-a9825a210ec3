'use client';

import * as React from 'react';

import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
} from '@/components/ui/chart';
import { Skeleton } from '@/components/ui/skeleton';
import { formatUSD } from '@/lib/currency';
import { TrendingUp } from 'lucide-react';
import { Cell, Label, Pie, PieChart } from 'recharts';

import { useServiceSavings } from '../../hooks/use-service-savings';
import { CHART_COLORS } from './constants';

interface TopServicesPieChartProps {
  startDate?: string;
  endDate?: string;
}

const chartConfig = {
  savings: {
    label: 'Monthly Savings ($)',
  },
  ec2: {
    label: 'EC2',
    color: 'var(--color-chart-1)',
  },
  rds: {
    label: 'RDS',
    color: 'var(--color-chart-2)',
  },
  s3: {
    label: 'S3',
    color: 'var(--color-chart-3)',
  },
  lambda: {
    label: 'Lambda',
    color: 'var(--color-chart-4)',
  },
  other: {
    label: 'Other',
    color: 'var(--color-chart-5)',
  },
} satisfies ChartConfig;

export function TopServicesPieChart({
  startDate,
  endDate,
}: TopServicesPieChartProps) {
  const { data: serviceSavings, isLoading } = useServiceSavings(
    startDate,
    endDate,
  );

  const chartData = React.useMemo(() => {
    if (!serviceSavings?.data) return [];

    // Filter out services with 0 savings
    const validServices = serviceSavings.data.filter(
      (service) => service.savings > 0,
    );

    if (validServices.length === 0) return [];

    // Take top 4 services and group the rest as "Other"
    const topServices = validServices.slice(0, 4);
    const otherServices = validServices.slice(4);

    const otherSavings = otherServices.reduce(
      (sum, service) => sum + service.savings,
      0,
    );

    const data = topServices.map((service, index) => ({
      service: service.service,
      savings: service.savings,
      percentage: service.percentage,
      fill: CHART_COLORS.palette[index % CHART_COLORS.palette.length],
    }));

    if (otherSavings > 0) {
      data.push({
        service: 'Other',
        savings: otherSavings,
        percentage: (otherSavings / serviceSavings.total_savings) * 100,
        fill: CHART_COLORS.palette[4 % CHART_COLORS.palette.length],
      });
    }

    return data;
  }, [serviceSavings]);

  const totalSavings = serviceSavings?.total_savings || 0;
  const hasValidData = chartData.length > 0 && totalSavings > 0;

  // Dynamic title based on actual number of services
  const chartTitle =
    chartData.length > 0
      ? `Top ${chartData.length} High-Consuming Service${chartData.length !== 1 ? 's' : ''}`
      : 'High-Consuming Services';

  if (isLoading) {
    return (
      <Card className="flex flex-col rounded-lg">
        <CardHeader className="items-center pb-0">
          <CardTitle>{chartTitle}</CardTitle>
          <CardDescription>
            Monthly Savings Opportunities by Service
          </CardDescription>
        </CardHeader>
        <CardContent className="flex-1 pb-0">
          <div className="mx-auto flex aspect-square max-h-[360px] items-center justify-center">
            <Skeleton className="h-[300px] w-[300px] rounded-full" />
          </div>
        </CardContent>
        <CardFooter className="flex-col gap-2 text-sm">
          <div className="flex items-center gap-2 leading-none font-medium">
            <Skeleton className="h-4 w-32" />
          </div>
          <Skeleton className="h-4 w-48" />
        </CardFooter>
      </Card>
    );
  }

  if (!hasValidData) {
    return (
      <Card className="flex flex-col rounded-lg">
        <CardHeader className="items-center pb-0">
          <CardTitle>{chartTitle}</CardTitle>
          <CardDescription>
            Monthly Savings Opportunities by Service
          </CardDescription>
        </CardHeader>
        <CardContent className="flex-1 pb-0">
          <div className="mx-auto flex aspect-square max-h-[360px] items-center justify-center">
            <p className="text-muted-foreground text-sm">No data available</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="flex flex-col rounded-lg">
      <CardHeader className="items-center pb-0">
        <CardTitle>{chartTitle}</CardTitle>
        <CardDescription>
          Monthly Savings Opportunities by Service
        </CardDescription>
      </CardHeader>
      <CardContent className="flex-1 pb-0">
        <ChartContainer
          config={chartConfig}
          className="mx-auto aspect-square max-h-[360px]"
        >
          <PieChart>
            <ChartTooltip
              cursor={false}
              content={({ active, payload }) => {
                if (active && payload && payload.length) {
                  const data = payload[0];
                  const value = data.value as number;
                  const percentage = data.payload.percentage;
                  return (
                    <div className="bg-background w-[180px] rounded-lg border p-3 shadow-lg">
                      <div className="font-medium">{data.name}</div>
                      <div className="text-sm">{formatUSD(value)}</div>
                      <div className="text-muted-foreground text-xs">
                        {percentage.toFixed(1)}% of total
                      </div>
                    </div>
                  );
                }
                return null;
              }}
            />
            <Pie
              data={chartData}
              dataKey="savings"
              nameKey="service"
              innerRadius={60}
              strokeWidth={5}
            >
              <Label
                content={({ viewBox }) => {
                  if (viewBox && 'cx' in viewBox && 'cy' in viewBox) {
                    return (
                      <text
                        x={viewBox.cx}
                        y={viewBox.cy}
                        textAnchor="middle"
                        dominantBaseline="middle"
                      >
                        <tspan
                          x={viewBox.cx}
                          y={viewBox.cy}
                          className="fill-foreground text-2xl font-bold"
                        >
                          {formatUSD(totalSavings)}
                        </tspan>
                        <tspan
                          x={viewBox.cx}
                          y={(viewBox.cy || 0) + 20}
                          className="fill-muted-foreground text-sm"
                        >
                          Total Savings
                        </tspan>
                      </text>
                    );
                  }
                }}
              />

              {chartData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.fill} />
              ))}
            </Pie>
          </PieChart>
        </ChartContainer>
      </CardContent>
      <CardFooter className="flex-col gap-2 text-sm">
        <div className="flex items-center gap-2 leading-none font-medium">
          Optimization opportunities identified{' '}
          <TrendingUp className="h-4 w-4" />
        </div>
        <div className="text-muted-foreground leading-none">
          Potential monthly savings across {chartData.length} service
          {chartData.length !== 1 ? 's' : ''}
        </div>
      </CardFooter>
    </Card>
  );
}
