import { RecommendationPublic } from '@/client';
import { <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { DollarSign } from 'lucide-react';

export function RecommendationDetail({ data }: { data: RecommendationPublic }) {
  return (
    <CardHeader className="flex flex-row items-center justify-between space-y-0 rounded-lg pb-2">
      <CardTitle className="text-sm font-medium">Monthly Savings ($)</CardTitle>
      <DollarSign size={16} />
    </CardHeader>
  );
}
