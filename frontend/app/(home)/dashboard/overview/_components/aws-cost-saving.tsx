'use client';

import { useMemo, useState } from 'react';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
} from '@/components/ui/chart';
import { Skeleton } from '@/components/ui/skeleton';
import { formatUSD } from '@/lib/currency';
import { DateFormat, formatUtcDate } from '@/lib/date-utils';
import {
  differenceInDays,
  eachDayOfInterval,
  isAfter,
  parseISO,
} from 'date-fns';
import { Bar, BarChart, CartesianGrid, XAxis } from 'recharts';

import { useResourceSavings } from '../../hooks/use-resource-savings';
import { CHART_COLORS } from './constants';

export const description = 'An interactive bar chart showing daily savings';

const chartConfig = {
  ec2: {
    label: 'EC2 Savings',
    color: 'var(--color-chart-1)',
  },
  rds: {
    label: 'RDS Savings',
    color: 'var(--color-chart-2)',
  },
} satisfies ChartConfig;

const getDurationText = (startDate: string, endDate: string) => {
  const start = parseISO(startDate);
  const end = parseISO(endDate);
  const now = new Date();

  const effectiveEndDate = isAfter(end, now) ? now : end;
  const diffDays = differenceInDays(effectiveEndDate, start) + 1;

  if (diffDays <= 1) return '';
  return `over ${diffDays} days`;
};

export function AwsCostSaving({
  startDate,
  endDate,
}: {
  startDate: string;
  endDate: string;
}) {
  const [activeChart, setActiveChart] = useState<'ec2' | 'rds'>('ec2');
  const { data: resourceSavings, isLoading } = useResourceSavings(
    startDate,
    endDate,
  );

  const totalSavings = useMemo(
    () => ({
      ec2: resourceSavings?.total_ec2_savings || 0,
      rds: resourceSavings?.total_rds_savings || 0,
    }),
    [resourceSavings],
  );

  const chartData = useMemo(() => {
    if (!resourceSavings) return [];

    const ec2Map = new Map(
      resourceSavings.ec2_savings.map((point) => [
        formatUtcDate(new Date(point.date), DateFormat.ISO_DATE),
        point.value,
      ]),
    );
    const rdsMap = new Map(
      resourceSavings.rds_savings.map((point) => [
        formatUtcDate(new Date(point.date), DateFormat.ISO_DATE),
        point.value,
      ]),
    );

    const dateRange = eachDayOfInterval({
      start: parseISO(startDate),
      end: parseISO(endDate),
    });

    return dateRange.map((date) => {
      const dateStr = formatUtcDate(date, DateFormat.ISO_DATE);
      return {
        date: dateStr,
        ec2: ec2Map.get(dateStr) || 0,
        rds: rdsMap.get(dateStr) || 0,
      };
    });
  }, [resourceSavings, startDate, endDate]);

  return (
    <Card>
      <CardHeader className="flex flex-col items-stretch space-y-0 rounded-lg border-b p-0 sm:flex-row">
        <div className="flex flex-1 flex-col justify-center gap-1 px-6 py-5 sm:py-6">
          <div className="flex flex-row items-center justify-between">
            <CardTitle>AWS Cost Savings</CardTitle>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              className="text-muted-foreground h-4 w-4"
            >
              <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6" />
            </svg>
          </div>
          <CardDescription>
            Daily savings for EC2 and RDS {getDurationText(startDate, endDate)}
          </CardDescription>
        </div>
        <div className="flex">
          {['ec2', 'rds'].map((key) => {
            const chart = key as keyof typeof chartConfig;
            return (
              <button
                key={chart}
                data-active={activeChart === chart}
                className="data-[active=true]:bg-muted/50 relative flex flex-1 flex-col justify-center gap-1 border-t px-6 py-4 text-left even:border-l sm:border-t-0 sm:border-l sm:px-8 sm:py-6"
                onClick={() => setActiveChart(chart)}
              >
                <span className="text-muted-foreground text-xs">
                  {chartConfig[chart].label}
                </span>
                {isLoading ? (
                  <Skeleton className="h-8 w-[100px]" />
                ) : (
                  <span className="text-lg leading-none font-bold sm:text-3xl">
                    {formatUSD(totalSavings[chart])}
                  </span>
                )}
              </button>
            );
          })}
        </div>
      </CardHeader>
      <CardContent className="px-2 sm:p-6">
        {isLoading ? (
          <Skeleton className="h-[280px] w-full" />
        ) : (
          <ChartContainer
            config={chartConfig}
            className="aspect-auto h-[280px] w-full"
          >
            <BarChart
              accessibilityLayer
              data={chartData}
              margin={{
                left: 12,
                right: 12,
              }}
            >
              <defs>
                <linearGradient id="barGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop
                    offset="0%"
                    stopColor={CHART_COLORS.primary.main}
                    stopOpacity={0.8}
                  />
                  <stop
                    offset="100%"
                    stopColor={CHART_COLORS.primary.muted}
                    stopOpacity={0.5}
                  />
                </linearGradient>
              </defs>
              <CartesianGrid vertical={false} />
              <XAxis
                dataKey="date"
                tickLine={false}
                axisLine={false}
                tickMargin={8}
                minTickGap={32}
                tickFormatter={(value) => {
                  const date = new Date(value);
                  return date.toLocaleDateString('en-US', {
                    month: 'short',
                    day: 'numeric',
                  });
                }}
              />
              <ChartTooltip
                cursor={{ fill: 'var(--chart-background)' }}
                content={({ active, payload, label }) => {
                  if (active && payload && payload.length) {
                    const date = new Date(label).toLocaleDateString('en-US', {
                      month: 'short',
                      day: 'numeric',
                      year: 'numeric',
                    });
                    const activeSavings = payload[0].value as number;
                    const dataPoint = chartData.find((d) => d.date === label);
                    const ec2Savings = dataPoint?.ec2 || 0;
                    const rdsSavings = dataPoint?.rds || 0;
                    const totalSavings = ec2Savings + rdsSavings;
                    return (
                      <div className="bg-background w-[200px] rounded-lg border p-3 shadow-lg">
                        <div>{date}</div>
                        <div>
                          {chartConfig[activeChart].label}:{' '}
                          {formatUSD(activeSavings)}
                        </div>
                        <div>Total Savings: {formatUSD(totalSavings)}</div>
                      </div>
                    );
                  }
                  return null;
                }}
              />
              <Bar
                dataKey={activeChart}
                fill={`url(#barGradient)`}
                radius={[4, 4, 0, 0]}
                barSize={40}
                cursor="pointer"
              />
            </BarChart>
          </ChartContainer>
        )}
      </CardContent>
    </Card>
  );
}
