'use client';

import { useMemo } from 'react';

import { CalendarDateRangePicker } from '@/components/date-range-picker';
import PageContainer from '@/components/layout/page-container';
import { PageHeader } from '@/components/layout/page-header';
import { TabItem, UrlTabs } from '@/components/url-tabs';
import {
  differenceInDays,
  endOfMonth,
  parseISO,
  startOfMonth,
  subDays,
} from 'date-fns';
import { useQueryState } from 'nuqs';
import { DateRange } from 'react-day-picker';

import { OverviewTabContent } from './overview/_components/overview-tab-content';
import UsageTabContent from './usage/_components/usage-tab-content';

// @depercated
export default function OverViewPage() {
  const [dateRange, setDateRange] = useQueryState('dateRange', {
    parse: (value: string) => {
      const [from, to] = value.split(',');
      return {
        from: from ? parseISO(from) : undefined,
        to: to ? parseISO(to) : undefined,
      };
    },
    serialize: (value: DateRange | undefined) => {
      if (!value?.from && !value?.to) return '';
      return `${value.from?.toISOString() || ''},${
        value.to?.toISOString() || ''
      }`;
    },
    defaultValue: {
      from: undefined,
      to: undefined,
    },
  });

  const { defaultEnd, defaultStart } = useMemo(() => {
    const now = new Date();
    return {
      defaultEnd: endOfMonth(now),
      defaultStart: startOfMonth(now),
      now,
    };
  }, []);

  // Calculate date ranges
  const currentStartDate = dateRange?.from
    ? dateRange.from.toISOString()
    : defaultStart.toISOString();
  const currentEndDate = dateRange?.to
    ? dateRange.to.toISOString()
    : defaultEnd.toISOString();

  // Calculate previous period
  const diffDays = differenceInDays(
    new Date(currentEndDate),
    new Date(currentStartDate),
  );
  const previousStartDate = subDays(
    new Date(currentStartDate),
    diffDays + 1,
  ).toISOString();
  const previousEndDate = subDays(new Date(currentStartDate), 1).toISOString();

  // Get period label based on the date range
  const periodLabel = diffDays > 27 ? 'month' : 'day';

  const tabs: TabItem[] = [
    {
      value: 'overview',
      label: 'Overview',
      content: (
        <OverviewTabContent
          previousEndDate={previousEndDate}
          previousStartDate={previousStartDate}
          currentStartDate={currentStartDate}
          currentEndDate={currentEndDate}
          periodLabel={periodLabel}
        />
      ),
    },
    {
      value: 'usage',
      label: 'Usage Analytics',
      content: (
        <UsageTabContent
          currentStartDate={currentStartDate}
          currentEndDate={currentEndDate}
        />
      ),
    },
  ];

  return (
    <PageContainer scrollable>
      <div className="space-y-2">
        <PageHeader
          title="Hi, Welcome back 👋"
          description="View your cloud resource usage and analytics"
          actions={
            <div className="hidden items-center space-x-2 md:flex">
              <CalendarDateRangePicker
                value={dateRange}
                defaultValue={
                  !dateRange.from && !dateRange.to
                    ? {
                        from: defaultStart,
                        to: defaultEnd,
                      }
                    : dateRange
                }
                onChange={(date) =>
                  setDateRange({
                    from: date?.from,
                    to: date?.to,
                  })
                }
              />
              {/* <Button>Download</Button> */}
            </div>
          }
        />
        <UrlTabs tabs={tabs} defaultTab="overview" className="space-y-0" />
      </div>
    </PageContainer>
  );
}
