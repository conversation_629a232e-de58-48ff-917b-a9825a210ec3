import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

import pathsConfig from '@/config/paths.config';
import { UserInfo } from '@/types/common.enum';

import OverViewPage from './overview';

export const metadata = {
  title: 'Dashboard',
};

export default async function page() {
  const accessToken = (await cookies()).get(UserInfo.AccessToken)?.value;

  if (!accessToken) {
    return redirect(pathsConfig.auth.signIn);
  }

  return <OverViewPage />;
}
