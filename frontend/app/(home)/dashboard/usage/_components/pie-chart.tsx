'use client';

import * as React from 'react';

import {
  <PERSON>,
  Label,
  Pie,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Responsive<PERSON>ontainer,
  <PERSON><PERSON><PERSON>,
} from 'recharts';

const THEME_COLORS = [
  'hsl(280, 100%, 60%)', // Bright Purple
  'hsl(325, 100%, 60%)', // Hot Pink
  'hsl(262, 100%, 65%)', // Lavender
  'hsl(292, 100%, 55%)', // Magenta
  'hsl(315, 95%, 65%)', // Light Pink
];

const CustomTooltip = ({ active, payload }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="border-border bg-background/80 rounded-lg border p-3 shadow-lg backdrop-blur-xs">
        <p className="text-foreground font-medium">{payload[0].name}</p>
        <p className="text-muted-foreground">Value: {payload[0].value}</p>
        <p className="text-muted-foreground text-sm">
          {((payload[0].value / payload[0].payload.total) * 100).toFixed(1)}%
        </p>
      </div>
    );
  }
  return null;
};

interface PieChartProps {
  data: Array<{ type: string; value: number }>;
}

export function PieChart({ data }: PieChartProps) {
  const total = React.useMemo(
    () => data.reduce((sum, entry) => sum + entry.value, 0),
    [data],
  );

  return (
    <ResponsiveContainer width="100%" height={400}>
      <RechartsPie className="outline-hidden!">
        <Pie
          data={data.map((item) => ({ ...item, total }))}
          dataKey="value"
          nameKey="type"
          cx="50%"
          cy="50%"
          innerRadius={80}
          outerRadius={120}
          paddingAngle={4}
          // Remove cursor prop
          isAnimationActive={false} // Disable animation to prevent flickering
        >
          {data.map((entry, index) => (
            <Cell
              key={`cell-${index}`}
              fill={THEME_COLORS[index % THEME_COLORS.length]}
              className="outline-hidden! transition-opacity duration-200 hover:opacity-80"
            />
          ))}
          <Label
            content={({ viewBox }) => {
              const { cx, cy } = viewBox as { cx: number; cy: number };
              return (
                <text
                  x={cx}
                  y={cy}
                  textAnchor="middle"
                  dominantBaseline="central"
                >
                  <tspan
                    x={cx}
                    y={cy - 12}
                    className="fill-foreground text-2xl font-bold"
                  >
                    {total.toLocaleString()}
                  </tspan>
                  <tspan
                    x={cx}
                    y={cy + 12}
                    className="fill-muted-foreground text-base"
                  >
                    Total
                  </tspan>
                </text>
              );
            }}
          />
        </Pie>
        <Tooltip
          content={<CustomTooltip />}
          wrapperStyle={{ outline: 'none' }} // Remove tooltip outline
          cursor={false} // Remove cursor highlight
        />
      </RechartsPie>
    </ResponsiveContainer>
  );
}
