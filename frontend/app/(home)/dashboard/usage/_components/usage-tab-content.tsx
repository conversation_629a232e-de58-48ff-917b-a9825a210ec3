'use client';

import { useEffect, useState } from 'react';

import { DailyMessageVolume, TokenDistributionCategory } from '@/client';
import { QuotasService } from '@/client/sdk.gen';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { useUserContext } from '@/features/user/provider/user-provider';
import { DateFormat, formatUtcDate } from '@/lib/date-utils';

import { AreaChart } from './area-chart';
import { QuotaUsageCard } from './quota-usage-card';
import { TokenDistribution } from './token-distribution';

export default function UsageTabContent({
  currentStartDate,
  currentEndDate,
}: {
  currentStartDate: string;
  currentEndDate: string;
}) {
  const { user } = useUserContext();
  const [quotaInfo, setQuotaInfo] = useState<{
    quota_used: number;
    quota_limit: number;
    quota_remaining: number;
    usage_percentage: number;
  }>();

  const [messagesStats, setMessagesStats] = useState<{
    total_messages: number;
    average_response_time: number;
    average_input_tokens_per_message: number;
    average_output_tokens_per_message: number;
    daily_message_volume: Array<DailyMessageVolume>;
    token_distribution_by_message_length: Array<TokenDistributionCategory>;
  }>();

  useEffect(() => {
    const fetchQuotaInfo = async () => {
      try {
        const response = await QuotasService.getQuotaInfo({
          userId: user.id,
        });
        setQuotaInfo(response);
      } catch (error) {
        console.error('Error fetching quota info:', error);
      }
    };

    fetchQuotaInfo();
  }, [user.id]);

  useEffect(() => {
    const fetchMessagesStats = async () => {
      try {
        const response = await QuotasService.getMessagesStatistics({
          startDate: currentStartDate
            ? formatUtcDate(new Date(currentStartDate), DateFormat.ISO_DATE)
            : undefined,
          endDate: currentEndDate
            ? formatUtcDate(new Date(currentEndDate), DateFormat.ISO_DATE)
            : undefined,
        });
        setMessagesStats(response);
      } catch (error) {
        console.error('Error fetching messages stats:', error);
      }
    };

    fetchMessagesStats();
  }, [currentEndDate, currentStartDate]);

  if (!user) {
    return (
      <div className="flex h-full items-center justify-center">
        <div className="text-center">
          <h2 className="mb-2 text-xl font-medium">Loading user data...</h2>
          <p className="text-muted-foreground">
            Please wait while we fetch your information
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-1">
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card className="rounded-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-medium">
              Total Messages
            </CardTitle>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              className="text-muted-foreground h-4 w-4"
            >
              <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
            </svg>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="text-2xl font-bold">
              {messagesStats?.total_messages || 0}
            </div>
            <p className="text-muted-foreground text-xs">
              Total conversations processed
            </p>
          </CardContent>
        </Card>
        <Card className="rounded-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-medium">
              Avg Response Time
            </CardTitle>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              className="text-muted-foreground h-4 w-4"
            >
              <circle cx="12" cy="12" r="10" />
              <polyline points="12 6 12 12 16 14" />
            </svg>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="text-2xl font-bold">
              {(messagesStats?.average_response_time || 0).toFixed(2)}s
            </div>
            <p className="text-muted-foreground text-xs">
              Average processing time
            </p>
          </CardContent>
        </Card>
        <Card className="rounded-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-medium">
              Avg Input Tokens
            </CardTitle>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              className="text-muted-foreground h-4 w-4"
            >
              <path d="M17 6.1H3" />
              <path d="M21 12.1H3" />
              <path d="M15.1 18H3" />
            </svg>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="text-2xl font-bold">
              {(messagesStats?.average_input_tokens_per_message || 0).toFixed(
                0,
              )}
            </div>
            <p className="text-muted-foreground text-xs">
              Tokens per input message
            </p>
          </CardContent>
        </Card>
        <Card className="rounded-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-medium">
              Avg Output Tokens
            </CardTitle>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              className="text-muted-foreground h-4 w-4"
            >
              <path d="M17 6.1H3" />
              <path d="M21 12.1H3" />
              <path d="M15.1 18H3" />
            </svg>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="text-2xl font-bold">
              {(messagesStats?.average_output_tokens_per_message || 0).toFixed(
                0,
              )}
            </div>
            <p className="text-muted-foreground text-xs">
              Tokens per output message
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Quota Usage Section */}
      <div className="grid gap-6">
        <QuotaUsageCard quotaInfo={quotaInfo} />
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-7">
        <Card className="col-span-4 rounded-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle>Daily Message Volume</CardTitle>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              className="text-muted-foreground h-4 w-4"
            >
              <path d="M3 3v18h18" />
              <path d="m19 9-5 5-4-4-3 3" />
            </svg>
          </CardHeader>
          <CardContent className="pt-0">
            <AreaChart
              data={(messagesStats?.daily_message_volume || []).map((item) => ({
                date: item.date,
                value: item.message_count,
              }))}
            />
          </CardContent>
        </Card>
        <Card className="col-span-3 rounded-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <div>
              <CardTitle>Message Distribution</CardTitle>
              <CardDescription>By number of tokens</CardDescription>
            </div>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              className="text-muted-foreground h-4 w-4"
            >
              <path d="M11 20a20 20 0 0 0 2 0c6.6-1 9-6 9-11a8 8 0 0 0-16 0c0 5 2.3 10 9 11" />
              <circle cx="12" cy="9" r="4" />
            </svg>
          </CardHeader>
          <CardContent className="pt-0">
            <TokenDistribution
              data={(
                messagesStats?.token_distribution_by_message_length || []
              ).map((item) => ({
                category: item.category,
                percentage: item.percentage,
              }))}
            />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
