'use client';

import { format, isValid, parseISO } from 'date-fns';
import {
  Area,
  CartesianGrid,
  AreaChart as Re<PERSON>rtsA<PERSON>,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts';

import { CHART_COLORS, CHART_STYLES } from './constants';

interface AreaChartProps {
  data: Array<{ date: string; value: number }>;
  // Add dateFormat prop to allow customization
  dateFormat?: string;
  // Optional period to determine automatic formatting
  period?: 'daily' | 'monthly' | 'yearly';
}

export function AreaChart({
  data,
  dateFormat = 'MMM d, yyyy',
  period = 'daily',
}: AreaChartProps) {
  // Helper function to format dates
  const formatDate = (dateStr: string) => {
    const date = parseISO(dateStr);
    if (!isValid(date)) return dateStr;

    // Automatic format based on period
    const formatString =
      period === 'yearly'
        ? 'yyyy'
        : period === 'monthly'
          ? 'MMM yyyy'
          : dateFormat;

    return format(date, formatString);
  };

  return (
    <ResponsiveContainer width="100%" height={350}>
      <RechartsArea data={data}>
        <defs>
          <linearGradient id="colorValue" x1="0" y1="0" x2="0" y2="1">
            <stop
              offset="5%"
              stopColor={CHART_COLORS.primary.main}
              stopOpacity={0.2}
            />
            <stop
              offset="95%"
              stopColor={CHART_COLORS.primary.main}
              stopOpacity={0}
            />
          </linearGradient>
        </defs>
        <CartesianGrid {...CHART_STYLES.grid} />
        <XAxis
          dataKey="date"
          {...CHART_STYLES.text}
          tickMargin={10}
          // Add tick formatter
          tickFormatter={formatDate}
        />
        <YAxis {...CHART_STYLES.text} tickMargin={10} />
        <Tooltip
          {...CHART_STYLES.tooltip}
          // Customize tooltip label
          labelFormatter={(label) => formatDate(label as string)}
        />
        <Area
          type="monotone"
          dataKey="value"
          stroke={CHART_COLORS.primary.main}
          fill="url(#colorValue)"
          strokeWidth={2}
          dot={{ fill: CHART_COLORS.primary.main }}
          activeDot={{ r: 6, strokeWidth: 0 }}
        />
      </RechartsArea>
    </ResponsiveContainer>
  );
}
