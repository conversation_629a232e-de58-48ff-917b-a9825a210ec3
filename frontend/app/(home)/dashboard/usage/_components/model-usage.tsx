import { Card, CardContent } from '@/components/ui/card';

interface ModelUsageBreakdownProps {
  data: Array<{
    model: string;
    usage: number;
    successRate: number;
    avgTokens: number;
  }>;
}

export function ModelUsageBreakdown({ data }: ModelUsageBreakdownProps) {
  return (
    <div className="space-y-4">
      {data.map((model) => (
        <Card key={model.model}>
          <CardContent className="p-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <p className="text-sm font-medium">{model.model}</p>
                <p className="text-sm font-medium">
                  {((model.usage / 12500) * 100).toFixed(1)}% of total
                </p>
              </div>
              <div className="text-muted-foreground flex items-center justify-between text-xs">
                <div>
                  <p>{model.usage.toLocaleString()} messages</p>
                  <p>{model.successRate}% success rate</p>
                </div>
                <div className="text-right">
                  <p>{model.avgTokens} avg tokens/msg</p>
                  <p>
                    {(model.usage * model.avgTokens).toLocaleString()} total
                    tokens
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
