'use client';

import { useState } from 'react';

import { cn } from '@/lib/utils';
import {
  Bar,
  BarChart,
  CartesianGrid,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts';

import { CHART_COLORS, CHART_STYLES } from './constants';

interface TokenDistributionProps {
  data: Array<{
    category: string;
    percentage: number;
  }>;
}

export function TokenDistribution({ data }: TokenDistributionProps) {
  const [hoveredCategory, setHoveredCategory] = useState<string | null>(null);

  // Custom tooltip content
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-popover text-popover-foreground rounded-lg border p-3 shadow-lg backdrop-blur-xs">
          <p className="text-foreground font-medium">
            {payload[0].payload.category}
          </p>
          <p className="text-muted-foreground text-sm">
            {payload[0].value}% of messages
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <div className="space-y-6">
      <ResponsiveContainer width="100%" height={200}>
        <BarChart
          data={data}
          onMouseMove={(e) => {
            if (e.activePayload) {
              setHoveredCategory(e.activePayload[0].payload.category);
            }
          }}
          onMouseLeave={() => setHoveredCategory(null)}
        >
          <defs>
            <linearGradient id="barGradient" x1="0" y1="0" x2="0" y2="1">
              <stop
                offset="0%"
                stopColor={CHART_COLORS.primary.main}
                stopOpacity={0.8}
              />
              <stop
                offset="100%"
                stopColor={CHART_COLORS.primary.muted}
                stopOpacity={0.5}
              />
            </linearGradient>
          </defs>
          <CartesianGrid {...CHART_STYLES.grid} />
          <XAxis dataKey="category" {...CHART_STYLES.text} tickMargin={8} />
          <YAxis
            {...CHART_STYLES.text}
            tickMargin={8}
            tickFormatter={(value) => `${value}%`}
          />
          <Tooltip
            content={<CustomTooltip />}
            cursor={{
              fill: 'color-mix(in srgb, var(--color-muted), transparent 90%)',
            }}
            wrapperStyle={{ outline: 'none' }}
          />
          <Bar
            dataKey="percentage"
            fill="url(#barGradient)"
            radius={[4, 4, 0, 0]}
            barSize={40}
            cursor="pointer"
          />
        </BarChart>
      </ResponsiveContainer>

      <div className="grid grid-cols-3 gap-4">
        {data.map((item) => (
          <div
            key={item.category}
            className={cn(
              'hover:bg-accent rounded-lg border p-3 transition-colors duration-200',
              hoveredCategory === item.category && 'bg-accent',
            )}
          >
            <div className="mb-2 flex items-center justify-between">
              <div className="bg-primary h-2 w-2 rounded-full" />
              <span className="text-lg font-medium">{item.percentage}%</span>
            </div>
            <div className="text-muted-foreground text-sm">{item.category}</div>
          </div>
        ))}
      </div>
    </div>
  );
}
