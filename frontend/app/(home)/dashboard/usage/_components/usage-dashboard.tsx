'use client';

import { useEffect, useState } from 'react';

import { DailyMessageVolume, TokenDistributionCategory } from '@/client';
import { QuotasService } from '@/client/sdk.gen';
import { CalendarDateRangePicker } from '@/components/date-range-picker';
import PageContainer from '@/components/layout/page-container';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useUserContext } from '@/features/user/provider/user-provider';
import { UserInfo } from '@/types/common.enum';
import { format } from 'date-fns';
import clientCookie from 'js-cookie';
import { DateRange } from 'react-day-picker';

import { Area<PERSON>hart } from './area-chart';
import { Pie<PERSON><PERSON> } from './pie-chart';
import { QuotaUsageCard } from './quota-usage-card';
import { TokenDistribution } from './token-distribution';

// @depercated
export default function AIMonitoringDashboard() {
  const { user, workspaces } = useUserContext();
  const [dateRange, setDateRange] = useState<DateRange | undefined>();
  const [selectedWorkspaceId, setSelectedWorkspaceId] = useState<string>('');
  const [quotaInfo, setQuotaInfo] = useState<{
    quota_used: number;
    quota_limit: number;
    quota_remaining: number;
    usage_percentage: number;
  }>();
  const [usageStats, setUsageStats] = useState<{
    input_tokens: number;
    output_tokens: number;
    total_tokens: number;
    quota_limit: number;
    quota_used: number;
    quota_remaining: number;
    usage_percentage: number;
    daily_token_usage: Array<{ date: string; total_tokens: number }>;
    agent_type_stats: Array<{ agent_type: string; total_tokens: number }>;
  }>();
  const [messagesStats, setMessagesStats] = useState<{
    total_messages: number;
    average_response_time: number;
    average_input_tokens_per_message: number;
    average_output_tokens_per_message: number;
    daily_message_volume: Array<DailyMessageVolume>;
    token_distribution_by_message_length: Array<TokenDistributionCategory>;
  }>();

  useEffect(() => {
    if (!user) return;

    const fetchData = async () => {
      try {
        const response = await QuotasService.getUsageStatistics({
          userId: user.id,
          startDate: dateRange?.from
            ? format(dateRange.from, 'yyyy-MM-dd')
            : undefined,
          endDate: dateRange?.to
            ? format(dateRange.to, 'yyyy-MM-dd')
            : undefined,
        });
        setUsageStats(response);
      } catch (error) {
        console.error('Error fetching usage stats:', error);
      }
    };

    fetchData();
  }, [dateRange, user]);

  useEffect(() => {
    if (!user) return;

    const fetchQuotaInfo = async () => {
      try {
        const response = await QuotasService.getQuotaInfo({
          userId: user.id,
        });
        setQuotaInfo(response);
      } catch (error) {
        console.error('Error fetching quota info:', error);
      }
    };

    fetchQuotaInfo();
  }, [user]);

  useEffect(() => {
    const fetchMessagesStats = async () => {
      try {
        const response = await QuotasService.getMessagesStatistics({
          startDate: dateRange?.from
            ? format(dateRange.from, 'yyyy-MM-dd')
            : undefined,
          endDate: dateRange?.to
            ? format(dateRange.to, 'yyyy-MM-dd')
            : undefined,
        });
        setMessagesStats(response);
      } catch (error) {
        console.error('Error fetching messages stats:', error);
      }
    };

    fetchMessagesStats();
  }, [dateRange]);

  // Handle workspace change
  const handleWorkspaceChange = (workspaceId: string) => {
    if (workspaceId) {
      setSelectedWorkspaceId(workspaceId);
      clientCookie.set(UserInfo.WorkspacesID, workspaceId);
    }
  };

  if (!user) {
    return (
      <PageContainer scrollable>
        <div className="flex h-full items-center justify-center">
          <div className="text-center">
            <h2 className="mb-2 text-xl font-medium">Loading user data...</h2>
            <p className="text-muted-foreground">
              Please wait while we fetch your information
            </p>
          </div>
        </div>
      </PageContainer>
    );
  }

  return (
    <PageContainer scrollable>
      <div className="space-y-2">
        <div className="flex items-center justify-between space-y-2">
          <h2 className="text-3xl font-bold tracking-tight">Usage Analytics</h2>
          <div className="hidden items-center space-x-2 md:flex">
            <CalendarDateRangePicker
              value={dateRange}
              onChange={(newDateRange) => {
                // If both dates are cleared, set to undefined
                if (!newDateRange?.from && !newDateRange?.to) {
                  setDateRange(undefined);
                } else {
                  setDateRange(newDateRange);
                }
              }}
            />
          </div>
        </div>
        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            {/* <TabsTrigger value="detailed">Token Usage</TabsTrigger> */}
          </TabsList>
          <TabsContent value="overview" className="space-y-4">
            {/* Workspace Filter */}
            {workspaces.length > 0 && (
              <div className="mb-4 flex justify-end">
                <Select
                  value={selectedWorkspaceId}
                  onValueChange={handleWorkspaceChange}
                >
                  <SelectTrigger className="w-[250px]">
                    <SelectValue placeholder="Select workspace" />
                  </SelectTrigger>
                  <SelectContent>
                    {workspaces.map(
                      (workspace) =>
                        workspace.id && (
                          <SelectItem key={workspace.id} value={workspace.id}>
                            {workspace.name}
                          </SelectItem>
                        ),
                    )}
                  </SelectContent>
                </Select>
              </div>
            )}

            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    Total Messages
                  </CardTitle>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    className="text-muted-foreground h-4 w-4"
                  >
                    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
                  </svg>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {messagesStats?.total_messages || 0}
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    Avg Response Time
                  </CardTitle>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    className="text-muted-foreground h-4 w-4"
                  >
                    <circle cx="12" cy="12" r="10" />
                    <polyline points="12 6 12 12 16 14" />
                  </svg>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {(messagesStats?.average_response_time || 0).toFixed(2)}s
                  </div>
                </CardContent>
              </Card>
              <QuotaUsageCard quotaInfo={quotaInfo} />
              {/* <Card>
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium">
                                        Avg Input Tokens/Message
                                    </CardTitle>
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        stroke="currentColor"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth="2"
                                        className="h-4 w-4 text-muted-foreground"
                                    >
                                        <path d="M17 6.1H3" />
                                        <path d="M21 12.1H3" />
                                        <path d="M15.1 18H3" />
                                    </svg>
                                </CardHeader>
                                <CardContent>
                                    <div className="text-2xl font-bold">{(messagesStats?.average_input_tokens_per_message || 0).toFixed(1)}</div>
                                </CardContent>
                            </Card>
                            <Card>
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <CardTitle className="text-sm font-medium">
                                        Avg Output Tokens/Message
                                    </CardTitle>
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        stroke="currentColor"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth="2"
                                        className="h-4 w-4 text-muted-foreground"
                                    >
                                        <path d="M17 6.1H3" />
                                        <path d="M21 12.1H3" />
                                        <path d="M15.1 18H3" />
                                    </svg>
                                </CardHeader>
                                <CardContent>
                                    <div className="text-2xl font-bold">{(messagesStats?.average_output_tokens_per_message || 0).toFixed(1)}</div>
                                </CardContent>
                            </Card> */}
            </div>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-7">
              <Card className="col-span-4">
                <CardHeader>
                  <CardTitle>Daily Message Volume</CardTitle>
                </CardHeader>
                <CardContent>
                  <AreaChart
                    data={(messagesStats?.daily_message_volume || []).map(
                      (item) => ({
                        date: item.date,
                        value: item.message_count,
                      }),
                    )}
                  />
                </CardContent>
              </Card>
              <Card className="col-span-3">
                <CardHeader>
                  <CardTitle>Message Distribution</CardTitle>
                  <CardDescription>By number of tokens</CardDescription>
                </CardHeader>
                <CardContent>
                  <TokenDistribution
                    data={(
                      messagesStats?.token_distribution_by_message_length || []
                    ).map((item) => ({
                      category: item.category,
                      percentage: item.percentage,
                    }))}
                  />
                </CardContent>
              </Card>
            </div>
          </TabsContent>
          <TabsContent value="detailed" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    Input Tokens
                  </CardTitle>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    className="text-muted-foreground h-4 w-4"
                  >
                    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z" />
                  </svg>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {usageStats?.input_tokens?.toLocaleString() || '0'}
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    Output Tokens
                  </CardTitle>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    className="text-muted-foreground h-4 w-4"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14" />
                    <polyline points="22 4 12 14.01 9 11.01" />
                  </svg>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {usageStats?.output_tokens?.toLocaleString() || '0'}
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    Total Tokens
                  </CardTitle>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    className="text-muted-foreground h-4 w-4"
                  >
                    <path d="M17 6.1H3" />
                    <path d="M21 12.1H3" />
                    <path d="M15.1 18H3" />
                  </svg>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {usageStats?.total_tokens?.toLocaleString() || '0'}
                  </div>
                </CardContent>
              </Card>
            </div>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
              <Card className="col-span-4">
                <CardHeader>
                  <CardTitle>Token Usage Over Time</CardTitle>
                </CardHeader>
                <CardContent className="pl-2">
                  <AreaChart
                    data={(usageStats?.daily_token_usage || []).map((item) => ({
                      date: item.date,
                      value: item.total_tokens,
                    }))}
                  />
                </CardContent>
              </Card>
              <Card className="col-span-3">
                <CardHeader>
                  <CardTitle>Token Distribution</CardTitle>
                </CardHeader>
                <PieChart
                  data={(usageStats?.agent_type_stats || []).map((item) => ({
                    type: item.agent_type,
                    value: item.total_tokens,
                  }))}
                />
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </PageContainer>
  );
}
