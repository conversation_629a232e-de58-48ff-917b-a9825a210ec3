// bar-chart.tsx
'use client';

import {
  <PERSON>,
  Cartes<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from 'recharts';

import { CHART_COLORS, CHART_STYLES } from './constants';

// bar-chart.tsx

// bar-chart.tsx

interface BarChartProps {
  data: Array<{ model: string; usage: number }>;
}

export function BarChart({ data }: BarChartProps) {
  return (
    <ResponsiveContainer width="100%" height={350}>
      <RechartsBar data={data}>
        <CartesianGrid {...CHART_STYLES.grid} />
        <XAxis dataKey="model" {...CHART_STYLES.text} tickMargin={10} />
        <YAxis {...CHART_STYLES.text} tickMargin={10} />
        <Tooltip {...CHART_STYLES.tooltip} />
        <Bar
          dataKey="usage"
          fill={CHART_COLORS.secondary.main}
          radius={[4, 4, 0, 0]}
          barSize={40}
          cursor="pointer"
        />
      </RechartsBar>
    </ResponsiveContainer>
  );
}
