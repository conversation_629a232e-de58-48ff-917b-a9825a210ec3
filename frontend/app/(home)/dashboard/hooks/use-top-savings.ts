'use client';

import { ReportsService } from '@/client';
import { useQuery } from '@tanstack/react-query';

export function useTopSavings(startDate?: string, endDate?: string) {
  return useQuery({
    queryKey: ['top-savings', { startDate, endDate }],
    queryFn: () =>
      ReportsService.getTopPotentialSavings({
        limit: 5,
        ...(startDate && { startDate }),
        ...(endDate && { endDate }),
      }),
  });
}
