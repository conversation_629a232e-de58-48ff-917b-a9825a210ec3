'use client';

import { ReportsService } from '@/client';
import { useQuery } from '@tanstack/react-query';

export function useResourceSavings(startDate?: string, endDate?: string) {
  return useQuery({
    queryKey: ['resource-savings', { startDate, endDate }],
    queryFn: () =>
      ReportsService.getSavingsByResource({
        ...(startDate && { startDate }),
        ...(endDate && { endDate }),
      }),
  });
}
