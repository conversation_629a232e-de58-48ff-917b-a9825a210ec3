'use client';

import { ReportsService } from '@/client';
import { useQuery } from '@tanstack/react-query';

export const useSavingsSummary = ({
  startDate,
  endDate,
  previousStartDate,
  previousEndDate,
}: {
  startDate?: string;
  endDate?: string;
  previousStartDate?: string;
  previousEndDate?: string;
}) => {
  return useQuery({
    queryKey: [
      'savings-summary',
      startDate,
      endDate,
      previousStartDate,
      previousEndDate,
    ],
    queryFn: () =>
      ReportsService.getSavingsSummary({
        startDate,
        endDate,
        previousStartDate,
        previousEndDate,
      }),
  });
};
