'use client';

import { Icons } from '@/components/icons';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

export default function IntegrationsPage() {
  return (
    <div className="flex-1 space-y-4 p-4 pt-6 md:p-8">
      <div className="flex items-center justify-between space-y-2">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Integrations</h2>
          <p className="text-muted-foreground">
            Connect your workspace with external services
          </p>
        </div>
      </div>
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card className="border-2 transition-colors duration-200 hover:border-[#36C5F0]">
          <CardHeader className="space-y-1">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <div className="h-8 w-8">
                  <Icons.slack className="h-full w-full" />
                </div>
                <CardTitle className="text-2xl">Slack</CardTitle>
              </div>
              {/* <Badge
                variant="outline"
                className="text-sm bg-[#2EB67D]/10 text-[#2EB67D] border-[#2EB67D]/20"
              >
                Coming Soon
              </Badge> */}
            </div>
            <CardDescription className="pt-3">
              Connect your workspace with Slack to enhance team collaboration
              and receive real-time notifications
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col space-y-4">
              <div className="space-y-2">
                <p className="text-sm font-medium">Features</p>
                <ul className="text-muted-foreground grid gap-2 text-sm">
                  <li className="flex items-center">
                    <div className="mr-2 h-1 w-1 rounded-full bg-[#E01E5A]" />
                    Real-time cost alerts
                  </li>
                  <li className="flex items-center">
                    <div className="mr-2 h-1 w-1 rounded-full bg-[#36C5F0]" />
                    Automated reports
                  </li>
                  <li className="flex items-center">
                    <div className="mr-2 h-1 w-1 rounded-full bg-[#2EB67D]" />
                    Team notifications
                  </li>
                </ul>
              </div>
              <Button
                variant="outline"
                className="w-full transition-colors hover:bg-[#611f47] hover:text-white"
                onClick={() => {
                  // Placeholder link - to be added later
                  window.open(
                    process.env.NEXT_PUBLIC_API_URL + '/slack/install',
                  );
                }}
              >
                <Icons.slack className="mr-2 h-5 w-5" />
                Connect with Slack
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
