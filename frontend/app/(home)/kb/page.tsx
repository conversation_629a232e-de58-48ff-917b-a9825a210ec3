import PageContainer from '@/components/layout/page-container';
import { searchParamsCache } from '@/lib/searchparams';

import { KBPageWrapper } from './components/kb-page-wrapper';

export const metadata = {
  title: 'Knowledge Base',
};

type PageProps = {
  searchParams: Promise<{
    page: string;
    limit: string;
    q: string;
    access_level: string;
    usage_mode: string;
  }>;
};

export default async function Page(props: PageProps) {
  const searchParams = await props.searchParams;
  searchParamsCache.parse(searchParams);

  return (
    <PageContainer scrollable={false}>
      <KBPageWrapper
        page={searchParams.page ? parseInt(searchParams.page) : undefined}
        limit={searchParams.limit ? parseInt(searchParams.limit) : undefined}
        q={searchParams.q ? searchParams.q : undefined}
        access_level={
          searchParams.access_level ? searchParams.access_level : undefined
        }
        usage_mode={
          searchParams.usage_mode ? searchParams.usage_mode : undefined
        }
      />
    </PageContainer>
  );
}
