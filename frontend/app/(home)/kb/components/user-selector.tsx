'use client';

import { useEffect, useState } from 'react';

import { KnowledgeBaseService } from '@/client';
import type { UserPublic } from '@/client';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  FormControl,
  FormDescription,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { CacheKey } from '@/components/utils/cache-key';
import { useQueryClient } from '@tanstack/react-query';
import { Loader2, Shield, X } from 'lucide-react';
import { useFormContext } from 'react-hook-form';
import { toast } from 'sonner';

interface UserSelectorProps {
  className?: string;
  show: boolean;
}

interface User {
  id: string;
  email: string;
  full_name: string | null;
}

export function UserSelector({ className, show }: UserSelectorProps) {
  const form = useFormContext();
  const queryClient = useQueryClient();
  const [availableUsers, setAvailableUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Get current user information
  const currentUser = queryClient.getQueryData<UserPublic>([
    CacheKey.CurrentUser,
  ]);

  // Get the current allowed users from the form
  const allowedUsers: string[] = form.watch('allowed_users') || [];

  // Auto-include current user when switching to shared mode
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (
        name === 'access_level' &&
        value.access_level === 'shared' &&
        currentUser
      ) {
        const currentAllowedUsers = value.allowed_users || [];
        if (!currentAllowedUsers.includes(currentUser.id)) {
          // Automatically add current user when switching to shared mode
          form.setValue('allowed_users', [
            ...currentAllowedUsers,
            currentUser.id,
          ]);
        }
      }
    });
    return () => subscription.unsubscribe();
  }, [form, currentUser]);

  // Fetch available users from the API
  useEffect(() => {
    if (show) {
      setLoading(true);
      setError(null);

      // Direct API call to avoid any routing issues
      KnowledgeBaseService.getAvailableUsers()
        .then((response) => {
          // The response should have a 'data' field with the users array
          if (response && response.data && Array.isArray(response.data)) {
            setAvailableUsers(response.data);
          } else {
            console.warn(
              '⚠️ UserSelector: Unexpected response structure:',
              response,
            );
            setAvailableUsers([]);
            setError('Unexpected response format');
          }
        })
        .catch((error) => {
          console.error(
            '❌ UserSelector: Error fetching available users:',
            error,
          );
          setError(error?.message || 'Failed to fetch available users');
          toast.error('Failed to fetch available users');
          setAvailableUsers([]);
        })
        .finally(() => {
          setLoading(false);
        });
    } else {
      // Reset state when not showing
      setAvailableUsers([]);
      setError(null);
    }
  }, [show]);

  // Toggle user selection from workspace list
  const handleToggleUser = (user: User) => {
    // Prevent current user from unchecking themselves
    if (currentUser && user.id === currentUser.id) {
      toast.error('You cannot remove yourself from the allowed users list');
      return;
    }

    // Use user ID as the identifier for consistency with backend
    const userId = user.id;
    if (allowedUsers.includes(userId)) {
      // Remove user if already selected
      const newUsers = allowedUsers.filter((id: string) => id !== userId);
      form.setValue('allowed_users', newUsers);
    } else {
      // Add user if not selected
      const newUsers = [...allowedUsers, userId];
      form.setValue('allowed_users', newUsers);
    }
  };

  // Remove a user
  const handleRemoveUser = (userToRemove: string) => {
    // Prevent current user from removing themselves
    if (currentUser && userToRemove === currentUser.id) {
      toast.error('You cannot remove yourself from the allowed users list');
      return;
    }

    const newUsers = allowedUsers.filter(
      (user: string) => user !== userToRemove,
    );
    form.setValue('allowed_users', newUsers);
  };

  if (!show) {
    return null;
  }

  return (
    <FormItem className={className}>
      <FormLabel>Share with Users</FormLabel>
      <FormControl>
        <div className="space-y-4">
          {loading ? (
            <div className="bg-muted/30 flex items-center justify-center rounded-lg border-2 border-dashed py-8">
              <Loader2 className="text-muted-foreground h-6 w-6 animate-spin" />
              <span className="text-muted-foreground ml-2 text-sm">
                Loading workspace users...
              </span>
            </div>
          ) : error ? (
            <div className="border-destructive/20 bg-destructive/10 text-destructive rounded-lg border py-6 text-center text-sm">
              <p className="font-medium">{error}</p>
              <Button
                variant="outline"
                size="sm"
                className="mt-3"
                onClick={() => {
                  setError(null);
                  // Trigger refetch by toggling loading
                  setLoading(true);
                  setTimeout(() => {
                    KnowledgeBaseService.getAvailableUsers()
                      .then((response) => {
                        if (
                          response &&
                          response.data &&
                          Array.isArray(response.data)
                        ) {
                          setAvailableUsers(response.data);
                        } else {
                          setAvailableUsers([]);
                          setError('Unexpected response format');
                        }
                      })
                      .catch((error) => {
                        setError(
                          error?.message || 'Failed to fetch available users',
                        );
                        setAvailableUsers([]);
                      })
                      .finally(() => {
                        setLoading(false);
                      });
                  }, 100);
                }}
              >
                Retry
              </Button>
            </div>
          ) : availableUsers.length === 0 ? (
            <div className="bg-muted/30 text-muted-foreground rounded-lg border-2 border-dashed py-8 text-center">
              <p>No other users found in this workspace</p>
            </div>
          ) : (
            <ScrollArea className="bg-background h-64 rounded-lg border p-3">
              <div className="space-y-2">
                {availableUsers.map((user) => {
                  const isCurrentUser =
                    currentUser && user.id === currentUser.id;
                  const isChecked = allowedUsers.includes(user.id);

                  return (
                    <div
                      key={user.id}
                      className="hover:bg-muted/50 flex items-center space-x-3 rounded-lg p-2 transition-colors"
                    >
                      <Checkbox
                        id={`user-${user.id}`}
                        checked={isChecked}
                        onCheckedChange={() => handleToggleUser(user)}
                        disabled={isCurrentUser && isChecked} // Prevent unchecking self
                      />
                      <label
                        htmlFor={`user-${user.id}`}
                        className="flex grow cursor-pointer flex-col text-sm"
                      >
                        <div className="flex items-center gap-2">
                          <span className="font-medium">
                            {user.full_name || 'Unnamed User'}
                          </span>
                          {isCurrentUser && (
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Shield className="h-3 w-3 text-blue-600 dark:text-blue-400" />
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>You (owner)</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          )}
                        </div>
                        <span className="text-muted-foreground text-xs">
                          {user.email}
                        </span>
                      </label>
                    </div>
                  );
                })}
              </div>
            </ScrollArea>
          )}
        </div>
      </FormControl>

      <div className="mt-4 flex min-h-8 flex-wrap gap-2">
        {allowedUsers.length === 0 ? (
          <span className="text-muted-foreground text-sm">
            No users added yet
          </span>
        ) : (
          allowedUsers.map((userId: string) => {
            // Find the user details by ID
            const userDetails = availableUsers.find((u) => u.id === userId);
            const isCurrentUser = currentUser && userId === currentUser.id;

            // Fallback to current user data if not found in available users
            const finalUserDetails =
              userDetails || (isCurrentUser ? currentUser : null);

            const displayName = finalUserDetails?.full_name || 'Unknown User';
            const displayEmail = finalUserDetails?.email || '';
            const displayText = `${displayName} (${displayEmail})`;

            return (
              <Badge
                variant="secondary"
                key={userId}
                className={`max-w-xs ${isCurrentUser ? 'border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950' : ''}`}
              >
                <div className="flex items-center gap-1">
                  <span className="truncate">{displayText}</span>
                  {isCurrentUser && (
                    <Shield className="h-3 w-3 shrink-0 text-blue-600 dark:text-blue-400" />
                  )}
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  className="ml-2 h-4 w-4 p-0 hover:bg-transparent"
                  onClick={() => handleRemoveUser(userId)}
                  disabled={isCurrentUser} // Prevent removing self
                >
                  <X
                    className={`h-3 w-3 ${isCurrentUser ? 'text-muted-foreground opacity-50' : 'text-muted-foreground hover:text-foreground'}`}
                  />
                </Button>
              </Badge>
            );
          })
        )}
      </div>

      <FormDescription>
        Select users from your workspace who will have access to this knowledge
        base
      </FormDescription>
      <FormMessage />
    </FormItem>
  );
}
