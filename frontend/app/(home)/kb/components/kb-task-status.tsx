import { useEffect, useRef, useState } from 'react';

import {
  KnowledgeBaseService,
  app__schemas__kb__TaskStatusResponse,
} from '@/client';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { motion } from 'framer-motion';
import { CheckCircle, Loader2, XCircle } from 'lucide-react';

interface KBTaskStatusProps {
  taskId: string;
  onComplete?: () => void;
  onError?: (error: string) => void;
}

export function KBTaskStatus({
  taskId,
  onComplete,
  onError,
}: KBTaskStatusProps) {
  const [taskStatus, setTaskStatus] =
    useState<app__schemas__kb__TaskStatusResponse | null>(null);
  const [isPolling, setIsPolling] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Use refs to prevent stale closures in setInterval and ensure single polling instance
  const taskIdRef = useRef(taskId);
  const isPollingRef = useRef(isPolling);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const errorCountRef = useRef(0);

  // Update the refs when props/state change
  useEffect(() => {
    taskIdRef.current = taskId;
    isPollingRef.current = isPolling;
  }, [taskId, isPolling]);

  // Poll task status
  useEffect(() => {
    if (!taskId) return;

    // Clear any existing interval to prevent duplicate polling
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }

    const pollStatus = async () => {
      if (!isPollingRef.current || !taskIdRef.current) return;

      try {
        const response = await KnowledgeBaseService.getTaskStatus({
          taskId: taskIdRef.current,
        });
        setTaskStatus(response);
        setError(null);
        errorCountRef.current = 0;

        // Normalize status to uppercase for consistency with backend
        const status = response.status?.toUpperCase();

        // Only stop polling if we have a definite end state
        if (status === 'SUCCESS' || status === 'COMPLETED') {
          setIsPolling(false);
          if (onComplete) onComplete();
        } else if (status === 'FAILED' || status === 'FAILURE') {
          setIsPolling(false);
          if (onError) onError(response.status_message || 'Task failed');
        } else if (status === 'CANCELLED') {
          setIsPolling(false);
        }
        // Continue polling for IN_PROGRESS, PENDING, or any other status
      } catch (err) {
        console.error('Error polling task status:', err);
        errorCountRef.current += 1;

        // Only set error message after multiple consecutive errors
        if (errorCountRef.current > 3) {
          setError('Failed to fetch task status. Retrying...');
        }

        // Stop polling after too many consecutive errors
        if (errorCountRef.current > 10) {
          setIsPolling(false);
          if (onError)
            onError('Task status monitoring failed after multiple attempts');
        }
      }
    };

    // Poll immediately
    pollStatus();

    // Then set up interval (using 2.5s instead of 2s to avoid rate limiting)
    intervalRef.current = setInterval(pollStatus, 2500);

    // Clean up the interval when component unmounts or taskId changes
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [taskId, onComplete, onError]); // Only re-run when taskId or callbacks change

  const getStatusData = () => {
    if (!taskStatus)
      return { color: 'bg-muted-foreground/20', icon: null, label: 'Unknown' };

    const status = taskStatus.status?.toUpperCase();

    switch (status) {
      case 'COMPLETED':
      case 'SUCCESS':
        return {
          color: 'bg-green-500',
          icon: <CheckCircle className="h-4 w-4" />,
          label: 'Completed',
          variant: 'ghost-success',
        };
      case 'FAILED':
      case 'FAILURE':
        return {
          color: 'bg-red-500',
          icon: <XCircle className="h-4 w-4" />,
          label: 'Failed',
          variant: 'ghost-destructive',
        };
      case 'CANCELLED':
        return {
          color: 'bg-amber-500',
          icon: <XCircle className="h-4 w-4" />,
          label: 'Cancelled',
          variant: 'warning',
        };
      case 'IN_PROGRESS':
      case 'PROGRESS':
        return {
          color: 'bg-blue-500',
          icon: <Loader2 className="h-4 w-4 animate-spin" />,
          label: 'In Progress',
          variant: 'ghost-info',
        };
      case 'PENDING':
        return {
          color: 'bg-blue-500/50',
          icon: null,
          label: 'Pending',
          variant: 'outline',
        };
      default:
        return {
          color: 'bg-muted-foreground/20',
          icon: null,
          label: status || 'Unknown',
          variant: 'outline',
        };
    }
  };

  const handleRetry = () => {
    setError(null);
    errorCountRef.current = 0;
    setIsPolling(true);
  };

  const statusData = getStatusData();

  return (
    <motion.div
      className="bg-card flex flex-col gap-3 rounded-lg border p-3 shadow-xs"
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className="flex items-center justify-between">
        <Badge
          variant={statusData.variant as any}
          className="flex items-center gap-1 py-1 pr-2 pl-1.5"
        >
          {statusData.icon}
          <span>{statusData.label}</span>
        </Badge>
        <div className="text-muted-foreground text-xs">
          {taskId.substring(0, 8)}
        </div>
      </div>

      {error ? (
        <div className="mt-1 flex items-center gap-2">
          <div className="text-destructive grow text-xs">{error}</div>
          <Button
            size="sm"
            variant="outline"
            onClick={handleRetry}
            className="h-7 px-2 text-xs"
          >
            Retry
          </Button>
        </div>
      ) : (
        <>
          {taskStatus?.status_message && (
            <div className="text-muted-foreground mt-1 line-clamp-2 text-xs">
              {taskStatus.status_message}
            </div>
          )}

          <div className="flex flex-col gap-1">
            <div className="bg-muted relative h-2 w-full overflow-hidden rounded-full">
              <Progress
                value={taskStatus?.progress || 0}
                className={`h-full ${statusData.color} transition-all`}
              />
              {['PROGRESS', 'IN_PROGRESS'].includes(
                taskStatus?.status?.toUpperCase() || '',
              ) && (
                <motion.div
                  className="absolute inset-0 bg-linear-to-r from-transparent via-white/30 to-transparent"
                  animate={{ x: ['0%', '100%'] }}
                  transition={{
                    repeat: Infinity,
                    duration: 1.5,
                    ease: 'linear',
                  }}
                />
              )}
            </div>
            <div className="flex justify-end">
              <span className="text-xs font-medium">
                {taskStatus?.progress || 0}%
              </span>
            </div>
          </div>
        </>
      )}
    </motion.div>
  );
}
