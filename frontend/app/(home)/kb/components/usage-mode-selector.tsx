'use client';

import { KBUsageMode } from '@/client';
import {
  FormControl,
  FormDescription,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { cn } from '@/lib/utils';
import { Clock, FileText } from 'lucide-react';
import { useFormContext } from 'react-hook-form';

const usageModes = [
  {
    value: 'manual' as KBUsageMode,
    label: 'Manual',
    icon: FileText,
    description: 'Only when explicitly referenced',
    longDescription:
      'This knowledge base will only be used when you explicitly reference or select it in conversations.',
  },
  // {
  //   value: 'agent_requested' as KBUsageMode,
  //   label: 'Agent Requested',
  //   icon: Tag,
  //   description: 'When the agent determines it\'s needed',
  //   longDescription: 'The AI agent can automatically access this knowledge base when it determines the content is relevant.'
  // },
  {
    value: 'always' as KBUsageMode,
    label: 'Always',
    icon: Clock,
    description: 'Automatically included in all conversations',
    longDescription:
      'This knowledge base will be automatically included in all AI conversations within the workspace.',
  },
];

interface UsageModeSelectorProps {
  className?: string;
}

export function UsageModeSelector({ className }: UsageModeSelectorProps) {
  const form = useFormContext();
  const currentValue = form.watch('usage_mode');

  return (
    <FormItem className={className}>
      <FormLabel className="text-base font-semibold">Usage Mode</FormLabel>
      <FormDescription className="text-muted-foreground text-sm">
        Choose how this knowledge base will be used in AI conversations
      </FormDescription>
      <FormControl>
        <RadioGroup
          onValueChange={(value) =>
            form.setValue('usage_mode', value as KBUsageMode)
          }
          value={currentValue}
          className="grid gap-4 pt-2"
        >
          {usageModes.map((mode) => {
            const isSelected = currentValue === mode.value;

            return (
              <FormItem key={mode.value} className="space-y-0">
                <label
                  htmlFor={mode.value}
                  className={cn(
                    'hover:bg-accent/50 flex cursor-pointer items-start space-x-3 rounded-lg border-2 p-4 transition-all',
                    isSelected
                      ? 'border-primary bg-accent/30'
                      : 'border-muted hover:border-muted-foreground/50',
                  )}
                >
                  <FormControl>
                    <RadioGroupItem
                      value={mode.value}
                      id={mode.value}
                      className="mt-1"
                    />
                  </FormControl>
                  <div className="flex flex-1 items-start gap-3">
                    <mode.icon
                      className={cn(
                        'mt-0.5 h-5 w-5 transition-colors',
                        isSelected ? 'text-primary' : 'text-muted-foreground',
                      )}
                    />
                    <div className="flex-1 space-y-1">
                      <FormLabel
                        htmlFor={mode.value}
                        className={cn(
                          'cursor-pointer text-sm font-semibold transition-colors',
                          isSelected ? 'text-foreground' : 'text-foreground',
                        )}
                      >
                        {mode.label}
                      </FormLabel>
                      <p className="text-muted-foreground text-xs leading-relaxed">
                        {mode.longDescription}
                      </p>
                    </div>
                  </div>
                </label>
              </FormItem>
            );
          })}
        </RadioGroup>
      </FormControl>
      <FormMessage />
    </FormItem>
  );
}
