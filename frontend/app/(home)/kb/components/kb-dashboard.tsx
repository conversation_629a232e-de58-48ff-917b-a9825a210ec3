import { useEffect, useState } from 'react';

import Link from 'next/link';

import { K<PERSON>ead, KnowledgeBaseService } from '@/client';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';
import { Skeleton } from '@/components/ui/skeleton';
import pathsConfig from '@/config/paths.config';
import { convertUtcToLocalTime, formatRelativeTime } from '@/lib/date-utils';
import {
  ArrowRight,
  Clock,
  Database,
  Edit,
  ExternalLink,
  File,
  FileText,
  MoreHorizontal,
  Trash2,
} from 'lucide-react';

interface KBDashboardProps {
  kbs: KBRead[];
  isLoading: boolean;
  onEdit: (kb: KBRead) => void;
  onDelete: (kb: KBRead) => void;
}

// Define a specific type for document counts
interface DocumentCounts {
  [kbId: string]: number;
}

export function KBDashboard({
  kbs,
  isLoading,
  onEdit,
  onDelete,
}: KBDashboardProps) {
  const [documentCounts, setDocumentCounts] = useState<DocumentCounts>({});
  const [isLoadingCounts, setIsLoadingCounts] = useState(false);

  // Get recent KBs (last 5 instead of 3)
  const recentKbs = [...kbs]
    .sort(
      (a, b) =>
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime(),
    )
    .slice(0, 5);

  // Get document counts for all KBs
  useEffect(() => {
    if (kbs.length === 0 || isLoading) return;

    const fetchDocumentCounts = async () => {
      setIsLoadingCounts(true);
      const counts: DocumentCounts = {};

      try {
        // We'll fetch document count for each KB
        await Promise.all(
          kbs.map(async (kb) => {
            try {
              const response = await KnowledgeBaseService.listDocuments({
                kbId: kb.id,
                skip: 0,
                limit: 1, // We only need count, not actual documents
              });
              counts[kb.id] = Number(response.count) || 0;
            } catch (error) {
              console.error(
                `Error fetching document count for KB ${kb.id}:`,
                error,
              );
              counts[kb.id] = 0;
            }
          }),
        );

        setDocumentCounts(counts);
      } catch (error) {
        console.error('Error fetching document counts:', error);
      } finally {
        setIsLoadingCounts(false);
      }
    };

    fetchDocumentCounts();
  }, [kbs, isLoading]);

  // Calculate total counts
  const totalKbs = kbs.length;

  // Fix the type issue with reduce by ensuring we have proper number values
  const totalDocuments = Object.entries(documentCounts).reduce(
    (sum, [_, count]) => sum + (count || 0),
    0,
  );

  const personalKbs = kbs.filter((kb) => kb.access_level === 'private').length;
  const workspaceKbs = kbs.filter((kb) => kb.access_level === 'shared').length;

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Knowledge Bases
            </CardTitle>
            <Database className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <Skeleton className="h-7 w-16" />
            ) : (
              <div className="text-2xl font-bold">{totalKbs}</div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Documents
            </CardTitle>
            <FileText className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            {isLoading || isLoadingCounts ? (
              <Skeleton className="h-7 w-16" />
            ) : (
              <div className="text-2xl font-bold">{totalDocuments}</div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Personal KBs</CardTitle>
            <File className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <Skeleton className="h-7 w-16" />
            ) : (
              <div className="text-2xl font-bold">{personalKbs}</div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Workspace KBs</CardTitle>
            <File className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <Skeleton className="h-7 w-16" />
            ) : (
              <div className="text-2xl font-bold">{workspaceKbs}</div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Recent Knowledge Bases - Horizontal Card Scroll */}
      {recentKbs.length > 0 && (
        <div>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Recent Knowledge Bases</CardTitle>
                <CardDescription>
                  Your recently created knowledge bases
                </CardDescription>
              </div>
              <Button
                variant="ghost"
                size="sm"
                asChild
                className="gap-1 text-sm"
              >
                <Link href={`${pathsConfig.app.knowledgeBase}?tab=all`}>
                  View all
                  <ArrowRight className="h-3.5 w-3.5" />
                </Link>
              </Button>
            </CardHeader>
            <CardContent>
              <ScrollArea className="-mx-1 px-1">
                <div className="flex space-x-4 pb-4">
                  {recentKbs.map((kb) => (
                    <Card
                      key={kb.id}
                      className="group flex max-w-[300px] min-w-[280px] flex-col"
                    >
                      <CardHeader className="pb-2">
                        <div className="flex items-center justify-between">
                          <Badge
                            variant={
                              (kb.access_level || 'private') === 'private'
                                ? 'outline'
                                : 'default'
                            }
                            className="mb-1.5 text-xs"
                          >
                            {(kb.access_level || 'private')
                              .charAt(0)
                              .toUpperCase() +
                              (kb.access_level || 'private').slice(1)}
                          </Badge>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8 opacity-0 transition-opacity group-hover:opacity-100"
                              >
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => onEdit(kb)}>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => onDelete(kb)}
                                className="text-destructive focus:text-destructive"
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                        <CardTitle className="truncate text-base">
                          <Link
                            href={pathsConfig.app.knowledgeBaseDetail(kb.id)}
                            className="hover:underline"
                          >
                            {kb.title}
                          </Link>
                        </CardTitle>
                        {kb.description && (
                          <CardDescription className="line-clamp-2 h-9 text-xs">
                            {kb.description}
                          </CardDescription>
                        )}
                      </CardHeader>
                      <CardContent className="mt-auto pt-0 pb-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <div className="flex items-center text-xs">
                              <FileText className="text-muted-foreground mr-1 h-3 w-3" />
                              {isLoadingCounts ? (
                                <Skeleton className="h-4 w-8" />
                              ) : (
                                <span>{documentCounts[kb.id] || 0} docs</span>
                              )}
                            </div>
                            <div className="flex items-center text-xs">
                              <Clock className="text-muted-foreground mr-1 h-3 w-3" />
                              <span
                                className="truncate"
                                title={
                                  convertUtcToLocalTime(
                                    kb.created_at,
                                  )?.toLocaleString() || ''
                                }
                              >
                                {formatRelativeTime(kb.created_at)}
                              </span>
                            </div>
                          </div>
                          <Button
                            size="sm"
                            variant="ghost"
                            className="h-7 px-2"
                            asChild
                          >
                            <Link
                              href={pathsConfig.app.knowledgeBaseDetail(kb.id)}
                            >
                              <ExternalLink className="h-3.5 w-3.5" />
                            </Link>
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
                <ScrollBar orientation="horizontal" />
              </ScrollArea>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
