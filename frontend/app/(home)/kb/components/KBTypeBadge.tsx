import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { User, Users } from 'lucide-react';

interface KBTypeBadgeProps {
  type: 'private' | 'shared';
  showIcon?: boolean;
  className?: string;
}

export function KBTypeBadge({
  type,
  showIcon = true,
  className,
}: KBTypeBadgeProps) {
  const isPrivate = type === 'private';

  return (
    <Badge variant="outline" className={cn('p-2', className)}>
      {showIcon &&
        (isPrivate ? (
          <User size={16} className="mr-2" />
        ) : (
          <Users size={16} className="mr-2" />
        ))}
      <span>{isPrivate ? 'Private' : 'Shared'}</span>
    </Badge>
  );
}
