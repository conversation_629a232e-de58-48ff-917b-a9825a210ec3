'use client';

import * as React from 'react';

import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command';
import { FormControl } from '@/components/ui/form';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { Check, ChevronsUpDown, Lock, Users } from 'lucide-react';

const accessLevels = [
  {
    value: 'private',
    label: 'Private',
    icon: <Lock className="mr-2 h-4 w-4" />,
    description: 'Only accessible by you',
  },
  {
    value: 'shared',
    label: 'Shared',
    icon: <Users className="mr-2 h-4 w-4" />,
    description: 'Share with specific users',
  },
];

interface AccessLevelComboboxProps {
  value: string;
  onChange: (value: string) => void;
}

export function AccessLevelCombobox({
  value,
  onChange,
}: AccessLevelComboboxProps) {
  const [open, setOpen] = React.useState(false);

  return (
    <FormControl>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between"
          >
            {value ? (
              <div className="flex items-center">
                {accessLevels.find((level) => level.value === value)?.icon}
                {accessLevels.find((level) => level.value === value)?.label}
              </div>
            ) : (
              'Select access level...'
            )}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0">
          <Command>
            <CommandInput placeholder="Search access level..." />
            <CommandList>
              <CommandEmpty>No access level found.</CommandEmpty>
              <CommandGroup>
                {accessLevels.map((level) => (
                  <CommandItem
                    key={level.value}
                    value={level.value}
                    onSelect={(currentValue) => {
                      onChange(currentValue);
                      setOpen(false);
                    }}
                    className="flex items-start py-2"
                  >
                    <div className="flex items-start">
                      <span className="flex items-center">
                        {level.icon}
                        <Check
                          className={cn(
                            'ml-auto h-4 w-4',
                            value === level.value ? 'opacity-100' : 'opacity-0',
                          )}
                        />
                      </span>
                      <div className="ml-2">
                        <p className="font-medium">{level.label}</p>
                        <p className="text-muted-foreground text-xs">
                          {level.description}
                        </p>
                      </div>
                    </div>
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </FormControl>
  );
}
