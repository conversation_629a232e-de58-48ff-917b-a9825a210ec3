import Link from 'next/link';

import { KBR<PERSON> } from '@/client';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import pathsConfig from '@/config/paths.config';
import { convertUtcToLocalTime, formatRelativeTime } from '@/lib/date-utils';
import { Edit, FileText, MoreHorizontal, Trash2 } from 'lucide-react';

import { KBTypeBadge } from '../KBTypeBadge';

interface KBCardProps {
  kb: KBRead;
  onEdit: (kb: KBRead) => void;
  onDelete: (kb: KBRead) => void;
}

export function KBCard({ kb, onEdit, onDelete }: KBCardProps) {
  return (
    <div className="group">
      <Card className="flex h-full flex-col transition-shadow hover:shadow-md">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <KBTypeBadge
              type={(kb.access_level as 'private' | 'shared') || 'private'}
            />
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8 opacity-0 transition-opacity group-hover:opacity-100"
                >
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                <DropdownMenuItem onClick={() => onEdit(kb)}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => onDelete(kb)}
                  className="text-destructive focus:text-destructive"
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          <CardTitle className="mt-1 truncate text-base">
            <Link
              href={pathsConfig.app.knowledgeBaseDetail(kb.id)}
              className="hover:underline"
            >
              {kb.title}
            </Link>
          </CardTitle>
        </CardHeader>
        <CardContent className="grow pb-2">
          <div className="mb-2 flex items-center gap-2">
            {kb.usage_mode && (
              <Badge variant="outline" className="text-xs">
                {kb.usage_mode.replace('_', ' ')}
              </Badge>
            )}
            {kb.tags && kb.tags.length > 0 && (
              <Badge variant="secondary" className="text-xs">
                {kb.tags[0]}
                {kb.tags.length > 1 && `+${kb.tags.length - 1}`}
              </Badge>
            )}
          </div>
          {kb.description && (
            <p className="text-muted-foreground line-clamp-2 text-sm">
              {kb.description}
            </p>
          )}
        </CardContent>
        <CardFooter className="text-muted-foreground flex items-center justify-between pt-2 text-xs">
          <div className="flex items-center">
            <FileText className="mr-1.5 h-3.5 w-3.5" />
            <span>
              Created {formatRelativeTime(convertUtcToLocalTime(kb.created_at))}
            </span>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
