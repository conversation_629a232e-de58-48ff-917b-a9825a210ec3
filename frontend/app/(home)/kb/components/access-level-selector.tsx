'use client';

import { KBAccessLevel } from '@/client';
import {
  FormControl,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { UseFormReturn, useFormContext } from 'react-hook-form';

interface AccessLevelSelectorProps {
  className?: string;
}

export function AccessLevelSelector({ className }: AccessLevelSelectorProps) {
  const form = useFormContext();

  return (
    <FormItem className={className}>
      <FormLabel>Access Level</FormLabel>
      <FormControl>
        <RadioGroup
          onValueChange={(value) =>
            form.setValue('access_level', value as KBAccessLevel)
          }
          value={form.watch('access_level')}
          className="flex flex-col space-y-1"
        >
          <FormItem className="flex items-center space-y-0 space-x-3">
            <FormControl>
              <RadioGroupItem value="private" />
            </FormControl>
            <FormLabel className="font-normal">
              Private (only accessible by you)
            </FormLabel>
          </FormItem>
          <FormItem className="flex items-center space-y-0 space-x-3">
            <FormControl>
              <RadioGroupItem value="shared" />
            </FormControl>
            <FormLabel className="font-normal">
              Shared (accessible by specified users)
            </FormLabel>
          </FormItem>
        </RadioGroup>
      </FormControl>
      <FormMessage />
    </FormItem>
  );
}
