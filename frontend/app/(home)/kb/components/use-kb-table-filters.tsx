'use client';

import { useCallback, useMemo } from 'react';

import { searchParams } from '@/lib/searchparams';
import { useQueryState } from 'nuqs';

export function useKBTableFilters() {
  const [searchQuery, setSearchQuery] = useQueryState(
    'q',
    searchParams.q
      .withOptions({ shallow: false, throttleMs: 1000 })
      .withDefault(''),
  );

  const [accessLevelFilter, setAccessLevelFilter] = useQueryState(
    'access_level',
    searchParams.access_level.withOptions({ shallow: false }).withDefault(''),
  );

  const [usageModeFilter, setUsageModeFilter] = useQueryState(
    'usage_mode',
    searchParams.usage_mode.withOptions({ shallow: false }).withDefault(''),
  );

  const [page, setPage] = useQueryState(
    'page',
    searchParams.page.withOptions({ shallow: false }),
  );

  const resetFilters = useCallback(() => {
    setSearchQuery(null);
    setAccessLevelFilter(null);
    setUsageModeFilter(null);
    setPage(1);
  }, [setSearchQuery, setAccessLevelFilter, setUsageModeFilter, setPage]);

  const isAnyFilterActive = useMemo(() => {
    return !!searchQuery || !!accessLevelFilter || !!usageModeFilter;
  }, [searchQuery, accessLevelFilter, usageModeFilter]);

  return {
    searchQuery,
    setSearchQuery,
    accessLevelFilter,
    setAccessLevelFilter,
    usageModeFilter,
    setUsageModeFilter,
    page,
    setPage,
    resetFilters,
    isAnyFilterActive,
  };
}
