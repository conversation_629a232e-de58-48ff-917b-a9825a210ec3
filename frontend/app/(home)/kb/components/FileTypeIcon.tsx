import React from 'react';

import { cn } from '@/lib/utils';
import {
  FileArchive,
  FileAudio,
  FileCode,
  FileIcon,
  FileImage,
  FileSpreadsheet,
  FileText,
  FileVideo,
  Globe,
} from 'lucide-react';

interface FileTypeIconProps {
  filename: string;
  className?: string;
  documentType?: 'file' | 'url';
}

export function FileTypeIcon({
  filename,
  className = 'h-4 w-4',
  documentType,
}: FileTypeIconProps) {
  // Handle URL documents
  if (
    documentType === 'url' ||
    filename.startsWith('http://') ||
    filename.startsWith('https://')
  ) {
    return <Globe className={cn(className, 'text-blue-500')} />;
  }

  const extension = filename.split('.').pop()?.toLowerCase() || '';

  // Map extensions to specific icons and colors
  switch (extension) {
    case 'pdf':
      return <FileText className={cn(className, 'text-red-500')} />;
    case 'md':
      return <FileText className={cn(className, 'text-blue-500')} />;
    case 'txt':
      return <FileText className={cn(className, 'text-blue-400')} />;
    case 'html':
    case 'htm':
      return <FileCode className={cn(className, 'text-orange-500')} />;
    case 'css':
      return <FileCode className={cn(className, 'text-blue-500')} />;
    case 'js':
    case 'jsx':
      return <FileCode className={cn(className, 'text-yellow-500')} />;
    case 'ts':
    case 'tsx':
      return <FileCode className={cn(className, 'text-blue-600')} />;
    case 'csv':
      return <FileSpreadsheet className={cn(className, 'text-green-600')} />;
    case 'json':
      return <FileCode className={cn(className, 'text-green-500')} />;
    case 'zip':
    case 'rar':
    case 'gz':
    case 'tar':
      return <FileArchive className={cn(className, 'text-gray-500')} />;
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
    case 'svg':
    case 'webp':
      return <FileImage className={cn(className, 'text-purple-500')} />;
    case 'mp4':
    case 'webm':
    case 'mov':
      return <FileVideo className={cn(className, 'text-indigo-500')} />;
    case 'mp3':
    case 'wav':
    case 'ogg':
      return <FileAudio className={cn(className, 'text-pink-500')} />;
    case 'doc':
    case 'docx':
      return <FileText className={cn(className, 'text-blue-600')} />;
    case 'xls':
    case 'xlsx':
      return <FileSpreadsheet className={cn(className, 'text-green-700')} />;
    case 'ppt':
    case 'pptx':
      return <FileText className={cn(className, 'text-orange-600')} />;
    case 'xml':
      return <FileCode className={cn(className, 'text-purple-500')} />;
    default:
      return <FileIcon className={cn(className, 'text-gray-400')} />;
  }
}
