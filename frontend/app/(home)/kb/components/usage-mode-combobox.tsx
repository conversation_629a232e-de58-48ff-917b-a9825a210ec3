'use client';

import { FormControl } from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Clock, FileText } from 'lucide-react';

const usageModes = [
  {
    value: 'manual',
    label: 'Manual',
    icon: FileText,
    description: 'Only when explicitly referenced',
  },
  // {
  //   value: "agent_requested",
  //   label: "Agent Requested",
  //   icon: Tag,
  //   description: "When the agent determines it's needed"
  // },
  {
    value: 'always',
    label: 'Always',
    icon: Clock,
    description: 'Automatically included in all conversations',
  },
];

interface UsageModeComboboxProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
}

export function UsageModeCombobox({
  value,
  onChange,
  placeholder = 'Select usage mode...',
  className,
}: UsageModeComboboxProps) {
  const selectedMode = usageModes.find((mode) => mode.value === value);

  return (
    <FormControl>
      <Select value={value} onValueChange={onChange}>
        <SelectTrigger className={className}>
          <SelectValue placeholder={placeholder}>
            {selectedMode && (
              <div className="flex items-center gap-2">
                <selectedMode.icon className="h-4 w-4" />
                <span>{selectedMode.label}</span>
              </div>
            )}
          </SelectValue>
        </SelectTrigger>
        <SelectContent>
          {usageModes.map((mode) => (
            <SelectItem key={mode.value} value={mode.value}>
              <div className="flex items-start gap-3 py-1">
                <mode.icon className="text-muted-foreground mt-0.5 h-4 w-4" />
                <div className="flex flex-col gap-1">
                  <span className="font-medium">{mode.label}</span>
                  <span className="text-muted-foreground text-xs leading-tight">
                    {mode.description}
                  </span>
                </div>
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </FormControl>
  );
}
