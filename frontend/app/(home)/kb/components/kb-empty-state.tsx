import { Button } from '@/components/ui/button';
import { DatabaseIcon, Plus } from 'lucide-react';

interface KBEmptyStateProps {
  onCreateClick: () => void;
}

export function KBEmptyState({ onCreateClick }: KBEmptyStateProps) {
  return (
    <div className="flex h-[calc(100dvh-400px)] flex-col items-center justify-center space-y-6 px-4">
      <div className="bg-primary/10 rounded-full p-6">
        <DatabaseIcon className="text-primary h-12 w-12" strokeWidth={1.5} />
      </div>
      <div className="max-w-md space-y-2 text-center">
        <h3 className="text-xl font-medium">No Knowledge Bases Yet</h3>
        <p className="text-muted-foreground">
          Create your first knowledge base to store documents and make them
          searchable.
        </p>
      </div>
      <Button size="lg" onClick={onCreateClick} className="gap-2 rounded-lg">
        <Plus className="h-4 w-4" />
        New
      </Button>
    </div>
  );
}
