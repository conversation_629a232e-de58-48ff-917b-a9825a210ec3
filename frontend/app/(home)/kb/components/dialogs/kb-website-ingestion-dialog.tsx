import { useEffect, useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Switch } from '@/components/ui/switch';
import { zodResolver } from '@hookform/resolvers/zod';
import { Globe, Link as LinkIcon, Plus, Settings, X } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

const urlSchema = z
  .string()
  .min(1, 'URL is required')
  .max(2048, 'URL is too long (maximum 2048 characters)')
  .url('Must be a valid URL')
  .refine((url) => url.startsWith('http://') || url.startsWith('https://'), {
    message: 'URL must start with http:// or https://',
  })
  .refine(
    (url) => {
      try {
        const urlObj = new URL(url);

        // Block localhost and private IP ranges for security
        const hostname = urlObj.hostname.toLowerCase();

        // Block localhost variations
        if (
          hostname === 'localhost' ||
          hostname === '127.0.0.1' ||
          hostname === '::1'
        ) {
          return false;
        }

        // Block private IP ranges (basic check)
        if (
          hostname.match(/^10\./) ||
          hostname.match(/^192\.168\./) ||
          hostname.match(/^172\.(1[6-9]|2[0-9]|3[0-1])\./) ||
          hostname.match(/^169\.254\./)
        ) {
          return false;
        }

        // Block file:// and other dangerous protocols
        if (!['http:', 'https:'].includes(urlObj.protocol)) {
          return false;
        }

        // Block URLs with suspicious patterns
        const suspiciousPatterns = [
          /javascript:/i,
          /data:/i,
          /vbscript:/i,
          /<script/i,
          /onload=/i,
          /onerror=/i,
        ];

        for (const pattern of suspiciousPatterns) {
          if (pattern.test(url)) {
            return false;
          }
        }

        return true;
      } catch {
        return false;
      }
    },
    {
      message:
        'URL is not allowed (localhost, private IPs, or suspicious content detected)',
    },
  )
  .refine(
    (url) => {
      // Additional length check for different URL parts
      try {
        const urlObj = new URL(url);

        // Check hostname length
        if (urlObj.hostname.length > 253) {
          return false;
        }

        // Check path length
        if (urlObj.pathname.length > 1000) {
          return false;
        }

        return true;
      } catch {
        return false;
      }
    },
    {
      message: 'URL components are too long',
    },
  );

const formSchema = z.object({
  url: urlSchema,
  deep_crawl: z.boolean(),
});

type FormValues = z.infer<typeof formSchema>;

interface WebsiteItem {
  id: string;
  url: string;
  deep_crawl: boolean;
}

interface KBWebsiteIngestionDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (values: { urls: string[]; deep_crawls: boolean[] }) => void;
  isProcessing: boolean;
}

export function KBWebsiteIngestionDialog({
  open,
  onOpenChange,
  onSubmit,
  isProcessing,
}: KBWebsiteIngestionDialogProps) {
  const [websites, setWebsites] = useState<WebsiteItem[]>([]);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      url: '',
      deep_crawl: false,
    },
  });

  // Reset state when dialog is opened/closed
  useEffect(() => {
    if (!open && !isProcessing) {
      setWebsites([]);
      form.reset({
        url: '',
        deep_crawl: false,
      });
    }
  }, [open, isProcessing, form]);

  const addWebsite = (values: FormValues) => {
    const newWebsite: WebsiteItem = {
      id: Math.random().toString(36).substring(2, 9),
      url: values.url,
      deep_crawl: values.deep_crawl,
    };

    setWebsites((prev) => [...prev, newWebsite]);
    form.reset();
  };

  const removeWebsite = (id: string) => {
    if (!isProcessing) {
      setWebsites((prev) => prev.filter((w) => w.id !== id));
    }
  };

  const toggleDeepCrawl = (id: string) => {
    if (!isProcessing) {
      setWebsites((prev) =>
        prev.map((w) =>
          w.id === id ? { ...w, deep_crawl: !w.deep_crawl } : w,
        ),
      );
    }
  };

  const handleFinalSubmit = () => {
    if (websites.length === 0) return;

    const urls = websites.map((w) => w.url);
    const deep_crawls = websites.map((w) => w.deep_crawl);

    onSubmit({ urls, deep_crawls });
  };

  // Handler to prevent closing dialog while processing
  const handleOpenChange = (newOpen: boolean) => {
    if (!isProcessing || newOpen) {
      onOpenChange(newOpen);
      if (!newOpen) {
        // Reset form and websites when dialog closes
        setWebsites([]);
        form.reset({
          url: '',
          deep_crawl: false,
        });
      }
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="flex max-h-[90vh] w-[95vw] max-w-2xl flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            Add Website Content
          </DialogTitle>
          <DialogDescription>
            Add website URLs to extract their content into your knowledge base.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Add URL Form */}
          <div className="card rounded-lg p-4">
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(addWebsite)}
                className="space-y-4"
              >
                <FormField
                  control={form.control}
                  name="url"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Website URL</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="https://example.com/documentation"
                          {...field}
                          disabled={isProcessing}
                          className="rounded-lg"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="flex items-center justify-between">
                  <FormField
                    control={form.control}
                    name="deep_crawl"
                    render={({ field }) => (
                      <FormItem className="flex items-center space-x-3">
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            id="deep-crawl"
                            disabled={isProcessing}
                          />
                        </FormControl>
                        <div className="space-y-0.5">
                          <FormLabel
                            htmlFor="deep-crawl"
                            className="cursor-pointer text-sm font-medium"
                          >
                            Deep crawl
                          </FormLabel>
                          <FormDescription className="text-xs">
                            {field.value
                              ? 'Extract content from linked pages'
                              : 'Extract only this page'}
                          </FormDescription>
                        </div>
                      </FormItem>
                    )}
                  />

                  <Button
                    type="submit"
                    disabled={isProcessing}
                    className="flex items-center gap-2 rounded-lg"
                  >
                    <Plus className="h-4 w-4" />
                    Add URL
                  </Button>
                </div>
              </form>
            </Form>
          </div>

          {/* Website List */}
          {websites.length > 0 && (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h3 className="flex items-center gap-2 text-sm font-medium">
                  <LinkIcon className="h-4 w-4" />
                  Ready to Process ({websites.length})
                </h3>
                <Badge variant="secondary" className="text-xs">
                  {websites.filter((w) => w.deep_crawl).length} deep crawl
                </Badge>
              </div>

              <ScrollArea className="max-h-[300px] rounded-lg border">
                <div className="space-y-2 p-3">
                  {websites.map((website) => (
                    <div
                      key={website.id}
                      className="bg-card hover:bg-muted/50 flex items-center gap-3 rounded-lg border p-3 transition-colors"
                    >
                      <Globe className="text-muted-foreground h-4 w-4 shrink-0" />

                      <div className="min-w-0 flex-1">
                        <p
                          className="truncate text-sm font-medium"
                          title={website.url}
                        >
                          {website.url}
                        </p>
                        <div className="mt-1 flex items-center gap-2">
                          <Badge
                            variant={website.deep_crawl ? 'default' : 'outline'}
                            className="rounded-lg text-xs"
                          >
                            {website.deep_crawl ? 'Deep crawl' : 'Single page'}
                          </Badge>
                        </div>
                      </div>

                      <div className="flex items-center gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleDeepCrawl(website.id)}
                          disabled={isProcessing}
                          className="h-8 rounded-lg px-2 text-xs"
                        >
                          <Settings className="mr-1 h-3 w-3" />
                          Toggle
                        </Button>

                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => removeWebsite(website.id)}
                          disabled={isProcessing}
                          className="text-muted-foreground hover:text-destructive h-8 w-8 rounded-lg"
                          aria-label="Remove website"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </div>
          )}

          {websites.length === 0 && (
            <div className="text-muted-foreground py-8 text-center">
              <LinkIcon className="mx-auto mb-2 h-8 w-8 opacity-50" />
              <p className="text-sm">No websites added yet</p>
            </div>
          )}
        </div>

        <DialogFooter className="shrink-0">
          <Button
            type="button"
            variant="outline"
            onClick={() => handleOpenChange(false)}
            disabled={isProcessing}
            className="rounded-lg"
          >
            Cancel
          </Button>
          <Button
            onClick={handleFinalSubmit}
            disabled={isProcessing || websites.length === 0}
            className="rounded-lg"
          >
            {isProcessing
              ? 'Processing...'
              : `Process ${websites.length} Website${websites.length === 1 ? '' : 's'}`}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
