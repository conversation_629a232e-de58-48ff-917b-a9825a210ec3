interface CachedPresignedUrl {
  url: string;
  expiresAt: number; // timestamp
  objectName: string;
  kbId: string;
}

const CACHE_KEY_PREFIX = 'kb_presigned_url_';
const DEFAULT_EXPIRY_BUFFER = 5 * 60 * 1000; // 5 minutes buffer before actual expiry

function isLocalStorageAvailable(): boolean {
  try {
    const test = '__localStorage_test__';
    localStorage.setItem(test, test);
    localStorage.removeItem(test);
    return true;
  } catch {
    return false;
  }
}

function getCacheKey(kbId: string, objectName: string): string {
  return `${CACHE_KEY_PREFIX}${kbId}_${btoa(objectName).replace(/[^a-zA-Z0-9]/g, '')}`;
}

export function getCachedPresignedUrl(
  kbId: string,
  objectName: string,
): string | null {
  if (!isLocalStorageAvailable()) return null;

  try {
    const cacheKey = getCacheKey(kbId, objectName);
    const cached = localStorage.getItem(cacheKey);

    if (!cached) return null;

    const cachedData: CachedPresignedUrl = JSON.parse(cached);
    const now = Date.now();

    // Check if URL is expired (with buffer)
    if (now >= cachedData.expiresAt - DEFAULT_EXPIRY_BUFFER) {
      localStorage.removeItem(cacheKey);
      return null;
    }

    return cachedData.url;
  } catch (error) {
    console.warn('Error reading cached presigned URL:', error);
    return null;
  }
}

export function setCachedPresignedUrl(
  kbId: string,
  objectName: string,
  url: string,
  expiryHours: number = 1,
): void {
  if (!isLocalStorageAvailable()) return;

  try {
    const cacheKey = getCacheKey(kbId, objectName);
    const expiresAt = Date.now() + expiryHours * 60 * 60 * 1000;

    const cachedData: CachedPresignedUrl = {
      url,
      expiresAt,
      objectName,
      kbId,
    };

    localStorage.setItem(cacheKey, JSON.stringify(cachedData));
  } catch (error) {
    console.warn('Error caching presigned URL:', error);
  }
}

export function removeCachedPresignedUrl(
  kbId: string,
  objectName: string,
): void {
  if (!isLocalStorageAvailable()) return;

  try {
    const cacheKey = getCacheKey(kbId, objectName);
    localStorage.removeItem(cacheKey);
  } catch (error) {
    console.warn('Error removing cached presigned URL:', error);
  }
}

export function clearAllCachedPresignedUrls(): void {
  if (!isLocalStorageAvailable()) return;

  try {
    const keys = Object.keys(localStorage);
    keys.forEach((key) => {
      if (key.startsWith(CACHE_KEY_PREFIX)) {
        localStorage.removeItem(key);
      }
    });
  } catch (error) {
    console.warn('Error clearing cached presigned URLs:', error);
  }
}

export function cleanupExpiredPresignedUrls(): void {
  if (!isLocalStorageAvailable()) return;

  try {
    const keys = Object.keys(localStorage);
    const now = Date.now();

    keys.forEach((key) => {
      if (key.startsWith(CACHE_KEY_PREFIX)) {
        try {
          const cached = localStorage.getItem(key);
          if (cached) {
            const cachedData: CachedPresignedUrl = JSON.parse(cached);
            if (now >= cachedData.expiresAt) {
              localStorage.removeItem(key);
            }
          }
        } catch {
          // Remove invalid entries
          localStorage.removeItem(key);
        }
      }
    });
  } catch (error) {
    console.warn('Error cleaning up expired presigned URLs:', error);
  }
}

// Helper function to check if a URL is still valid by making a HEAD request
export async function isPresignedUrlValid(url: string): Promise<boolean> {
  try {
    const response = await fetch(url, { method: 'HEAD' });
    return response.ok;
  } catch {
    return false;
  }
}
