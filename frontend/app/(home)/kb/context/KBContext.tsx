import React, {
  ReactNode,
  createContext,
  useCallback,
  useContext,
  useMemo,
  useState,
} from 'react';

import { KBRead, KnowledgeBaseService } from '@/client';
import { CacheKey } from '@/components/utils/cache-key';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { debounce } from 'lodash';
import { toast } from 'sonner';

import { SearchState } from '../utils';
import {
  cleanupExpiredPresignedUrls,
  getCachedPresignedUrl,
  isPresignedUrlValid,
  setCachedPresignedUrl,
} from '../utils/presigned-url-cache';

// Define the DocumentKBRead interface that matches the backend model
export interface DocumentKBRead {
  id: string;
  kb_id: string;
  name: string;
  type: 'url' | 'file';
  url?: string;
  deep_crawl: boolean;
  file_name?: string;
  file_type?: string;
  object_name?: string;
  embed_status: 'PENDING' | 'PROGRESS' | 'SUCCESS' | 'FAILURE';
  created_at: string;
  updated_at: string;
  is_deleted: boolean;
  size?: number;
  isDeleting?: boolean;
  parent_id?: string | null;
  children?: DocumentKBRead[];
}

interface KBContextType {
  // KB State
  selectedKB: KBRead | null;
  isLoadingKB: boolean;

  // Document State
  documents: DocumentKBRead[];
  isLoadingDocuments: boolean;
  documentCount: number;

  // Search State
  searchState: SearchState;
  setSearchQuery: (query: string) => void;

  // Document Handlers
  handleViewDocument: (document: DocumentKBRead) => void;
  handleDeleteDocument: (document: DocumentKBRead) => void;
  refreshDocuments: () => void;

  // Pagination
  skip: number;
  limit: number;
  onPaginate: (skip: number, limit: number) => void;

  // Document Content
  selectedDocument: DocumentKBRead | null;
  documentContent: string | null;
  isLoadingContent: boolean;
  setSelectedDocument: (doc: DocumentKBRead | null) => void;

  // Dialog States
  isDocumentDetailOpen: boolean;
  setIsDocumentDetailOpen: (open: boolean) => void;
}

// Create selector hooks to prevent unnecessary re-renders
const KBContext = createContext<KBContextType | undefined>(undefined);

interface KBProviderProps {
  children: ReactNode;
  kbId: string;
}

export function KBProvider({ children, kbId }: KBProviderProps) {
  const queryClient = useQueryClient();

  // Document list state with simplified pagination
  const [documents, setDocuments] = useState<DocumentKBRead[]>([]);
  const [documentCount, setDocumentCount] = useState(0);
  const [skip, setSkip] = useState(0);
  const [limit, setLimit] = useState(10);

  // Search state
  const [searchState, setSearchState] = useState<SearchState>({
    query: '',
    isSearching: false,
  });

  // Document detail state
  const [selectedDocument, setSelectedDocument] =
    useState<DocumentKBRead | null>(null);
  const [documentContent, setDocumentContent] = useState<string | null>(null);
  const [isDocumentDetailOpen, setIsDocumentDetailOpen] = useState(false);

  // KB API Queries - Using the direct endpoint
  const { data: selectedKBData, isLoading: isLoadingKB } = useQuery({
    queryKey: [CacheKey.KnowledgeBase, 'detail', kbId],
    queryFn: async () => {
      try {
        // Use the direct KB fetch endpoint
        return await KnowledgeBaseService.getKbById({ kbId });
      } catch (error) {
        console.error('Error fetching knowledge base:', error);
        toast.error('Failed to load knowledge base');
        throw error;
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: !!kbId,
  });

  // Make sure selectedKB is never undefined
  const selectedKB = selectedKBData || null;

  // Memoized document transformation function
  const transformDocuments = useCallback(
    (data: any[] = []) => {
      return data.map((doc: any) => ({
        id: doc.id,
        kb_id: doc.kb_id || kbId,
        name: doc.name || '',
        type: doc.type || 'file',
        url: doc.url,
        deep_crawl: doc.deep_crawl || false,
        file_name: doc.file_name || '',
        file_type: doc.file_type,
        object_name: doc.object_name,
        embed_status: doc.embed_status || 'PENDING',
        created_at: doc.created_at,
        updated_at: doc.updated_at,
        is_deleted: doc.is_deleted || false,
        size: doc.size,
        parent_id: doc.parent_id || null,
        children: doc.children || [],
      })) as DocumentKBRead[];
    },
    [kbId],
  );

  // Document list query
  const { isLoading: isLoadingDocuments, refetch: refetchDocuments } = useQuery(
    {
      queryKey: [
        CacheKey.KnowledgeBase,
        kbId,
        'documents',
        skip,
        limit,
        searchState.query,
      ],
      queryFn: async () => {
        console.log('🔍 Fetching documents for KB:', kbId, {
          skip,
          limit,
          search: searchState.query,
        });
        try {
          const result = await KnowledgeBaseService.listDocuments({
            kbId,
            skip,
            limit,
            search: searchState.query || undefined,
          });

          console.log(
            '✅ Documents fetched successfully:',
            result.count,
            'total documents',
          );

          // Use memoized transform function
          const transformedData = transformDocuments(result.data);
          setDocuments(transformedData);
          setDocumentCount((result.count as number) || 0);
          return result;
        } catch (error) {
          console.error('❌ Error fetching documents:', error);
          toast.error('Failed to load documents');
          throw error;
        }
      },
      enabled: !!kbId,
      staleTime: 0, // Always refetch to ensure fresh data when navigating
      refetchOnMount: true, // Ensure query runs when component mounts
    },
  );

  // Content fetching state
  const [isLoadingContent, setIsLoadingContent] = useState(false);

  // Refresh documents function
  const refreshDocuments = useCallback(() => {
    refetchDocuments();
  }, [refetchDocuments]);

  // Search handling
  const debouncedSearch = useCallback(
    debounce((query: string) => {
      setSkip(0); // Reset to first page on search
      setSearchState((prev) => ({ ...prev, isSearching: false }));
      refetchDocuments();
    }, 500),
    [refetchDocuments],
  );

  const setSearchQuery = useCallback(
    (query: string) => {
      setSearchState({ query, isSearching: !!query });
      debouncedSearch(query);
    },
    [debouncedSearch],
  );

  // Reset state when kbId changes to ensure fresh data fetching
  React.useEffect(() => {
    console.log('🔄 KB changed, resetting state for kbId:', kbId);
    // Reset pagination and search state when navigating to a different KB
    setSkip(0);
    setLimit(10);
    setSearchState({
      query: '',
      isSearching: false,
    });
    setDocuments([]);
    setDocumentCount(0);
    setSelectedDocument(null);
    setDocumentContent(null);
    setIsDocumentDetailOpen(false);

    // Cancel any pending debounced search
    debouncedSearch.cancel();
  }, [kbId, debouncedSearch]);

  // Simplified pagination handling
  const onPaginate = useCallback((newSkip: number, newLimit: number) => {
    setSkip(newSkip);
    setLimit(newLimit);
  }, []);

  // Document operations
  const handleViewDocument = useCallback(
    async (document: DocumentKBRead) => {
      setSelectedDocument(document);
      setIsDocumentDetailOpen(true);
      setIsLoadingContent(true);
      setDocumentContent(null);

      try {
        // Clean up expired URLs on each view
        cleanupExpiredPresignedUrls();

        const objectName = document.object_name || document.name;
        let presignedUrl: string | null = null;

        // First, check if we have a cached presigned URL
        const cachedUrl = getCachedPresignedUrl(kbId, objectName);

        if (cachedUrl) {
          // Verify the cached URL is still valid
          const isValid = await isPresignedUrlValid(cachedUrl);
          if (isValid) {
            presignedUrl = cachedUrl;
            console.log('Using cached presigned URL');
          } else {
            console.log('Cached URL expired, fetching new one');
          }
        }

        // If no valid cached URL, fetch a new one
        if (!presignedUrl) {
          const newPresignedUrl = await KnowledgeBaseService.getDocumentContent(
            {
              kbId,
              objectName,
            },
          );

          // Ensure we have a valid presigned URL
          if (!newPresignedUrl || typeof newPresignedUrl !== 'string') {
            throw new Error('No presigned URL received from server');
          }

          presignedUrl = newPresignedUrl;

          // Cache the new URL (default 1 hour expiry)
          setCachedPresignedUrl(kbId, objectName, presignedUrl, 1);
          console.log('Cached new presigned URL');
        }

        // Now fetch the actual content from the presigned URL
        const response = await fetch(presignedUrl);
        if (!response.ok) {
          throw new Error(
            `Failed to fetch document: ${response.status} ${response.statusText}`,
          );
        }

        const blob = await response.blob();
        if (!blob) {
          throw new Error('No content received from presigned URL');
        }

        let processedContent: string | null = null;

        // Convert blob to appropriate format for display - wait for complete processing
        if (blob instanceof Blob) {
          // Wait for the complete blob to be processed
          const fileExtension =
            document.file_name?.split('.').pop()?.toLowerCase() ||
            document.name.split('.').pop()?.toLowerCase() ||
            '';
          const isPdf = fileExtension === 'pdf';
          const isImage = ['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp'].includes(
            fileExtension,
          );
          const isBinary =
            isPdf ||
            isImage ||
            blob.type.startsWith('image/') ||
            blob.type === 'application/pdf';

          if (isPdf) {
            // For PDFs, convert blob to base64 for the PDF viewer using FileReader
            // This is more efficient for large files
            const base64 = await new Promise<string>((resolve, reject) => {
              const reader = new FileReader();
              reader.onload = () => {
                const result = reader.result as string;
                // FileReader returns data URL format, so use it directly
                resolve(result);
              };
              reader.onerror = () =>
                reject(new Error('Failed to read PDF file'));
              reader.readAsDataURL(blob);
            });

            if (!base64 || base64.length === 0) {
              throw new Error('Empty PDF file received');
            }

            processedContent = base64;
          } else if (isBinary) {
            // For other binary files, convert to base64
            const arrayBuffer = await blob.arrayBuffer();
            if (arrayBuffer.byteLength === 0) {
              throw new Error('Empty binary file received');
            }
            const base64 = btoa(
              String.fromCharCode(...new Uint8Array(arrayBuffer)),
            );
            processedContent = JSON.stringify({
              is_binary: true,
              content: base64,
              content_type: blob.type || 'application/octet-stream',
            });
          } else {
            // For text files, ensure complete text is loaded
            const text = await blob.text();
            if (text.length === 0 && blob.size > 0) {
              throw new Error('Failed to read text content from file');
            }
            processedContent = text;
          }
        } else {
          // Handle other response types if needed
          processedContent = String(blob);
        }

        // Only set content after complete processing
        if (processedContent !== null) {
          setDocumentContent(processedContent);
        } else {
          throw new Error('Failed to process document content');
        }
      } catch (error) {
        console.error('Error fetching document content:', error);
        toast.error('Failed to load document content');
        setDocumentContent(null);
      } finally {
        setIsLoadingContent(false);
      }
    },
    [kbId],
  );

  const handleDeleteDocument = useCallback(
    async (document: DocumentKBRead) => {
      try {
        setDocuments((prevDocs) =>
          prevDocs.map((doc) =>
            doc.id === document.id ? { ...doc, isDeleting: true } : doc,
          ),
        );

        const documentId = document.id;

        await KnowledgeBaseService.deleteDocument({
          kbId,
          documentId: documentId,
          objectName: document.object_name || document.name,
        });

        toast.success('Document is being deleted');

        if (selectedDocument?.id === document.id) {
          setIsDocumentDetailOpen(false);
          setSelectedDocument(null);
        }

        // Start polling for document removal - 5 times with 3 second intervals
        let pollCount = 0;
        const maxPolls = 5;
        const pollInterval = 3000; // 3 seconds

        const pollForDeletion = () => {
          if (pollCount >= maxPolls) {
            return;
          }

          pollCount++;

          setTimeout(() => {
            queryClient.invalidateQueries({
              queryKey: [CacheKey.KnowledgeBase, kbId, 'documents'],
            });

            if (pollCount < maxPolls) {
              pollForDeletion();
            }
          }, pollInterval);
        };

        pollForDeletion();
      } catch (error) {
        console.error('Error deleting document:', error);
        toast.error('Failed to start document deletion');

        setDocuments((prevDocs) =>
          prevDocs.map((doc) =>
            doc.id === document.id ? { ...doc, isDeleting: undefined } : doc,
          ),
        );
      }
    },
    [
      kbId,
      selectedDocument?.id,
      setIsDocumentDetailOpen,
      setSelectedDocument,
      queryClient,
    ],
  );

  // Cleanup effect for debounced search
  React.useEffect(() => {
    return () => {
      debouncedSearch.cancel();
    };
  }, [debouncedSearch]);

  const value = useMemo(
    () => ({
      // KB State
      selectedKB,
      isLoadingKB,

      // Document State
      documents,
      isLoadingDocuments,
      documentCount,

      // Search State
      searchState,
      setSearchQuery,

      // Document Handlers
      handleViewDocument,
      handleDeleteDocument,
      refreshDocuments,

      // Pagination
      skip,
      limit,
      onPaginate,

      // Document Content
      selectedDocument,
      documentContent,
      isLoadingContent,
      setSelectedDocument,

      // Dialog States
      isDocumentDetailOpen,
      setIsDocumentDetailOpen,
    }),
    [
      selectedKB,
      isLoadingKB,
      documents,
      isLoadingDocuments,
      documentCount,
      searchState,
      setSearchQuery,
      handleViewDocument,
      handleDeleteDocument,
      refreshDocuments,
      skip,
      limit,
      onPaginate,
      selectedDocument,
      documentContent,
      isLoadingContent,
      setSelectedDocument,
      isDocumentDetailOpen,
      setIsDocumentDetailOpen,
    ],
  );

  return <KBContext.Provider value={value}>{children}</KBContext.Provider>;
}

// Main hook to access the entire context
export function useKB() {
  const context = useContext(KBContext);
  if (context === undefined) {
    throw new Error('useKB must be used within a KBProvider');
  }
  return context;
}

// Selector hooks for specific parts of the context to prevent unnecessary re-renders
export function useKBDocuments() {
  const context = useContext(KBContext);
  if (context === undefined) {
    throw new Error('useKBDocuments must be used within a KBProvider');
  }
  return {
    documents: context.documents,
    isLoadingDocuments: context.isLoadingDocuments,
    documentCount: context.documentCount,
    handleViewDocument: context.handleViewDocument,
    handleDeleteDocument: context.handleDeleteDocument,
    refreshDocuments: context.refreshDocuments,
  };
}

export function useKBPagination() {
  const context = useContext(KBContext);
  if (context === undefined) {
    throw new Error('useKBPagination must be used within a KBProvider');
  }
  return {
    skip: context.skip,
    limit: context.limit,
    onPaginate: context.onPaginate,
  };
}

export function useKBSearch() {
  const context = useContext(KBContext);
  if (context === undefined) {
    throw new Error('useKBSearch must be used within a KBProvider');
  }
  return {
    searchState: context.searchState,
    setSearchQuery: context.setSearchQuery,
  };
}
