'use client';

import { useEffect } from 'react';

import { SubscriptionsService } from '@/client';
// UI Components
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation } from '@tanstack/react-query';
import { Loader2 } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

// Form schema
const formSchema = z.object({
  firstName: z.string().min(1, { message: 'Please enter your first name' }),
  lastName: z.string().min(1, { message: 'Please enter your last name' }),
  jobTitle: z.string().min(2, { message: 'Please enter your job title' }),
  workEmail: z
    .string()
    .email({ message: 'Please enter a valid work email address' }),
  companyName: z.string().min(2, { message: 'Please enter your company name' }),
  cloudSpending: z.string().min(1, {
    message: 'Please select your estimated monthly cloud spending',
  }),
  projectDescription: z.string().min(1, {
    message: 'Please describe your current setup and needs',
  }),
  privacyConsent: z.boolean().refine((val) => val === true, {
    message: 'You must agree to the privacy policy',
  }),
});

type FormValues = z.infer<typeof formSchema>;

interface EnterpriseContactFormProps {
  open: boolean;
  onClose: () => void;
  productId?: string;
}

const defaultValues: FormValues = {
  firstName: '',
  lastName: '',
  companyName: '',
  jobTitle: '',
  workEmail: '',
  cloudSpending: '',
  projectDescription: '',
  privacyConsent: false,
};

export function EnterpriseContactForm({
  open,
  onClose,
  productId,
}: EnterpriseContactFormProps) {
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues,
  });

  const { isPending, mutateAsync } = useMutation({
    mutationFn: SubscriptionsService.submitEnterpriseEnquiry,
    onSuccess: (res) => {
      toast.success('Request submitted successfully', {
        description: res.message,
      });
      onClose();
    },
    onError: () => {
      toast.error('Submission failed', {
        description:
          'There was a problem submitting your request. Please try again.',
      });
    },
  });

  const onSubmit = async (data: FormValues) => {
    if (!productId) {
      toast.error('Submission failed', {
        description:
          'There was a problem submitting your request. Please try again.',
      });
      onClose();

      return;
    }

    return mutateAsync({
      requestBody: {
        product_id: productId,
        first_name: data.firstName,
        last_name: data.lastName,
        work_title: data.jobTitle,
        company_name: data.companyName,
        estimated_monthly_cost: data.cloudSpending,
        message: data.projectDescription,
        work_email: data.workEmail,
      },
    });
  };

  useEffect(() => {
    if (!open && (form.formState.isDirty || form.formState.isSubmitted)) {
      form.reset(defaultValues);
    }
  }, [form, form.formState.isDirty, form.formState.isSubmitted, open]);

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-h-[91vh] max-w-2xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Enterprise Plan Inquiry</DialogTitle>
          <DialogDescription>
            Fill out this form to receive detailed pricing and features
            information tailored to your organization&apos;s needs.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Contact Information */}
            <div className="space-y-4">
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <FormField
                  control={form.control}
                  name="firstName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>First Name*</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter your first name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="lastName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Last Name*</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter your last name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <FormField
                  control={form.control}
                  name="workEmail"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Work Email*</FormLabel>
                      <FormControl>
                        <Input placeholder="<EMAIL>" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="jobTitle"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Job Title*</FormLabel>
                      <FormControl>
                        <Input placeholder="Your position" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="companyName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Company Name*</FormLabel>
                    <FormControl>
                      <Input placeholder="Your organization" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Cloud Information */}
            <div className="space-y-4">
              <FormField
                control={form.control}
                name="cloudSpending"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Estimated Monthly Cloud Spending*</FormLabel>
                    <Select onValueChange={field.onChange}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select your monthly cloud spend" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="under10k">
                          Less than $10,000/month
                        </SelectItem>
                        <SelectItem value="10k-100k">
                          $10,000 - $100,000/month
                        </SelectItem>
                        <SelectItem value="over100k">
                          Over $100,000/month
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="projectDescription"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tell us about your needs</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Describe your current setup, challenges, or specific cost optimization needs..."
                        className="min-h-[120px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Privacy Consent */}
            <FormField
              control={form.control}
              name="privacyConsent"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-y-0 space-x-3">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel className="text-sm font-normal">
                      By submitting this form, I agree to the processing of my
                      personal data as described in the{' '}
                      <a
                        href="https://www.cloudthinker.io/privacy-policy"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-primary hover:text-primary/80 underline"
                      >
                        Privacy Policy
                      </a>
                      .
                    </FormLabel>
                    <FormMessage />
                  </div>
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isPending || !form.watch('privacyConsent')}
              >
                {isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {isPending ? 'Submitting...' : 'Submit'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
