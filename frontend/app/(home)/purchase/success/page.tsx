'use client';

import { useEffect, useRef, useState } from 'react';

import Link from 'next/link';

import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import pathsConfig from '@/config/paths.config';
import { useSubscription } from '@/lib/hooks/useSubscription';
import { cn } from '@/lib/utils';
import {
  ArrowRight,
  BarChart3,
  CheckCircle2,
  Loader2,
  Settings,
  Sparkles,
  Users2,
} from 'lucide-react';

const nextSteps = [
  {
    title: 'Configure Settings',
    description: 'Set up your workspace preferences and notification settings',
    icon: Settings,
    href: pathsConfig.app.workspaces,
  },
  {
    title: 'Start Optimizing',
    description: 'Start optimizing your cloud costs and resources',
    icon: BarChart3,
    href: pathsConfig.app.dashboard,
  },
  {
    title: 'Invite Team Members',
    description: 'Add your team members to collaborate on cost optimization',
    icon: Users2,
    href: pathsConfig.app.workspaceUsers,
  },
];

const POLL_INTERVAL_MS = 2000; // 2 seconds
const MAX_POLL_ATTEMPTS = 15;

export default function PurchaseSuccessPage() {
  const [pollingStatus, setPollingStatus] = useState<
    'polling' | 'success' | 'timeout'
  >('polling');

  const { hasActiveSubscription, activeSubscription, isFetched, fetch } =
    useSubscription();

  const pollAttempts = useRef<number>(0);
  const pollInterval = useRef<NodeJS.Timeout>(undefined);

  const checkSubscription = async () => {
    await fetch();
    pollAttempts.current += 1;
  };

  const pollingSubscription = async () => {
    if (pollInterval.current) {
      clearInterval(pollInterval.current);
    }

    pollInterval.current = setInterval(() => {
      if (pollAttempts.current < MAX_POLL_ATTEMPTS) {
        checkSubscription();
      } else {
        setPollingStatus('timeout');
        clearInterval(pollInterval.current);
      }
    }, POLL_INTERVAL_MS);
  };

  useEffect(() => {
    if (isFetched && !hasActiveSubscription) {
      pollingSubscription();
    }
  }, [isFetched]);

  useEffect(() => {
    if (hasActiveSubscription) {
      setPollingStatus('success');
    }
  }, [hasActiveSubscription]);

  useEffect(() => {
    return () => {
      clearInterval(pollInterval.current);
    };
  }, []);

  if (pollingStatus === 'polling') {
    return (
      <div className="container flex max-w-4xl flex-col items-center justify-center py-20">
        <div className="relative">
          <div className="bg-primary/20 absolute inset-0 animate-ping rounded-full"></div>
          <Loader2 className="text-primary relative h-16 w-16 animate-spin" />
        </div>
        <h1 className="to-primary-600 from-primary mt-8 bg-linear-to-r bg-clip-text text-3xl font-bold text-transparent">
          Activating Your Subscription
        </h1>
        <p className="text-muted-foreground mt-4 max-w-lg text-center text-lg">
          We&apos;re setting up your account with enhanced features. This will
          only take a moment.
        </p>
        <div className="text-muted-foreground mt-8 flex items-center gap-2 text-sm">
          <Loader2 className="h-4 w-4 animate-spin" />
          Processing payment and activating subscription...
        </div>
      </div>
    );
  }

  if (pollingStatus === 'timeout') {
    return (
      <div className="container max-w-4xl py-10">
        <div className="mx-auto mb-10 text-center">
          <h1 className="to-primary-600 from-primary bg-linear-to-r bg-clip-text text-4xl font-bold text-transparent">
            Purchase Processing
          </h1>
          <p className="text-muted-foreground mt-4 text-lg">
            Your purchase is being processed. This may take a few minutes to
            complete.
          </p>
        </div>

        <Card className="overflow-hidden">
          <div className="bg-muted/30 border-b px-6 py-4">
            <h2 className="text-xl font-semibold">What&apos;s happening?</h2>
          </div>
          <CardContent className="space-y-4 p-6">
            <p className="text-muted-foreground">
              Your payment has been confirmed, but we&apos;re still setting up
              your subscription. You can check your subscription status in a few
              minutes.
            </p>

            <div className="mt-6 flex flex-col gap-4 sm:flex-row sm:justify-center">
              <Button
                onClick={() => window.location.reload()}
                className="to-primary-600 hover:from-primary-600 from-primary hover:to-primary bg-linear-to-r transition-all duration-300"
              >
                Refresh Page
              </Button>
              <Button variant="outline" asChild>
                <Link href={pathsConfig.app.subscription}>
                  View Subscription Status
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container max-w-4xl py-10">
      <div className="mx-auto mb-10 text-center">
        <div className="inline-flex items-center justify-center rounded-full bg-green-100 p-3 dark:bg-green-900/30">
          <CheckCircle2 className="h-8 w-8 text-green-600 dark:text-green-400" />
        </div>
        <h1 className="to-primary-600 from-primary mt-6 bg-linear-to-r bg-clip-text text-4xl font-bold text-transparent">
          Purchase Successful!
        </h1>
        <p className="text-muted-foreground mt-4 text-lg">
          Thank you for your purchase. Your subscription has been activated
          successfully.
        </p>
      </div>

      <div className="mb-8 rounded-lg border bg-linear-to-r from-green-50 to-emerald-50 p-4 dark:border-green-900/30 dark:from-green-900/30 dark:to-emerald-900/30">
        <div className="flex items-start gap-3">
          <div className="rounded-lg bg-green-100 p-2 dark:bg-green-800/30">
            <Sparkles className="h-5 w-5 text-green-600 dark:text-green-400" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-green-800 dark:text-green-400">
              {activeSubscription?.product_name} Plan Activated
            </h3>
            <p className="mt-1 text-sm text-green-700 dark:text-green-300">
              You now have access to all features included in your subscription
              plan.
            </p>
          </div>
        </div>
      </div>

      <h2 className="mb-6 text-2xl font-semibold">Next Steps</h2>
      <div className="grid gap-4 md:grid-cols-3">
        {nextSteps.map((step) => {
          const Icon = step.icon;
          return (
            <Link
              key={step.title}
              href={step.href}
              className={cn(
                'group from-card/50 to-card/30 relative overflow-hidden rounded-lg border bg-linear-to-br p-6 shadow-xs transition-all hover:shadow-md dark:border-slate-800',
                'hover:from-primary/5 hover:to-primary/10 hover:bg-linear-to-br',
              )}
            >
              <div className="absolute top-2 right-2">
                <ArrowRight className="text-muted-foreground h-5 w-5 transition-transform duration-300 group-hover:translate-x-1" />
              </div>
              <div className="bg-primary/10 mb-4 inline-flex items-center justify-center rounded-lg p-2.5">
                <Icon className="text-primary h-6 w-6" />
              </div>
              <h3 className="mb-2 text-lg font-semibold">{step.title}</h3>
              <p className="text-muted-foreground text-sm">
                {step.description}
              </p>
            </Link>
          );
        })}
      </div>
    </div>
  );
}
