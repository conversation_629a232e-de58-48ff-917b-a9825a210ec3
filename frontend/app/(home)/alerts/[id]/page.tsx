// app/alerts/[id]/page.tsx
import PageContainer from '@/components/layout/page-container';

import AlertDetails from '../_components/alert-details';

type PageProps = {
  params: Promise<{ id: string }>;
};

export default async function Page(props: PageProps) {
  const params = await props.params;
  return (
    <PageContainer scrollable>
      <div className="mx-auto max-w-6xl space-y-6">
        <AlertDetails id={params.id} />
      </div>
    </PageContainer>
  );
}
