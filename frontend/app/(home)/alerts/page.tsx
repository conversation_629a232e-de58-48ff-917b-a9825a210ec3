// app/alerts/page.tsx
import PageContainer from '@/components/layout/page-container';
import { PageHeader } from '@/components/layout/page-header';

import AlertListing from './_components/alert-listing';
import AlertSummary from './_components/alert-summary';

export const metadata = {
  title: 'Dashboard: Alerts',
};

type PageProps = {
  searchParams: Promise<{
    page: string;
    limit: string;
    q: string;
    severity: string;
  }>;
};

export default async function AlertsPage(props: PageProps) {
  const searchParams = await props.searchParams;
  return (
    <PageContainer>
      <PageHeader
        title="Alerts"
        description="Monitor and manage system alerts"
        additionalContent={<AlertSummary />}
      />
      <AlertListing
        page={searchParams.page ? parseInt(searchParams.page) : undefined}
        limit={searchParams.limit ? parseInt(searchParams.limit) : undefined}
        q={searchParams.q || undefined}
        severity={searchParams.severity || undefined}
      />
    </PageContainer>
  );
}
