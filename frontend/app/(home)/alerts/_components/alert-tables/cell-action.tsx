// app/alerts/_components/alert-tables/cell-action.tsx
'use client';

import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Check, MoreHorizontal } from 'lucide-react';
import { toast } from 'sonner';

import { Alert, AlertStatus } from '../types';

// app/alerts/_components/alert-tables/cell-action.tsx

// app/alerts/_components/alert-tables/cell-action.tsx

type CellActionProps = {
  data: Alert;
};

export const CellAction = ({ data }: CellActionProps) => {
  const onStatusChange = (status: AlertStatus) => {
    toast.success('Status updated', {
      description: 'The alert status was updated successfully.',
    });
  };

  return (
    <DropdownMenu modal={false}>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Open menu</span>
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Actions</DropdownMenuLabel>
        {data.status !== 'acknowledged' && (
          <DropdownMenuItem onClick={() => onStatusChange('acknowledged')}>
            <Check className="mr-2 h-4 w-4" /> Acknowledge
          </DropdownMenuItem>
        )}
        {data.status !== 'resolved' && (
          <DropdownMenuItem onClick={() => onStatusChange('resolved')}>
            <Check className="mr-2 h-4 w-4" /> Resolve
          </DropdownMenuItem>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
