'use client';

import Link from 'next/link';

import { AlertResponse, AlertStatus, AlertsService } from '@/client';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { CacheKey } from '@/components/utils/cache-key';
import pathsConfig from '@/config/paths.config';
import { DateFormat, formatUtcDate } from '@/lib/date-utils';
import { cn } from '@/lib/utils';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { ColumnDef } from '@tanstack/react-table';
import {
  AlertCircle,
  AlertOctagon,
  AlertTriangle,
  CheckCircle2,
  Circle,
  Clock,
  Eye,
  Info,
  XCircle,
} from 'lucide-react';
import { toast } from 'sonner';

// Status change component for inline editing
function StatusSelect({ alert }: { alert: AlertResponse }) {
  const queryClient = useQueryClient();

  const { mutate: updateStatus, isPending } = useMutation({
    mutationFn: (newStatus: AlertStatus) =>
      AlertsService.updateAlertStatus({
        alertId: alert.id,
        status: newStatus,
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [CacheKey.Alerts] });
      toast.success('Alert status updated');
    },
    onError: () => {
      toast.error('Failed to update alert status');
    },
  });

  const statusOptions = [
    { value: 'OPEN', label: 'Open', icon: XCircle, color: 'text-destructive' },
    {
      value: 'ACKNOWLEDGED',
      label: 'Acknowledged',
      icon: Clock,
      color: 'text-orange-500',
    },
    {
      value: 'RESOLVED',
      label: 'Resolved',
      icon: CheckCircle2,
      color: 'text-green-500',
    },
    {
      value: 'CLOSED',
      label: 'Closed',
      icon: Circle,
      color: 'text-muted-foreground',
    },
  ];

  return (
    <Select
      value={alert.status}
      onValueChange={(value) => updateStatus(value as AlertStatus)}
      disabled={isPending}
    >
      <SelectTrigger className="h-8 w-[150px] px-4">
        <SelectValue />
      </SelectTrigger>
      <SelectContent>
        {statusOptions.map((option) => {
          const Icon = option.icon;
          return (
            <SelectItem key={option.value} value={option.value}>
              <div className="flex items-center gap-2">
                <Icon className={`h-4 w-4 ${option.color}`} />
                {option.label}
              </div>
            </SelectItem>
          );
        })}
      </SelectContent>
    </Select>
  );
}

// Helper function to get severity colors
function getSeverityColors(severity: AlertResponse['severity']): {
  text: string;
  bg: string;
  border: string;
} {
  switch (severity) {
    case 'CRITICAL':
      return {
        text: 'text-red-700 dark:text-red-400',
        bg: 'bg-red-50 dark:bg-red-950',
        border: 'border-red-200 dark:border-red-800',
      };
    case 'HIGH':
      return {
        text: 'text-orange-700 dark:text-orange-400',
        bg: 'bg-orange-50 dark:bg-orange-950',
        border: 'border-orange-200 dark:border-orange-800',
      };
    case 'MEDIUM':
      return {
        text: 'text-amber-700 dark:text-amber-400',
        bg: 'bg-amber-50 dark:bg-amber-950',
        border: 'border-amber-200 dark:border-amber-800',
      };
    case 'LOW':
      return {
        text: 'text-green-700 dark:text-green-400',
        bg: 'bg-green-50 dark:bg-green-950',
        border: 'border-green-200 dark:border-green-800',
      };
    case 'INFO':
      return {
        text: 'text-blue-700 dark:text-blue-400',
        bg: 'bg-blue-50 dark:bg-blue-950',
        border: 'border-blue-200 dark:border-blue-800',
      };
    default:
      return {
        text: 'text-slate-700 dark:text-slate-400',
        bg: 'bg-slate-50 dark:bg-slate-950',
        border: 'border-slate-200 dark:border-slate-800',
      };
  }
}

// Helper function to get severity icon
function getSeverityIcon(severity: AlertResponse['severity']) {
  switch (severity) {
    case 'CRITICAL':
      return <AlertOctagon className="h-3 w-3 shrink-0" />;
    case 'HIGH':
      return <AlertTriangle className="h-3 w-3 shrink-0" />;
    case 'MEDIUM':
      return <AlertTriangle className="h-3 w-3 shrink-0" />;
    case 'LOW':
      return <AlertCircle className="h-3 w-3 shrink-0" />;
    case 'INFO':
      return <Info className="h-3 w-3 shrink-0" />;
    default:
      return <AlertCircle className="h-3 w-3 shrink-0" />;
  }
}

export const columns: ColumnDef<AlertResponse>[] = [
  {
    accessorKey: 'title',
    header: 'Title',
    size: 300,
    minSize: 200,
    cell: ({ row }) => {
      const title = row.getValue('title') as string;
      return (
        <div className="max-w-[500px]">
          <div className="truncate font-medium" title={title}>
            {title}
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: 'severity',
    header: () => <div className="text-left">Severity</div>,
    size: 120,
    cell: ({ row }) => {
      const severity = row.getValue('severity') as AlertResponse['severity'];
      const colors = getSeverityColors(severity);
      const icon = getSeverityIcon(severity);

      return (
        <div className="justify-left flex">
          <Badge
            variant="outline"
            className={cn(
              'items-left flex p-2 capitalize',
              colors.text,
              colors.bg,
              colors.border,
            )}
          >
            {icon}
            <span className="ml-1">{severity.toLowerCase()}</span>
          </Badge>
        </div>
      );
    },
  },
  {
    accessorKey: 'status',
    header: () => <div className="text-left">Status</div>,
    size: 200,
    minSize: 150,
    cell: ({ row }) => {
      const alert = row.original;
      return (
        <div className="justify-left flex">
          <StatusSelect alert={alert} />
        </div>
      );
    },
  },
  {
    accessorKey: 'created_at',
    header: 'Created',
    // size: 120,
    cell: ({ row }) => {
      return formatUtcDate(row.getValue('created_at'), DateFormat.DATE_TIME);
    },
  },
  {
    accessorKey: 'updated_at',
    header: 'Updated',
    cell: ({ row }) => {
      return formatUtcDate(row.getValue('updated_at'), DateFormat.DATE_TIME);
    },
  },
  {
    id: 'actions',
    // size: 100,
    cell: ({ row }) => {
      const alert = row.original;
      return (
        <Button variant="ghost" size="sm" asChild>
          <Link href={pathsConfig.app.alertDetail(alert.id)}>
            <Eye className="mr-2 h-4 w-4" />
            View
          </Link>
        </Button>
      );
    },
  },
];
