// app/alerts/_components/alert-summary.tsx
'use client';

import { useEffect, useState } from 'react';

import { AlertsService } from '@/client';
import { AlertStatus, AlertStatusSummary } from '@/client';
import DashboardCard from '@/components/dashboard-card';
import {
  AcknowledgedAlertsIcon,
  ClosedAlertsIcon,
  OpenAlertsIcon,
  ResolvedAlertsIcon,
} from '@/components/dashboard-icons';
import { Card } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { AlertCircle, CheckCircle2, Clock, XCircle } from 'lucide-react';

// app/alerts/_components/alert-summary.tsx

// app/alerts/_components/alert-summary.tsx

// app/alerts/_components/alert-summary.tsx

export default function AlertSummary() {
  const [data, setData] = useState<AlertStatusSummary | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchAlertSummary = async () => {
      try {
        setIsLoading(true);
        const response = await AlertsService.getAlertStatusSummary();
        setData(response);
        setError(null);
      } catch (err) {
        console.error('Failed to fetch alert summary:', err);
        setError(
          err instanceof Error
            ? err
            : new Error('Failed to fetch alert summary'),
        );
      } finally {
        setIsLoading(false);
      }
    };

    fetchAlertSummary();
  }, []);

  // If loading, show skeleton loading state
  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="shadow-xs">
            <div className="space-y-4 p-6">
              <Skeleton className="h-5 w-1/2" />
              <Skeleton className="h-10 w-1/4" />
            </div>
          </Card>
        ))}
      </div>
    );
  }

  // If error occurred
  if (error) {
    return (
      <div className="border-destructive bg-destructive/10 mb-6 rounded-lg border p-4">
        <p className="text-destructive font-medium">
          Failed to load alert summary
        </p>
      </div>
    );
  }

  // If no data available
  if (!data) {
    return null;
  }

  // Extract counts from the data or default to 0 if not present
  const statusCounts = data.status_counts;
  const total = data.total;

  const openCount = statusCounts.OPEN || 0;
  const acknowledgedCount = statusCounts.ACKNOWLEDGED || 0;
  const resolvedCount = statusCounts.RESOLVED || 0;
  const closedCount = statusCounts.CLOSED || 0;

  // Calculate percentages of total for each status
  const openPercentage = total > 0 ? Math.round((openCount / total) * 100) : 0;
  const acknowledgedPercentage =
    total > 0 ? Math.round((acknowledgedCount / total) * 100) : 0;
  const resolvedPercentage =
    total > 0 ? Math.round((resolvedCount / total) * 100) : 0;
  const closedPercentage =
    total > 0 ? Math.round((closedCount / total) * 100) : 0;

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <DashboardCard
        title="Open Alerts"
        value={openCount}
        subtitle={`${openPercentage}% of total`}
        icon={<OpenAlertsIcon />}
      />

      <DashboardCard
        title="Acknowledged"
        value={acknowledgedCount}
        subtitle={`${acknowledgedPercentage}% of total`}
        icon={<AcknowledgedAlertsIcon />}
      />

      <DashboardCard
        title="Resolved"
        value={resolvedCount}
        subtitle={`${resolvedPercentage}% of total`}
        icon={<ResolvedAlertsIcon />}
      />

      <DashboardCard
        title="Closed"
        value={closedCount}
        subtitle={`${closedPercentage}% of total`}
        icon={<ClosedAlertsIcon />}
      />
    </div>
  );
}
