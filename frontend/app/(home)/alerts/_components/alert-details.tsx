'use client';

import { notFound, useRouter } from 'next/navigation';

import { AlertResponse, AlertStatus, AlertsService } from '@/client';
import FormCardSkeleton from '@/components/form-card-skeleton';
import { PageHeader } from '@/components/layout/page-header';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Separator } from '@/components/ui/separator';
import { <PERSON><PERSON><PERSON><PERSON> } from '@/components/utils/cache-key';
import pathsConfig from '@/config/paths.config';
import { DateFormat, formatUtcDate } from '@/lib/date-utils';
import { cn } from '@/lib/utils';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  AlertTriangle,
  ArrowLeft,
  CheckCircle2,
  ChevronDown,
  Circle,
  Clock,
  Edit,
  History,
  Info,
  Loader2,
  Trash2,
  XCircle,
} from 'lucide-react';
import { toast } from 'sonner';

interface AlertDetailsProps {
  id: string;
}

// Helper: Get severity icon and color
function getSeverityDisplay(severity: AlertResponse['severity']) {
  switch (severity) {
    case 'CRITICAL':
      return {
        icon: AlertTriangle,
        color: 'text-red-500',
        bgColor: 'bg-red-500/10',
        borderColor: 'border-red-500/20',
        badgeVariant: 'destructive' as const,
      };
    case 'HIGH':
      return {
        icon: AlertTriangle,
        color: 'text-orange-500',
        bgColor: 'bg-orange-500/10',
        borderColor: 'border-orange-500/20',
        badgeVariant: 'warning' as const,
      };
    case 'MEDIUM':
      return {
        icon: AlertTriangle,
        color: 'text-yellow-500',
        bgColor: 'bg-yellow-500/10',
        borderColor: 'border-yellow-500/20',
        badgeVariant: 'info' as const,
      };
    case 'LOW':
      return {
        icon: Circle,
        color: 'text-blue-500',
        bgColor: 'bg-blue-500/10',
        borderColor: 'border-blue-500/20',
        badgeVariant: 'secondary' as const,
      };
    case 'INFO':
      return {
        icon: Info,
        color: 'text-gray-500',
        bgColor: 'bg-gray-500/10',
        borderColor: 'border-gray-500/20',
        badgeVariant: 'ghost-info' as const,
      };
    default:
      return {
        icon: Circle,
        color: 'text-gray-500',
        bgColor: 'bg-gray-500/10',
        borderColor: 'border-gray-500/20',
        badgeVariant: 'default' as const,
      };
  }
}

// Helper: Get status icon and color
function getStatusDisplay(status: AlertStatus) {
  switch (status) {
    case 'OPEN':
      return {
        icon: XCircle,
        color: 'text-red-500',
        bgColor: 'bg-red-500/10',
        label: 'Open',
      };
    case 'ACKNOWLEDGED':
      return {
        icon: Clock,
        color: 'text-orange-500',
        bgColor: 'bg-orange-500/10',
        label: 'Acknowledged',
      };
    case 'RESOLVED':
      return {
        icon: CheckCircle2,
        color: 'text-green-500',
        bgColor: 'bg-green-500/10',
        label: 'Resolved',
      };
    case 'CLOSED':
      return {
        icon: Circle,
        color: 'text-muted-foreground',
        bgColor: 'bg-muted/10',
        label: 'Closed',
      };
    default:
      return {
        icon: Circle,
        color: 'text-muted-foreground',
        bgColor: 'bg-muted/10',
        label: status,
      };
  }
}

export default function AlertDetails({ id }: AlertDetailsProps) {
  const router = useRouter();
  const queryClient = useQueryClient();

  const { data: alert, isPending } = useQuery({
    queryFn: () => AlertsService.getAlert({ alertId: id }),
    queryKey: [CacheKey.Alerts, id],
  });

  const { mutate: updateStatus, isPending: isUpdating } = useMutation({
    mutationFn: (newStatus: AlertStatus) =>
      AlertsService.updateAlertStatus({
        alertId: id,
        status: newStatus,
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [CacheKey.Alerts] });
      toast.success('Alert status updated');
    },
    onError: () => {
      toast.error('Failed to update alert status');
    },
  });

  const { mutate: deleteAlert, isPending: isDeletePending } = useMutation({
    mutationFn: () =>
      AlertsService.deleteAlert({
        alertId: id,
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [CacheKey.Alerts] });
      toast.success('Alert deleted');
      router.push(pathsConfig.app.alerts);
    },
    onError: () => {
      toast.error('Failed to delete alert');
    },
  });

  if (isPending) {
    return <FormCardSkeleton />;
  }

  if (!alert) {
    notFound();
  }

  const severityDisplay = getSeverityDisplay(alert.severity);
  const statusDisplay = getStatusDisplay(alert.status);
  const SeverityIcon = severityDisplay.icon;
  const StatusIcon = statusDisplay.icon;

  const statusOptions = [
    { value: 'OPEN', label: 'Open', color: 'text-red-500', icon: XCircle },
    {
      value: 'ACKNOWLEDGED',
      label: 'Acknowledged',
      color: 'text-orange-500',
      icon: Clock,
    },
    {
      value: 'RESOLVED',
      label: 'Resolved',
      color: 'text-green-500',
      icon: CheckCircle2,
    },
    {
      value: 'CLOSED',
      label: 'Closed',
      color: 'text-muted-foreground',
      icon: Circle,
    },
  ];

  // Create the actions for the header
  const headerActions = (
    <div className="flex items-center space-x-2">
      <Button
        variant="outline"
        size="sm"
        onClick={() => router.push(pathsConfig.app.alerts)}
        className="flex items-center gap-2"
      >
        <ArrowLeft className="h-4 w-4" />
        Back
      </Button>

      {/* Status Change Dropdown */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild disabled={isUpdating}>
          <Button variant="outline" size="sm" disabled={isUpdating}>
            {isUpdating && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Change Status <ChevronDown className="ml-2 h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          {statusOptions.map((option) => {
            const OptionIcon = option.icon;
            return (
              <DropdownMenuItem
                key={option.value}
                onClick={() => updateStatus(option.value as AlertStatus)}
                disabled={alert.status === option.value}
                className={alert.status === option.value ? 'opacity-50' : ''}
              >
                <OptionIcon className={`${option.color} mr-2 h-4 w-4`} />
                {option.label}
              </DropdownMenuItem>
            );
          })}
        </DropdownMenuContent>
      </DropdownMenu>

      <AlertDialog>
        <AlertDialogTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className="text-destructive hover:text-destructive"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </AlertDialogTrigger>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Alert</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this alert? This action cannot be
              undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => deleteAlert()}
              disabled={isDeletePending}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeletePending ? 'Deleting...' : 'Delete'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header with consistent pattern */}
      <PageHeader
        title={alert.title}
        description={`Alert ${alert.severity?.toLowerCase()} severity`}
        actions={headerActions}
        additionalContent={
          <div className="flex items-center space-x-2">
            <Badge
              variant="outline"
              className={`${statusDisplay.bgColor} ${statusDisplay.color}`}
            >
              <StatusIcon className="mr-1 h-3 w-3" />
              {statusDisplay.label}
            </Badge>
            <Badge variant={severityDisplay.badgeVariant}>
              <SeverityIcon className="mr-1 h-3 w-3" />
              {alert.severity}
            </Badge>
          </div>
        }
      />

      {/* Two-column layout for main content */}
      <div className="grid gap-6 lg:grid-cols-3">
        {/* Main Content - Description (2/3 width) */}
        <div className="space-y-6 lg:col-span-2">
          <Card className="transition-shadow hover:shadow-md">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Edit className="h-5 w-5" />
                Alert Description
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="prose prose-sm max-w-none">
                <p className="text-foreground leading-relaxed whitespace-pre-line">
                  {alert.description}
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Timeline Card */}
          <Card className="transition-shadow hover:shadow-md">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <History className="h-5 w-5" />
                Activity Timeline
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="rounded-full bg-blue-500/10 p-2">
                    <Circle className="h-4 w-4 text-blue-500" />
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm font-medium">Alert Created</p>
                    <p className="text-muted-foreground text-xs">
                      {formatUtcDate(alert.created_at, DateFormat.DATE_TIME)}
                    </p>
                  </div>
                </div>
                {alert.updated_at && (
                  <div className="flex items-start gap-3">
                    <div className="rounded-full bg-green-500/10 p-2">
                      <CheckCircle2 className="h-4 w-4 text-green-500" />
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm font-medium">Last Updated</p>
                      <p className="text-muted-foreground text-xs">
                        {formatUtcDate(alert.updated_at, DateFormat.DATE_TIME)}
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar - Alert Information (1/3 width) */}
        <div className="space-y-6">
          <Card className="transition-shadow hover:shadow-md">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center space-x-2 text-lg">
                <AlertTriangle className="h-5 w-5" />
                <span>Alert Information</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="space-y-1">
                  <p className="text-muted-foreground text-sm font-medium">
                    Workspace ID
                  </p>
                  <p className="bg-muted rounded px-2 py-1 font-mono text-sm break-all">
                    {alert.workspace_id}
                  </p>
                </div>
                <div className="space-y-1">
                  <p className="text-muted-foreground text-sm font-medium">
                    Severity
                  </p>
                  <div className="flex items-center space-x-2">
                    <SeverityIcon
                      className={cn('h-4 w-4', severityDisplay.color)}
                    />
                    <span className="text-sm font-medium">
                      {alert.severity}
                    </span>
                  </div>
                </div>
                <div className="space-y-1">
                  <p className="text-muted-foreground text-sm font-medium">
                    Status
                  </p>
                  <div className="flex items-center space-x-2">
                    <StatusIcon
                      className={cn('h-4 w-4', statusDisplay.color)}
                    />
                    <span className="text-sm font-medium">
                      {statusDisplay.label}
                    </span>
                  </div>
                </div>
              </div>

              <Separator />

              <div className="space-y-3">
                <div className="space-y-1">
                  <p className="text-muted-foreground text-sm font-medium">
                    Created
                  </p>
                  <p className="text-sm">
                    {formatUtcDate(alert.created_at, DateFormat.DATE_TIME)}
                  </p>
                </div>

                {alert.updated_at && (
                  <div className="space-y-1">
                    <p className="text-muted-foreground text-sm font-medium">
                      Last Updated
                    </p>
                    <p className="text-sm">
                      {formatUtcDate(alert.updated_at, DateFormat.DATE_TIME)}
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions Card */}
          <Card className="transition-shadow hover:shadow-md">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {alert.status === 'OPEN' && (
                <Button
                  onClick={() => updateStatus('ACKNOWLEDGED')}
                  disabled={isUpdating}
                  className="w-full"
                  variant="outline"
                >
                  <Clock className="mr-2 h-4 w-4" />
                  Acknowledge Alert
                </Button>
              )}

              {(alert.status === 'OPEN' || alert.status === 'ACKNOWLEDGED') && (
                <Button
                  onClick={() => updateStatus('RESOLVED')}
                  disabled={isUpdating}
                  className="w-full"
                  variant="default"
                >
                  <CheckCircle2 className="mr-2 h-4 w-4" />
                  Mark as Resolved
                </Button>
              )}

              {alert.status === 'RESOLVED' && (
                <Button
                  onClick={() => updateStatus('CLOSED')}
                  disabled={isUpdating}
                  className="w-full"
                  variant="outline"
                >
                  <XCircle className="mr-2 h-4 w-4" />
                  Close Alert
                </Button>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
