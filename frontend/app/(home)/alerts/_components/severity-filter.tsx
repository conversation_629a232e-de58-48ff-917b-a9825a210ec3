// app/alerts/_components/severity-filter.tsx
'use client';

import { AlertSeverity } from '@/client';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Check, ChevronsUpDown, X } from 'lucide-react';

// app/alerts/_components/severity-filter.tsx

// app/alerts/_components/severity-filter.tsx

const severityOptions: {
  label: string;
  value: AlertSeverity;
  color: string;
}[] = [
  { label: 'Critical', value: 'CRITICAL', color: 'bg-red-500' },
  { label: 'High', value: 'HIGH', color: 'bg-orange-500' },
  { label: 'Medium', value: 'MEDIUM', color: 'bg-yellow-500' },
  { label: 'Low', value: 'LOW', color: 'bg-blue-500' },
  { label: 'Info', value: 'INFO', color: 'bg-green-500' },
];

interface SeverityFilterProps {
  selectedSeverity: AlertSeverity | null;
  onChange: (severity: AlertSeverity | null) => void;
}

export function SeverityFilter({
  selectedSeverity,
  onChange,
}: SeverityFilterProps) {
  const selectedOption = severityOptions.find(
    (option) => option.value === selectedSeverity,
  );

  return (
    <div className="flex items-center gap-2">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" className="flex items-center gap-2">
            {selectedSeverity ? <>Filter: Severity</> : <>Filter by severity</>}
            <ChevronsUpDown className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" className="w-[200px]">
          {severityOptions.map((option) => (
            <DropdownMenuItem
              key={option.value}
              onClick={() =>
                onChange(
                  option.value === selectedSeverity ? null : option.value,
                )
              }
              className="flex items-center justify-between"
            >
              <div className="flex items-center gap-2">
                <div className={`h-2 w-2 rounded-full ${option.color}`} />
                {option.label}
              </div>
              {option.value === selectedSeverity && (
                <Check className="h-4 w-4" />
              )}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>

      {selectedSeverity && (
        <Badge variant="secondary" className="flex items-center gap-1">
          <div className={`h-2 w-2 rounded-full ${selectedOption?.color}`} />
          {selectedOption?.label}
          <X
            className="ml-1 h-3 w-3 cursor-pointer"
            onClick={() => onChange(null)}
          />
        </Badge>
      )}
    </div>
  );
}
