import PageContainer from '@/components/layout/page-container';

import ItemViewPage from '../_components/item-view-page';

export const metadata = {
  title: 'CloudThinker',
};

type PageProps = { params: Promise<{ id: string }> };

export default async function Page(props: PageProps) {
  const params = await props.params;
  return (
    <PageContainer scrollable>
      <div className="flex-1 space-y-4">
        <ItemViewPage id={params.id} />
      </div>
    </PageContainer>
  );
}
