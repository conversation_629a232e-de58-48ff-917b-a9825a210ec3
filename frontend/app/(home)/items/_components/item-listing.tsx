'use client';

import { useEffect } from 'react';

import { ItemsService } from '@/client';
import { DataTable } from '@/components/ui/table/data-table';
import { CacheKey } from '@/components/utils/cache-key';
import { useQuery, useQueryClient } from '@tanstack/react-query';

import { columns } from './item-tables/columns';

type ItemListingPageProps = {
  page?: number;
  limit?: number;
};

function getItemsQueryOptions({
  page,
  limit,
}: {
  page: number;
  limit: number;
}) {
  return {
    queryFn: () =>
      ItemsService.readItems({
        skip: (page - 1) * limit,
        limit,
      }),
    queryKey: [CacheKey.Items, { page, limit }],
  };
}

export default function ItemListingPage({
  page = 1,
  limit = 10,
}: ItemListingPageProps) {
  const queryClient = useQueryClient();

  const {
    data: items,
    isPending,
    isPlaceholderData,
  } = useQuery({
    ...getItemsQueryOptions({
      page,
      limit,
    }),
    placeholderData: (prevData) => prevData,
  });

  const hasNextPage = !isPlaceholderData && items?.data.length === limit;

  useEffect(() => {
    if (hasNextPage) {
      queryClient.prefetchQuery(
        getItemsQueryOptions({ page: page + 1, limit }),
      );
    }
  }, [page, queryClient, hasNextPage, limit]);

  return (
    <div>
      <DataTable
        columns={columns}
        data={items?.data ?? []}
        totalItems={items?.count ?? 0}
        loading={isPending}
      />
    </div>
  );
}
