'use client';

import { useRouter } from 'next/navigation';

import {
  Api<PERSON>rror,
  ItemCreate,
  ItemPublic,
  ItemUpdate,
  ItemsService,
} from '@/client';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { CacheKey } from '@/components/utils/cache-key';
import { handleError } from '@/components/utils/handle-error';
import pathsConfig from '@/config/paths.config';
import { ActionType } from '@/types/common.enum';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import * as z from 'zod';

type ItemFormProps = {
  initialData: ItemPublic | null;
  pageTitle: string;
  action: ActionType;
};

const formSchema = z.object({
  title: z.string().min(2, {
    message: 'Item name must be at least 2 characters.',
  }),
  description: z.string().optional(),
});

export default function ItemForm({
  initialData,
  pageTitle,
  action,
}: ItemFormProps) {
  const defaultValues = {
    title: initialData?.title || '',
    description: initialData?.description || '',
  };

  const queryClient = useQueryClient();

  const router = useRouter();

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    values: defaultValues,
  });

  const { isSubmitting } = form.formState;

  const invalidateKeys = initialData
    ? [initialData.id, CacheKey.Items]
    : [CacheKey.Items];

  const updateMutation = useMutation({
    mutationFn: (payload: { data: ItemUpdate; id: string }) =>
      ItemsService.updateItem({ id: payload.id, requestBody: payload.data }),
    onSuccess: () => {
      toast.success('Success!', {
        description: 'Item updated successfully.',
      });
    },
    onError: (err: ApiError) => {
      handleError(err);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: invalidateKeys });
    },
  });

  const createMutation = useMutation({
    mutationFn: (payload: { data: ItemCreate }) =>
      ItemsService.createItem({ requestBody: payload.data }),
    onSuccess: () => {
      toast.success('Success!', {
        description: 'Item created successfully.',
      });

      router.push(pathsConfig.app.items);
    },
    onError: (err: ApiError) => {
      handleError(err);
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: invalidateKeys });
    },
  });

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      switch (action) {
        case ActionType.Create:
          await createMutation.mutateAsync({ data: values });
          break;
        case ActionType.Edit:
          if (!initialData) {
            toast.error('Error', { description: 'Item not found' });
            return;
          }
          await updateMutation.mutateAsync({
            data: values,
            id: initialData.id,
          });
          break;
        default:
          break;
      }
    } catch (error: any) {
      toast.error('Submit failed', { description: error?.message });
    }
  };

  return (
    <Card className="mx-auto w-full">
      <CardHeader>
        <CardTitle className="text-left text-2xl font-bold">
          {pageTitle}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <FormField
                control={form.control}
                name="title"
                disabled={isSubmitting}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Item Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter item name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <FormField
              control={form.control}
              name="description"
              disabled={isSubmitting}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter item description"
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button disabled={isSubmitting} type="submit">
              Submit
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
