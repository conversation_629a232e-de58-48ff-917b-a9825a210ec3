'use client';

import { notFound } from 'next/navigation';

import { ItemsService } from '@/client';
import FormCardSkeleton from '@/components/form-card-skeleton';
import { ActionType } from '@/types/common.enum';
import { useQuery } from '@tanstack/react-query';

import ItemForm from './item-form';

type ItemViewPageProps = {
  id: string;
};

export default function ItemViewPage({ id }: ItemViewPageProps) {
  const { data: product, isPending } = useQuery({
    queryFn: () => ItemsService.readItem({ id }),
    queryKey: [id],
  });

  if (isPending) {
    return <FormCardSkeleton />;
  }

  if (!product) {
    notFound();
  }

  return (
    <ItemForm
      initialData={product}
      pageTitle="Edit Product"
      action={ActionType.Edit}
    />
  );
}
