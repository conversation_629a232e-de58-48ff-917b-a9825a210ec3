'use client';

import { useState, useTransition } from 'react';

import { useRouter } from 'next/navigation';

import { ItemPublic, ItemsService } from '@/client';
import { AlertModal } from '@/components/modal/alert-modal';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { CacheKey } from '@/components/utils/cache-key';
import pathsConfig from '@/config/paths.config';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Edit, MoreHorizontal, Trash } from 'lucide-react';
import { toast } from 'sonner';

type CellActionProps = {
  data: ItemPublic;
};

export const CellAction = ({ data }: CellActionProps) => {
  const queryClient = useQueryClient();

  const [loading, startTransition] = useTransition();
  const [open, setOpen] = useState(false);
  const router = useRouter();

  const mutation = useMutation({
    mutationFn: (id: string) => ItemsService.deleteItem({ id }),
    onSuccess: () => {
      toast('Success', {
        description: `The item was deleted successfully.`,
      });
      setOpen(false);
    },
    onError: () => {
      toast.error('An error occurred.', {
        description: `An error occurred while deleting the item`,
      });
    },
    onSettled: () => {
      queryClient.invalidateQueries({
        queryKey: [CacheKey.Items],
      });
    },
  });

  const onDeleteConfirm = async () => {
    startTransition(async () => {
      await mutation.mutateAsync(data.id);
    });
  };

  return (
    <>
      <AlertModal
        isOpen={open}
        onClose={() => setOpen(false)}
        onConfirm={onDeleteConfirm}
        loading={loading}
      />
      <DropdownMenu modal={false}>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>Actions</DropdownMenuLabel>

          <DropdownMenuItem
            onClick={() => router.push(pathsConfig.app.itemDetail(data.id))}
          >
            <Edit className="mr-2 h-4 w-4" /> Update
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => setOpen(true)}>
            <Trash className="mr-2 h-4 w-4" /> Delete
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </>
  );
};
