'use client';

import { ItemPublic } from '@/client';
import { ColumnDef } from '@tanstack/react-table';

import { CellAction } from './cell-action';

export const columns: ColumnDef<ItemPublic>[] = [
  {
    accessorKey: 'id',
    header: 'ID',
  },
  {
    accessorKey: 'title',
    header: 'TITLE',
  },
  {
    accessorKey: 'description',
    header: 'DESCRIPTION',
  },
  {
    id: 'actions',
    cell: ({ row }) => <CellAction data={row.original} />,
  },
];
