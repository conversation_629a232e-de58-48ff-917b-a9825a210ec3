import Link from 'next/link';

import PageContainer from '@/components/layout/page-container';
import { buttonVariants } from '@/components/ui/button';
import { Heading } from '@/components/ui/heading';
import { Separator } from '@/components/ui/separator';
import pathsConfig from '@/config/paths.config';
import { cn } from '@/lib/utils';
import { Plus } from 'lucide-react';

import ItemListingPage from './_components/item-listing';

export const metadata = {
  title: 'Dashboard: Items',
};

type PageProps = {
  searchParams: Promise<{ page: string; limit: string }>;
};

export default async function Page(props: PageProps) {
  const searchParams = await props.searchParams;
  return (
    <PageContainer>
      <div className="space-y-4">
        <div className="flex items-start justify-between">
          <Heading title="Items" description="Manage items" />
          <Link
            href={pathsConfig.app.newItem}
            className={cn(buttonVariants(), 'text-xs md:text-sm')}
          >
            <Plus className="mr-2 h-4 w-4" /> Add New
          </Link>
        </div>
        <Separator />
        <ItemListingPage
          page={searchParams.page ? parseInt(searchParams.page) : undefined}
          limit={searchParams.limit ? parseInt(searchParams.limit) : undefined}
        />
      </div>
    </PageContainer>
  );
}
