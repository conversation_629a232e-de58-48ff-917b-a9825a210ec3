'use client';

import PageContainer from '@/components/layout/page-container';
import { PageHeader } from '@/components/layout/page-header';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ToolList } from '@/features/builtin-tools/components/tool-list';
import { ConnectionList } from '@/features/connection';
import { ConnectionType } from '@/openapi-ts/gens';
import { Link, Wrench } from 'lucide-react';
import { useSearchParams, useRouter } from 'next/navigation';
import { useCallback } from 'react';

export default function Page() {
  const searchParams = useSearchParams();
  const router = useRouter();
  
  const activeTab = searchParams.get('tab') || 'builtin-tools';

  const handleTabChange = useCallback((value: string) => {
    const params = new URLSearchParams(searchParams);
    params.set('tab', value);
    router.push(`?${params.toString()}`);
  }, [searchParams, router]);

  return (
    <PageContainer>
      <div className="flex flex-col h-full">
        <div className="sticky top-0 z-10 bg-background pb-2">
          <PageHeader
            title="Connectors"
            description="Manage connectors and MCP servers for your environment"
          />
        </div>

        <Tabs value={activeTab} onValueChange={handleTabChange} className="space-y-2">
          <div className="sticky top-20 z-10 bg-background pb-1">
            <TabsList>
              <TabsTrigger value="builtin-tools" className="flex items-center gap-2">
                <Wrench className="h-4 w-4" />
                <span>Builtin Tools</span>
              </TabsTrigger>
              <TabsTrigger value="builtin-connections" className="flex items-center gap-2">
                <Link className="h-4 w-4" />
                <span>Builtin Connections</span>
              </TabsTrigger>
              <TabsTrigger value="mcp" className="flex items-center gap-2">
                <Link className="h-4 w-4" />
                <span>MCP</span>
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="builtin-tools">
            <ToolList />
          </TabsContent>

          <TabsContent value="builtin-connections">
            <ConnectionList type={ConnectionType.builtin} />
          </TabsContent>

          <TabsContent value="mcp">
            <ConnectionList type={ConnectionType.mcp} />
          </TabsContent>
        </Tabs>
      </div>
    </PageContainer>
  );
}
