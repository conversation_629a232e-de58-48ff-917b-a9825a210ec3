import { NewPageContainer } from '@/components/layout/new-page-container';
import { PageHeader } from '@/components/layout/page-header';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ChangePasswordCard } from '@/features/user/components/change-password-card';
import { ProfileCard } from '@/features/user/components/profile-card';

export const metadata = {
  title: 'Profile',
};

export default async function Page() {
  const tabs = [
    {
      label: 'Profile',
      content: <ProfileCard />,
    },
    {
      label: 'Change Password',
      content: <ChangePasswordCard />,
    },
  ];

  return (
    <NewPageContainer>
      <PageHeader
        title="My Profile"
        description="Manage your account settings and preferences"
      />
      <Tabs defaultValue={tabs[0].label}>
        <TabsList>
          {tabs.map((tab) => (
            <TabsTrigger key={tab.label} value={tab.label}>
              {tab.label}
            </TabsTrigger>
          ))}
        </TabsList>
        {tabs.map((tab) => (
          <TabsContent key={tab.label} value={tab.label}>
            {tab.content}
          </TabsContent>
        ))}
      </Tabs>
    </NewPageContainer>
  );
}
