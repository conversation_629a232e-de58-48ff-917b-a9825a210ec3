import { useCallback } from 'react';

import { McpServerService } from '@/client/sdk.gen';
import type {
  MCPServerCreateSchema,
  MCPServerResponseSchema,
  MCPServerUpdateSchema,
} from '@/client/types.gen';
import { <PERSON><PERSON><PERSON><PERSON> } from '@/components/utils/cache-key';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

import { useAgentConnectors } from './use-agent-connectors';

// Export the type that item-listing.tsx expects
export type DisplayMCPServerInfo = MCPServerResponseSchema;

interface UseMcpServersReturn {
  servers: MCPServerResponseSchema[];
  isLoading: boolean;
  error: Error | null;
  agentMcpServerMap: Record<string, DisplayMCPServerInfo[]>;
  addServer: (server: MCPServerCreateSchema) => Promise<void>;
  updateServer: (
    serverId: string,
    server: MCPServerUpdateSchema,
  ) => Promise<void>;
  deleteServer: (serverId: string) => Promise<void>;
  refreshServer: (serverId: string) => Promise<void>;
  refreshAllServers: () => Promise<void>;
  isRefreshing: (serverId: string) => boolean;
}

export const useMcpServers = (): UseMcpServersReturn => {
  const queryClient = useQueryClient();

  // Get agent connector data to create the agent-MCP server mapping
  const { data: agentConnectors } = useAgentConnectors();

  // Fetch all servers using React Query
  const {
    data: servers = [],
    isLoading,
    error,
    refetch: refreshAllServers,
  } = useQuery({
    queryKey: [CacheKey.McpServers],
    queryFn: async () => {
      const response = await McpServerService.getMcpServers();
      return response.data;
    },
    staleTime: 30 * 1000, // Consider data fresh for 30 seconds
    gcTime: 5 * 60 * 1000, // Keep in cache for 5 minutes
  });

  // Create a map of server names to server details
  const serverMap = servers.reduce(
    (acc, server) => {
      acc[server.name] = server;
      return acc;
    },
    {} as Record<string, MCPServerResponseSchema>,
  );

  // Create a map of agent ID to their MCP servers with full server details
  const agentMcpServerMap =
    agentConnectors?.reduce(
      (acc, connector) => {
        if (connector && serverMap) {
          acc[connector.agent_id] = connector.mcp_servers
            .map((serverName) => serverMap[serverName])
            .filter(Boolean); // Filter out any undefined servers
        }
        return acc;
      },
      {} as Record<string, DisplayMCPServerInfo[]>,
    ) || {};

  // Add new server mutation
  const addServerMutation = useMutation({
    mutationFn: async (server: MCPServerCreateSchema) => {
      return await McpServerService.createMcpServer({
        requestBody: server,
      });
    },
    onSuccess: () => {
      // Invalidate and refetch servers
      queryClient.invalidateQueries({ queryKey: [CacheKey.McpServers] });
      toast.success('Server added successfully');
    },
    onError: (err: Error) => {
      toast.error('Failed to add server');
      throw err;
    },
  });

  // Update existing server mutation
  const updateServerMutation = useMutation({
    mutationFn: async ({
      serverId,
      server,
    }: {
      serverId: string;
      server: MCPServerUpdateSchema;
    }) => {
      return await McpServerService.updateMcpServer({
        serverId,
        requestBody: server,
      });
    },
    onSuccess: () => {
      // Invalidate and refetch servers
      queryClient.invalidateQueries({ queryKey: [CacheKey.McpServers] });
      toast.success('Server updated successfully');
    },
    onError: (err: Error) => {
      toast.error('Failed to update server');
      throw err;
    },
  });

  // Delete server mutation
  const deleteServerMutation = useMutation({
    mutationFn: async (serverId: string) => {
      return await McpServerService.deleteMcpServer({ serverId });
    },
    onSuccess: () => {
      // Invalidate and refetch servers
      queryClient.invalidateQueries({ queryKey: [CacheKey.McpServers] });
      toast.success('Server deleted successfully');
    },
    onError: (err: Error) => {
      toast.error('Failed to delete server');
      throw err;
    },
  });

  // Refresh single server
  const refreshServerMutation = useMutation({
    mutationFn: async (serverId: string) => {
      return await McpServerService.refreshMcpServer({ serverId });
    },
    onSuccess: (updatedServer, serverId) => {
      // Update the specific server in the cache
      queryClient.setQueryData<MCPServerResponseSchema[]>(
        [CacheKey.McpServers],
        (oldData) => {
          if (!oldData) return [];
          return oldData.map((server) =>
            server.id === serverId ? updatedServer : server,
          );
        },
      );
      toast.success('Server refreshed successfully');
    },
    onError: (err: Error, serverId) => {
      toast.error(
        `Failed to refresh server: ${err instanceof Error ? err.message : 'Unknown error'}`,
      );
      throw err;
    },
  });

  // Wrapper functions to maintain the original API
  const addServer = useCallback(
    async (server: MCPServerCreateSchema) => {
      await addServerMutation.mutateAsync(server);
    },
    [addServerMutation],
  );

  const updateServer = useCallback(
    async (serverId: string, server: MCPServerUpdateSchema) => {
      await updateServerMutation.mutateAsync({ serverId, server });
    },
    [updateServerMutation],
  );

  const deleteServer = useCallback(
    async (serverId: string) => {
      await deleteServerMutation.mutateAsync(serverId);
    },
    [deleteServerMutation],
  );

  const refreshServer = useCallback(
    async (serverId: string) => {
      await refreshServerMutation.mutateAsync(serverId);
    },
    [refreshServerMutation],
  );

  const refreshAllServersCallback = useCallback(async () => {
    await refreshAllServers();
  }, [refreshAllServers]);

  const isRefreshing = useCallback(
    (serverId: string) => {
      return (
        refreshServerMutation.isPending &&
        refreshServerMutation.variables === serverId
      );
    },
    [refreshServerMutation.isPending, refreshServerMutation.variables],
  );

  return {
    servers,
    isLoading:
      isLoading ||
      addServerMutation.isPending ||
      updateServerMutation.isPending ||
      deleteServerMutation.isPending,
    error: error as Error | null,
    agentMcpServerMap,
    addServer,
    updateServer,
    deleteServer,
    refreshServer,
    refreshAllServers: refreshAllServersCallback,
    isRefreshing,
  };
};
