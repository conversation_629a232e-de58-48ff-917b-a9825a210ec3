import { useMemo } from 'react';

import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import {
  NavigationConfigSchema,
  SidebarConfig,
} from '@/config/navigation-config.schema';
import pathsConfig from '@/config/paths.config';
import { useUserContext } from '@/features/user/provider/user-provider';
import { useSubscription } from '@/lib/hooks/useSubscription';
import { UserInfo } from '@/types/common.enum';
import clientCookie from 'js-cookie';
import {
  BookIcon,
  Building2Icon,
  CalendarIcon,
  CloudCogIcon,
  CreditCardIcon,
  HomeIcon,
  LinkIcon,
  MonitorIcon,
  ShuffleIcon,
  TriangleAlertIcon,
  User,
  UsersIcon,
  ZapIcon,
} from 'lucide-react';

import useSaasEnabled from './useSaasEnabled';

export function useGetNavigationConfig() {
  const { user } = useUserContext();
  const { activeSubscription, isLoading, isFetched } = useSubscription();

  const currentWorkspaceId = clientCookie.get(UserInfo.WorkspacesID);
  const isInvitedUser = Boolean(
    currentWorkspaceId &&
      user.workspaces?.some(
        (w) =>
          w.id === currentWorkspaceId &&
          !user.own_workspaces?.some((ow) => ow.id === currentWorkspaceId),
      ),
  );

  // SaaS feature flag
  const { isEnabled: saasEnabled } = useSaasEnabled();

  const planNameDisplay = useMemo(() => {
    // Only show plan name if SaaS is enabled
    if (!saasEnabled) return 'Community';

    if (isLoading || !isFetched) {
      return <Skeleton className="h-4 w-16" />;
    }
    return activeSubscription?.product_name || 'Starter';
  }, [isLoading, activeSubscription, isFetched, saasEnabled]);

  const routes = useMemo(
    () =>
      [
        {
          label: 'Operations',
          children: [
            {
              label: 'Home',
              path: pathsConfig.app.home,
              Icon: HomeIcon,
              end: true,
            },
            {
              label: 'Dashboard',
              path: pathsConfig.app.dashboard,
              Icon: MonitorIcon,
              end: true,
            },
            {
              label: 'Resources',
              path: pathsConfig.app.resources,
              Icon: CloudCogIcon,
              end: true,
            },
            {
              label: 'Recommendations',
              extraLabel: (
                <Badge variant="ghost-destructive" className="truncate">
                  Depre...
                </Badge>
              ),
              path: pathsConfig.app.recommendations,
              Icon: ShuffleIcon,
              end: true,
            },
            {
              label: 'Tasks',
              path: pathsConfig.app.tasks,
              Icon: CalendarIcon,
              end: true,
            },
            {
              label: 'Alerts',
              path: pathsConfig.app.alerts,
              Icon: TriangleAlertIcon,
              end: true,
            },

            {
              label: 'Integrations',
              path: pathsConfig.app.integrations,
              Icon: ZapIcon,
              end: true,
              hidden: isInvitedUser,
            },
          ].filter((route) => !route.hidden),
        },
        {
          label: 'Setup',
          children: [
            {
              label: 'Connections',
              path: pathsConfig.app.connectors,
              Icon: LinkIcon,
              end: true,
            },
            {
              label: 'Agents',
              path: pathsConfig.app.agents,
              Icon: UsersIcon,
              end: true,
            },
            {
              label: 'Knowledge',
              path: pathsConfig.app.knowledgeBase,
              Icon: BookIcon,
              end: true,
            },
            {
              label: 'Workspaces',
              path: pathsConfig.app.workspaces,
              Icon: Building2Icon,
              end: true,
              hidden: isInvitedUser,
            },
            // {
            //   label: 'Users',
            //   extraLabel: (
            //     <Badge variant="ghost-destructive" className="truncate">
            //       Deprecated
            //     </Badge>
            //   ),
            //   path: pathsConfig.app.users,
            //   Icon: UserIcon,
            //   end: true,
            //   hidden: isInvitedUser,
            // },
          ].filter((route) => !route.hidden),
        },
        {
          label: 'Settings',
          children: [
            {
              label: 'Profile',
              path: pathsConfig.app.profile,
              Icon: User,
            },
            {
              label: 'Subscription',
              extraLabel: (
                <Badge variant="ghost-primary">{planNameDisplay}</Badge>
              ),
              path: pathsConfig.app.subscription,
              Icon: CreditCardIcon,
              hidden: !saasEnabled || isInvitedUser,
            },
          ].filter((route) => !route.hidden),
        },
      ] satisfies SidebarConfig['routes'],
    [isInvitedUser, planNameDisplay, saasEnabled],
  );

  return NavigationConfigSchema.parse({
    routes,
  });
}
