import { isArray, isEqual, isObject, transform } from 'lodash';

/**
 * Represents a plain JavaScript object with string keys and unknown values
 */
type PlainObject = Record<string, unknown>;

/**
 * Creates a function that computes the differences between an object and a base object
 *
 * This utility finds what properties in the object are different from the base object,
 * returning a new object containing only the properties that differ. It handles nested objects
 * and arrays, preserving their structure in the result.
 *
 * @param base - The base object to compare against
 * @returns A function that takes an object and returns a partial object with only the differences
 */
export const differentObject =
  <K>(base: K | undefined) =>
  <T extends PlainObject>(object: T): Partial<T> => {
    /**
     * Internal recursive function that computes the differences between two objects
     *
     * @param object - The object to compare
     * @param base - The base object to compare against
     * @returns A partial object containing only the different properties
     */
    function changes(object: T, base?: T): Partial<T> {
      if (!base) return object;
      return transform(object, (result: Partial<T>, value: unknown, key) => {
        if (!isEqual(value, base[key])) {
          if (isArray(value))
            result[key as keyof T] = value as T[keyof T] | undefined;
          else {
            if (value || base[key])
              result[key as keyof T] =
                !(value instanceof Date) &&
                isObject(value) &&
                isObject(base[key])
                  ? (changes(value as T, base[key] as T) as
                      | T[keyof T]
                      | undefined)
                  : (value as T[keyof T] | undefined);
          }
        }
      });
    }
    return changes(object, base as T);
  };
