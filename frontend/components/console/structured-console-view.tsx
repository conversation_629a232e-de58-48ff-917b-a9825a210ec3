import React, { memo, useEffect, useMemo, useRef, useState } from 'react';

import { ToolCall as OriginalToolCall } from '@/components/chat/types';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Brain, ChevronLeft, ChevronRight } from 'lucide-react';

import { TERMINAL_CONFIG } from './config';

// Simplified interface matching the updated structure
interface ProcessedToolCall {
  id: string;
  name: string;
  status: string; // Use string to match original status
  script?: string;
  output?: string;
  reasoning?: string;
  timestamp?: string;
}

interface StructuredConsoleViewProps {
  toolCalls?: OriginalToolCall[];
}

/**
 * Helper function to process tool call output data
 */
const processToolCallOutput = (
  toolCall: OriginalToolCall,
): ProcessedToolCall => {
  // Extract script/command from arguments
  const args = toolCall.arguments || {};
  const script = args.script;

  let console_output = '';
  try {
    console_output = (toolCall.output as Record<string, any>).output.stdout;
  } catch (error) {
    console.log('error processing output:', error);
    console_output = 'Error processing output';
  }

  // Extract reasoning from the correct location - it's in arguments.reasoning or thought field
  const reasoning =
    (args as any).reasoning || toolCall.thought || toolCall.reasoning || '';

  return {
    id: toolCall.id,
    name: toolCall.name,
    status: toolCall.status,
    script: script as string,
    output: console_output,
    reasoning: reasoning,
    timestamp: toolCall.startTime?.toISOString() || new Date().toISOString(),
  };
};

/**
 * Helper function to calculate dynamic line length based on container width
 */
const useResponsiveLineLength = (
  containerRef: React.RefObject<HTMLDivElement | null>,
) => {
  const [lineLength, setLineLength] = useState(80);

  useEffect(() => {
    const calculateLineLength = () => {
      if (containerRef.current) {
        const containerWidth = containerRef.current.offsetWidth;
        // Estimate characters per line based on container width
        // Assuming monospace font with ~8px character width and accounting for padding
        const estimatedCharsPerLine = Math.floor((containerWidth - 32) / 8); // 32px for padding
        setLineLength(Math.max(40, Math.min(120, estimatedCharsPerLine))); // Min 40, max 120
      }
    };

    calculateLineLength();
    window.addEventListener('resize', calculateLineLength);

    return () => window.removeEventListener('resize', calculateLineLength);
  }, [containerRef]);

  return lineLength;
};

/**
 * Helper function to format bash commands for terminal display
 * Breaks long commands into multiple lines with proper continuation
 */
const formatBashCommand = (
  command: string,
  maxLineLength: number = 80,
): string[] => {
  if (!command) return [];

  // Split by newlines to preserve original structure including blank lines
  const lines = command.split('\n');
  const allLines: string[] = [];

  lines.forEach((line, lineIndex) => {
    const trimmedLine = line.trim();

    // Handle blank lines - add them as empty strings to preserve spacing
    if (!trimmedLine) {
      allLines.push('');
      return;
    }

    // Handle lines with semicolons - split them into separate commands
    const commands = trimmedLine.split(';').filter((cmd) => cmd.trim());

    commands.forEach((singleCommand, cmdIndex) => {
      const trimmedCommand = singleCommand.trim();
      if (!trimmedCommand) return;

      // Add separator between multiple commands on same line (except for the first one)
      if (cmdIndex > 0) {
        allLines.push('');
      }

      // Split long single commands
      const words = trimmedCommand.split(' ');
      let currentLine = '';

      for (let i = 0; i < words.length; i++) {
        const word = words[i];
        const testLine = currentLine ? `${currentLine} ${word}` : word;

        if (testLine.length <= maxLineLength) {
          currentLine = testLine;
        } else {
          if (currentLine) {
            // Add continuation character
            allLines.push(currentLine + ' \\');
            currentLine = `  ${word}`; // Indent continuation with 2 spaces
          } else {
            // Word itself is too long, force it on its own line
            allLines.push(word);
          }
        }
      }

      if (currentLine) {
        allLines.push(currentLine);
      }
    });
  });

  return allLines;
};

/**
 * Helper function to format output text for better readability
 */
const formatOutputText = (
  output: string,
  maxLineLength: number = 80,
): string[] => {
  if (!output) return [];

  const lines = output.split('\n');
  const formattedLines: string[] = [];

  lines.forEach((line) => {
    if (line.length <= maxLineLength) {
      formattedLines.push(line);
    } else {
      // Split long lines at word boundaries
      const words = line.split(' ');
      let currentLine = '';

      for (const word of words) {
        const testLine = currentLine ? `${currentLine} ${word}` : word;

        if (testLine.length <= maxLineLength) {
          currentLine = testLine;
        } else {
          if (currentLine) {
            formattedLines.push(currentLine);
            currentLine = word;
          } else {
            formattedLines.push(word);
          }
        }
      }

      if (currentLine) {
        formattedLines.push(currentLine);
      }
    }
  });

  return formattedLines;
};

/**
 * StructuredConsoleView - Authentic terminal interface
 * Simulates a real bash terminal with proper command formatting and navigation
 */
export const StructuredConsoleView = memo(
  ({ toolCalls = [] }: StructuredConsoleViewProps) => {
    const [currentIndex, setCurrentIndex] = useState(0);
    const containerRef = useRef<HTMLDivElement>(null);
    const scrollAreaRef = useRef<HTMLDivElement>(null);
    const responsiveLineLength = useResponsiveLineLength(containerRef);

    // Process and filter tool calls to only show script-related tools
    const scriptToolCalls = useMemo(() => {
      return toolCalls
        .filter((toolCall) =>
          TERMINAL_CONFIG.toolCalls.scriptKeywords.some((keyword) =>
            toolCall.name.includes(keyword),
          ),
        )
        .map(processToolCallOutput)
        .sort(
          (a, b) =>
            new Date(a.timestamp || 0).getTime() -
            new Date(b.timestamp || 0).getTime(),
        );
    }, [toolCalls]);

    // Auto-scroll to bottom function
    const scrollToBottom = () => {
      if (scrollAreaRef.current) {
        const scrollContainer = scrollAreaRef.current.querySelector(
          '[data-radix-scroll-area-viewport]',
        );
        if (scrollContainer) {
          scrollContainer.scrollTop = scrollContainer.scrollHeight;
        }
      }
    };

    // Jump to latest tool call when new ones are added
    useEffect(() => {
      if (scriptToolCalls.length > 0) {
        setCurrentIndex(scriptToolCalls.length - 1);
      }
    }, [scriptToolCalls.length]);

    // Auto-scroll to bottom when component mounts or content changes
    useEffect(() => {
      scrollToBottom();
    }, [currentIndex, scriptToolCalls]);

    // Auto-scroll to bottom when tool call status changes (e.g., from processing to completed)
    useEffect(() => {
      const currentToolCall = scriptToolCalls[currentIndex];
      if (currentToolCall) {
        scrollToBottom();
      }
    }, [
      scriptToolCalls[currentIndex]?.status,
      scriptToolCalls[currentIndex]?.output,
    ]);

    const currentToolCall = scriptToolCalls[currentIndex];

    const handlePrevious = () => {
      setCurrentIndex((prev) => Math.max(0, prev - 1));
    };

    const handleNext = () => {
      setCurrentIndex((prev) => Math.min(scriptToolCalls.length - 1, prev + 1));
    };

    if (scriptToolCalls.length === 0) {
      return (
        <div
          ref={containerRef}
          className="flex h-full items-center justify-center bg-black font-mono text-sm text-green-400"
        >
          <div className="space-y-2 text-center">
            <div className="text-gray-500">No bash scripts executed</div>
            <div className="flex items-center gap-1">
              <span className="text-green-400">sandbox$</span>
              <span className="animate-pulse">_</span>
            </div>
          </div>
        </div>
      );
    }

    if (!currentToolCall) return null;

    const commandLines = formatBashCommand(
      currentToolCall.script || '',
      responsiveLineLength,
    );
    const outputLines = formatOutputText(
      currentToolCall.output || '',
      responsiveLineLength,
    );

    // Helper function to render terminal lines with consistent formatting
    const renderCommandLines = (lines: string[]) => {
      // Find the first non-empty line to show sandbox$ prompt
      const firstNonEmptyIndex = lines.findIndex((line) => line.trim() !== '');

      return lines.map((line, index) => {
        // Handle blank lines
        if (line.trim() === '') {
          return <div key={`command-${index}`} className="h-4"></div>;
        }

        return (
          <div key={`command-${index}`} className="flex items-start gap-1">
            {index === firstNonEmptyIndex ? (
              <>
                <span className="shrink-0 text-green-400">sandbox$</span>
                <span className="break-all text-white">{line}</span>
              </>
            ) : (
              <span className="break-all text-white">{line}</span>
            )}
          </div>
        );
      });
    };

    const renderOutputLines = (lines: string[]) => {
      // Find the first non-empty line to show sandbox$ prompt
      const firstNonEmptyIndex = lines.findIndex((line) => line.trim() !== '');

      return lines.map((line, index) => {
        // Handle blank lines in output
        if (line.trim() === '') {
          return <div key={`output-${index}`} className="h-4"></div>;
        }

        return (
          <div key={`output-${index}`} className="flex items-start gap-1">
            {index === firstNonEmptyIndex ? (
              <>
                <span className="shrink-0 text-green-400">sandbox$</span>
                <span className="break-all text-white">{line}</span>
              </>
            ) : (
              <span className="break-all text-white">{line}</span>
            )}
          </div>
        );
      });
    };

    return (
      <div
        ref={containerRef}
        className="flex h-full flex-col bg-black font-mono text-sm text-green-400"
      >
        {/* Terminal Content - Full height */}
        <div className="flex flex-1 flex-col overflow-hidden">
          <ScrollArea ref={scrollAreaRef} className="flex-1 p-4">
            <div className="space-y-0">
              {/* Command execution with proper bash formatting */}
              {renderCommandLines(commandLines)}

              {/* Output or processing indicator */}
              {currentToolCall.status !== 'completed' ? (
                <div className="flex items-center gap-1">
                  <span className="text-green-400">sandbox$</span>
                  <span className="animate-pulse text-white">...</span>
                </div>
              ) : (
                <div className="mt-1">{renderOutputLines(outputLines)}</div>
              )}
            </div>
          </ScrollArea>

          {/* Bottom navigation and status bar */}
          <div className="border-t border-gray-700 bg-gray-900">
            {/* Progress and navigation bar */}
            <div className="flex items-center gap-2 px-3 py-2 text-xs">
              {/* Progress bar */}
              <div className="h-1 flex-1 rounded-full bg-gray-700">
                <div
                  className="h-1 rounded-full bg-blue-500 transition-all duration-300"
                  style={{
                    width: `${((currentIndex + 1) / scriptToolCalls.length) * 100}%`,
                  }}
                ></div>
              </div>

              {/* Navigation controls */}
              <div className="flex items-center gap-1">
                <span className="text-gray-500">
                  {currentIndex + 1}/{scriptToolCalls.length}
                </span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handlePrevious}
                  disabled={currentIndex === 0}
                  className="h-5 w-5 p-0 text-gray-500 hover:text-white disabled:opacity-30"
                >
                  <ChevronLeft className="h-3 w-3" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleNext}
                  disabled={currentIndex === scriptToolCalls.length - 1}
                  className="h-5 w-5 p-0 text-gray-500 hover:text-white disabled:opacity-30"
                >
                  <ChevronRight className="h-3 w-3" />
                </Button>
              </div>

              {/* Live indicator */}
              <div className="ml-2 flex items-center gap-1">
                <div className="h-2 w-2 animate-pulse rounded-full bg-green-500"></div>
                <span className="font-medium text-green-400">live</span>
              </div>
            </div>

            {/* Thinking section - collapsible */}
            {currentToolCall.reasoning && (
              <div className="border-t border-gray-800 bg-gray-950">
                <div className="max-h-24 overflow-hidden p-3">
                  <div className="mb-2 flex items-center gap-2">
                    <Brain className="h-3 w-3 text-blue-400" />
                    <span className="text-xs font-semibold tracking-wide text-blue-400 uppercase">
                      Thinking
                    </span>
                  </div>
                  <ScrollArea className="max-h-16">
                    <div className="pr-2 text-xs leading-relaxed break-words text-gray-400">
                      {currentToolCall.reasoning}
                    </div>
                  </ScrollArea>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  },
);

StructuredConsoleView.displayName = 'StructuredConsoleView';
