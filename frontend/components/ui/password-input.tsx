import { createElement, useState } from 'react';

import { EyeIcon, EyeOffIcon } from 'lucide-react';

import { Button } from './button';
import { Input, InputProps } from './input';

export function PasswordInput(props: InputProps) {
  const [showPassword, setShowPassword] = useState(false);
  return (
    <div className="relative">
      <Input type={showPassword ? 'text' : 'password'} {...props} />
      <Button
        type="button"
        variant="ghost"
        size="sm"
        className="absolute top-0 right-0 h-full px-3 py-2 hover:bg-transparent"
        onClick={() => setShowPassword(!showPassword)}
      >
        {createElement(showPassword ? EyeOffIcon : EyeIcon, {
          className: 'text-muted-foreground size-4',
        })}
      </Button>
    </div>
  );
}
