'use client';

import { useMemo, useState } from 'react';

import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { cn } from '@/lib/utils';
import { AnimatePresence, motion } from 'framer-motion';
import {
  AlertCircle,
  CheckCircle2,
  ChevronDown,
  ChevronUp,
  Circle,
  X,
} from 'lucide-react';

import { ToolCall } from '../types';
import { BotAvatar } from './common/bot-icon';

interface PlanningSessionProps {
  planningTools: ToolCall[];
  onClose?: () => void;
}

interface PlanStep {
  index: number;
  description: string;
  status: 'not_started' | 'completed' | 'blocked';
  status_symbol: '[ ]' | '[✓]' | '[!]';
  notes: string;
}

interface PlanData {
  plan_id: string;
  title: string;
  progress: {
    completed: number;
    total: number;
    percentage: number;
    status_summary: {
      completed: number;
      blocked: number;
      not_started: number;
    };
  };
  steps: PlanStep[];
}

interface PlanOutput {
  status: string;
  data: {
    plan: PlanData & { plan_status?: string; status_reason?: string };
  };
}

const parsePlanData = (
  output: Record<string, unknown>,
): (PlanData & { plan_status?: string; status_reason?: string }) | null => {
  try {
    let data: PlanOutput;
    if (typeof output === 'string') {
      data = JSON.parse(output) as unknown as PlanOutput;
    } else {
      data = output as unknown as PlanOutput;
    }

    if (data.status === 'success' && data.data?.plan) {
      return data.data.plan;
    }
    return null;
  } catch (error) {
    // Silently fail, as this can be noisy during plan generation
    return null;
  }
};

export function PlanningSession({
  planningTools,
  onClose,
}: PlanningSessionProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const currentPlan = useMemo(() => {
    const plans = planningTools
      .map((tool) => {
        if (!tool.output) return null;
        const planData = parsePlanData(tool.output as Record<string, unknown>);
        return planData ? { tool, planData } : null;
      })
      .filter(
        (
          p,
        ): p is {
          tool: ToolCall;
          planData: NonNullable<ReturnType<typeof parsePlanData>>;
        } => p !== null,
      );

    if (plans.length > 0) {
      return plans[plans.length - 1];
    }
    return null;
  }, [planningTools]);

  const isGenerating = useMemo(
    () => planningTools.some((t) => t.status === 'running') && !currentPlan,
    [planningTools, currentPlan],
  );

  if (isGenerating) {
    return (
      <motion.div
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -10 }}
        className="border-muted-foreground/10 bg-background/50 mb-4 rounded-lg border p-3 backdrop-blur-sm"
      >
        <div className="flex items-center gap-3">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
            className="border-primary h-4 w-4 shrink-0 rounded-full border-2 border-t-transparent"
          />
          <span className="text-foreground text-sm">Generating plan...</span>
        </div>
      </motion.div>
    );
  }

  if (!currentPlan) {
    return null;
  }

  const { planData, tool } = currentPlan;
  const planStatus = planData.plan_status || 'in_progress';
  const progressPercentage = planData.progress.percentage;

  // Get the current task (first not completed step)
  const currentTask = planData.steps.find(
    (step) => step.status !== 'completed',
  );

  // Hide the planning session when all tasks are completed successfully
  if (planStatus === 'completed' && progressPercentage === 100) {
    return null;
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      className={cn(
        'border-muted-foreground/10 bg-background/50 mb-4 rounded-lg border p-3 backdrop-blur-sm transition-all duration-300',
        isExpanded && 'max-h-[40vh] overflow-y-auto',
      )}
    >
      <div className="space-y-2">
        {/* Header */}
        <div className="flex items-center justify-between gap-2">
          <div className="flex items-center gap-2 overflow-hidden">
            <BotAvatar
              role={tool.agentRole || 'assistant'}
              variant="compact"
              hideRoleText={true}
            />
            <div className="min-w-0 flex-1">
              <span className="text-foreground truncate text-sm font-medium">
                {planData.title}
              </span>
              {!isExpanded && currentTask && (
                <p className="text-muted-foreground mt-0.5 truncate text-xs">
                  {currentTask.description}
                </p>
              )}
            </div>
          </div>
          <div className="flex items-center">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsExpanded(!isExpanded)}
              className="h-6 w-6 shrink-0"
            >
              {isExpanded ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </Button>
            {onClose && (
              <Button
                variant="ghost"
                size="icon"
                onClick={onClose}
                className="h-6 w-6 shrink-0"
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>

        {/* Progress Bar - only show when expanded */}
        {isExpanded && (
          <div className="space-y-1">
            <div className="flex justify-between text-xs">
              <span className="text-muted-foreground">
                {planData.progress.completed}/{planData.progress.total} steps
              </span>
              <span
                className={cn(
                  'font-medium',
                  planStatus === 'blocked' && 'text-yellow-500',
                  planStatus === 'completed' && 'text-green-500',
                )}
              >
                {planStatus === 'blocked'
                  ? 'Blocked'
                  : planStatus === 'completed'
                    ? 'Completed'
                    : `${Math.round(progressPercentage)}%`}
              </span>
            </div>
            <Progress
              value={planStatus === 'completed' ? 100 : progressPercentage}
              className={cn(
                'h-1.5',
                planStatus === 'blocked' && '[&>div]:bg-yellow-500',
                planStatus === 'completed' && '[&>div]:bg-green-500',
              )}
            />
          </div>
        )}

        {/* Expanded Steps List */}
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="overflow-hidden pt-2"
            >
              <div className="border-muted-foreground/10 space-y-2 border-t pt-2">
                {planData.steps.map((step) => (
                  <div
                    key={step.index}
                    className="flex items-start gap-3 text-sm"
                  >
                    <div
                      className={cn(
                        'mt-0.5 shrink-0',
                        step.status === 'completed' && 'text-green-500',
                        step.status === 'blocked' && 'text-yellow-500',
                        step.status === 'not_started' && 'text-gray-400',
                      )}
                    >
                      {step.status === 'completed' && (
                        <CheckCircle2 className="h-4 w-4" />
                      )}
                      {step.status === 'blocked' && (
                        <AlertCircle className="h-4 w-4" />
                      )}
                      {step.status === 'not_started' && (
                        <Circle className="h-4 w-4" />
                      )}
                    </div>
                    <div className="min-w-0 flex-1">
                      <p className="text-foreground">{step.description}</p>
                      {step.notes && (
                        <p className="text-muted-foreground mt-1 text-xs">
                          {step.notes}
                        </p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.div>
  );
}
