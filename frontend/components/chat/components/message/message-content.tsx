import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from 'react';

import { MessageDisplayComponentPublic } from '@/client/types.gen';
import { MarkdownRenderer } from '@/components/markdown';
import { useUserContextSafe } from '@/features/user/provider/user-provider';
import { InterruptConfirmation } from '@/hooks/message-stream';

import { ContentPart, ToolCall } from '../../types';
import { ThinkingProcessSection } from '../common/thinking-process-section';
import { InlineConfirmation } from '../tools/tool-call-interrupt';
import { ToolCallRenderer } from '../tools/tool-call-renderer';
import { DisplayComponent } from './display-component';

interface MessageContentProps {
  contentParts: ContentPart[];
  nextContent: string | null;
  toolCalls?: ToolCall[];
  showToolCalls?: boolean;
  isStreaming?: boolean;
  openSections: number[];
  onToggleSection: (index: number) => void;
  onNextStepClick?: (content: string) => void;
  displayComponents?: MessageDisplayComponentPublic[];
  confirmation?: InterruptConfirmation | null;
  groupChatOnly?: boolean;
  agentInfo?: {
    name: string;
    role: string;
  };
}

// Helper component for group chat section
const GroupChatSection = ({ content }: { content: string }) => (
  <div className="border-primary/30 bg-primary/5 my-2 rounded-lg border p-4 text-sm">
    <MarkdownRenderer content={content} enableMentions={true} />
  </div>
);

// Helper to parse thought into sections (thinking, group_chat, markdown)
function parseThoughtSections(thought: string) {
  const sections: {
    type: 'thinking' | 'group_chat' | 'markdown';
    content: string;
  }[] = [];
  const text = thought;
  const pattern =
    /(<thinking>([\s\S]*?)(?:\n?<\/thinking>|$))|(<group_chat>([\s\S]*?)(?:<\/group_chat>|$))/g;
  let lastIndex = 0;
  let match;
  while ((match = pattern.exec(text)) !== null) {
    if (match.index > lastIndex) {
      // Markdown between blocks
      const md = text.slice(lastIndex, match.index).trim();
      if (md) sections.push({ type: 'markdown', content: md });
    }
    if (match[1]) {
      // Thinking block (may be open or closed)
      sections.push({ type: 'thinking', content: match[2].trim() });
    } else if (match[3]) {
      // Group chat block
      sections.push({ type: 'group_chat', content: match[4].trim() });
    }
    lastIndex = pattern.lastIndex;
  }
  // Any remaining markdown after last block
  if (lastIndex < text.length) {
    const md = text.slice(lastIndex).trim();
    if (md) sections.push({ type: 'markdown', content: md });
  }
  return sections;
}

export const MessageContent = ({
  contentParts,
  nextContent,
  toolCalls,
  showToolCalls = false,
  isStreaming = false,
  openSections,
  onToggleSection,
  onNextStepClick,
  displayComponents = [],
  confirmation,
  groupChatOnly = false,
  agentInfo,
}: MessageContentProps) => {
  const [showConfirmation, setShowConfirmation] = useState(true);
  const userContext = useUserContextSafe();
  const user = userContext?.user;

  useEffect(() => {
    if (confirmation) {
      setShowConfirmation(true);
    }
  }, [confirmation]);

  const handleConfirm = useCallback(() => {
    if (confirmation) {
      confirmation.onConfirm();
      setShowConfirmation(false);
    }
  }, [confirmation]);

  const handleCancel = useCallback(
    (cancelMessage: string) => {
      if (confirmation) {
        confirmation.onCancel(cancelMessage);
        setShowConfirmation(false);
      }
    },
    [confirmation],
  );

  // Merge tool calls and display components, sort by position
  const mergedByPosition = [
    ...(toolCalls || []).map((tc) => ({
      type: 'toolCall' as const,
      id: tc.id,
      position: typeof tc.position === 'number' ? tc.position : 0,
      item: tc,
    })),
    ...(displayComponents || []).map((dc) => ({
      type: 'displayComponent' as const,
      id: dc.id,
      position: typeof dc.position === 'number' ? dc.position : 0,
      item: dc,
    })),
  ].sort((a, b) => a.position - b.position || a.id.localeCompare(b.id));

  return (
    <div className="space-y-4">
      {/* Group Chat Only Mode */}
      {groupChatOnly ? (
        <>
          {/* Render group chat sections from toolCalls */}
          {toolCalls &&
            toolCalls.length > 0 &&
            toolCalls.map((tc, i) => {
              if (tc.name === 'group_chat') {
                let parsedContent: string = '';
                try {
                  if (typeof tc.arguments === 'string') {
                    const parsed = JSON.parse(tc.arguments);
                    if (user) {
                      const messageWithMention = parsed.message.replace(
                        /@Customer/g,
                        `@${user?.full_name?.replace(/\s+/g, '')}`,
                      );
                      parsedContent = `${messageWithMention}`;
                    } else {
                      parsedContent = parsed.message;
                    }
                  } else {
                    // Handle case where arguments is already an object
                    const args = tc.arguments as any;
                    parsedContent = args?.message || '';
                  }
                } catch (e) {
                  console.warn('Failed to parse group chat arguments:', e);
                  parsedContent = '';
                }
                return (
                  <div key={`group-chat-${i}`} className="text-sm">
                    <MarkdownRenderer
                      content={parsedContent}
                      enableMentions={true}
                    />
                  </div>
                );
              }
              return null;
            })}
        </>
      ) : (
        <>
          {/* Merged tool calls and display components, sorted by position */}
          {mergedByPosition.length > 0 && (
            <div className="mt-4 space-y-4">
              {mergedByPosition.map(({ type, id, item }, index) => {
                const position =
                  typeof item.position === 'number' ? item.position : index;
                if (type === 'toolCall') {
                  let thoughtContent = null;
                  let toolContent = null;

                  if (item.thought) {
                    const sections = parseThoughtSections(item.thought);
                    thoughtContent = (
                      <div key={id}>
                        {sections.map((section, i) => {
                          if (section.type === 'thinking') {
                            return (
                              <ThinkingProcessSection
                                key={`${id}-thinking-${i}`}
                                content={section.content}
                                index={position}
                                isOpen={openSections.includes(position)}
                                onToggle={() => onToggleSection(position)}
                                isStreaming={isStreaming}
                              />
                            );
                          } else {
                            return (
                              <div
                                key={`${id}-markdown-${i}`}
                                className="text-sm"
                              >
                                <MarkdownRenderer
                                  content={section.content}
                                  enableMentions={true}
                                />
                              </div>
                            );
                          }
                        })}
                      </div>
                    );
                  }

                  if (item.name === 'thought') {
                    toolContent = null;
                  } else if (item.name === 'group_chat') {
                    let parsedContent: string = '';
                    if (user) {
                      const messageWithMention =
                        item.arguments &&
                        typeof item.arguments === 'object' &&
                        'message' in item.arguments &&
                        item.arguments.message
                          ? (
                              item.arguments as { message: string }
                            ).message.replace(
                              /@Customer/g,
                              `@${user?.full_name?.replace(/\s+/g, '')}`,
                            )
                          : '';
                      parsedContent = `${messageWithMention}`;
                    } else {
                      parsedContent = (item.arguments as { message: string })
                        .message;
                    }
                    toolContent = parsedContent ? (
                      <GroupChatSection
                        key={`${id}-group-chat-parsed`}
                        content={parsedContent}
                      />
                    ) : null;
                  } else {
                    toolContent = (
                      <ToolCallRenderer
                        toolCall={item}
                        agentInfo={agentInfo}
                        useAnimations={true}
                      />
                    );
                  }

                  return (
                    <div key={`${id}-message-content-${index}`}>
                      {thoughtContent}
                      {toolContent}
                    </div>
                  );
                } else {
                  return <DisplayComponent key={id} component={item} />;
                }
              })}
            </div>
          )}

          {/* Confirmation dialogs - moved to end */}
          {confirmation && showConfirmation && (
            <div className="mt-4">
              <InlineConfirmation
                confirmation={{
                  ...confirmation,
                  onConfirm: handleConfirm,
                  onCancel: handleCancel,
                }}
              />
            </div>
          )}
        </>
      )}
    </div>
  );
};
