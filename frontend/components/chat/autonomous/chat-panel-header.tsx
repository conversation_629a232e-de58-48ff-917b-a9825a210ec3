'use client';

import { memo } from 'react';

import { usePathname } from 'next/navigation';

import { ShareButton } from '@/app/(home)/agents/_components/share-button';
import { ConversationHistoryPopover } from '@/components/conversation-history/conversation-history-popover';
import { Button } from '@/components/ui/button';
import { Loader2, MessageSquarePlus } from 'lucide-react';

import { ConversationHeader } from '../conversation-header';

export interface ChatPanelHeaderContentProps {
  isCreatingConversation: boolean;
  conversationId?: string;
  conversationTitle?: string;
  conversationCreatedAt?: string;
  modelProvider?: string;
  resourceId?: string;
}

export interface ChatPanelHeaderActionsContentProps {
  onNewChat: () => void;
  isCreatingConversation: boolean;
  conversationId?: string;
  isSharedView?: boolean;
}

const MemoizedConversationHeader = memo(ConversationHeader);

export const ChatPanelHeaderContent = memo(
  ({
    isCreatingConversation,
    conversationId,
    conversationTitle,
    conversationCreatedAt,
    modelProvider,
    resourceId,
  }: ChatPanelHeaderContentProps) => {
    const pathname = usePathname();

    // Check if we're on the resource detail page
    const isOnResourcePage =
      resourceId && pathname === `/resources/${resourceId}`;

    if (isCreatingConversation) {
      return (
        <div className="flex items-center justify-center">
          <Loader2 className="text-muted-foreground mr-2 h-4 w-4 animate-spin" />
          <span className="text-muted-foreground text-sm">
            Creating conversation...
          </span>
        </div>
      );
    }

    if (conversationId || resourceId) {
      return (
        <div className="flex items-center gap-2">
          {/* Show conversation title if we have conversationId */}
          {conversationId && conversationTitle && (
            <MemoizedConversationHeader
              conversationId={conversationId}
              conversationTitle={conversationTitle}
              conversationCreatedAt={conversationCreatedAt}
              modelProvider={modelProvider}
            />
          )}
        </div>
      );
    }
    return null;
  },
);
ChatPanelHeaderContent.displayName = 'ChatPanelHeaderContent';

export const ChatPanelHeaderActionsContent = memo(
  ({
    onNewChat,
    isCreatingConversation,
    conversationId,
    isSharedView = false,
  }: ChatPanelHeaderActionsContentProps) => {
    if (isSharedView) return null;

    return (
      <div className="relative flex items-center justify-end gap-2">
        {conversationId && <ShareButton conversationId={conversationId} />}
        <ConversationHistoryPopover key="inChat" />
        <Button
          variant="ghost"
          size="sm"
          onClick={onNewChat}
          disabled={isCreatingConversation}
          className="gap-2"
        >
          {isCreatingConversation ? (
            <Loader2 className="size-4 animate-spin" />
          ) : (
            <MessageSquarePlus className="size-4" />
          )}
          <span className="@max-lg:hidden">New chat</span>
        </Button>
      </div>
    );
  },
);
ChatPanelHeaderActionsContent.displayName = 'ChatPanelHeaderActionsContent';
