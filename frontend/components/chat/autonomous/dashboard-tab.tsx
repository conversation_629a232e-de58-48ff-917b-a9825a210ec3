import React, { useRef } from 'react';

import { Dashboard } from '@/client';
import { ScrollArea } from '@/components/ui/scroll-area';
import { LayoutDashboard, Loader2 } from 'lucide-react';

import DashboardGrid from './dashboard-content/dashboard-grid';
import DashboardHeader from './dashboard-content/dashboard-header';

export interface DashboardTabProps {
  dashboard?: Dashboard | null;
  isLoading?: boolean;
  conversationId?: string;
}

const DashboardTab: React.FC<DashboardTabProps> = ({
  dashboard,
  isLoading = false,
  conversationId,
}) => {
  const dashboardContentRef = useRef<HTMLDivElement>(null);

  const handleExportPDF = async () => {
    if (!dashboardContentRef.current || !dashboard) return;
    console.log('Dashboard PDF export will be implemented later');
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="flex flex-1 items-center justify-center">
        <div className="flex flex-col items-center gap-3">
          <Loader2 className="text-muted-foreground h-8 w-8 animate-spin" />
          <p className="text-muted-foreground text-sm">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  // Empty state when no dashboard exists
  if (!dashboard) {
    return (
      <div className="flex flex-1 items-center justify-center">
        <div className="mx-auto flex max-w-md flex-col items-center gap-4 p-6 text-center">
          <div className="bg-muted/50 flex h-16 w-16 items-center justify-center rounded-full">
            <LayoutDashboard className="text-muted-foreground h-8 w-8" />
          </div>
          <div>
            <h3 className="text-foreground mb-2 text-lg font-semibold">
              No Dashboard Generated
            </h3>
            <p className="text-muted-foreground text-sm leading-relaxed">
              Dashboards will appear here when generated by the agent. Ask the
              agent to create a dashboard to see data visualizations and
              key metrics by using #dashboard.
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Parse dashboard data if it's a string
  let dashboardData: any;
  try {
    if (typeof dashboard === 'string') {
      dashboardData = JSON.parse(dashboard);
    } else if (typeof dashboard === 'object' && dashboard !== null) {
      dashboardData = dashboard;
    } else {
      // Invalid dashboard data, show empty state
      return (
        <div className="flex flex-1 items-center justify-center">
          <div className="mx-auto flex max-w-md flex-col items-center gap-4 p-6 text-center">
            <div className="bg-muted/50 flex h-16 w-16 items-center justify-center rounded-full">
              <LayoutDashboard className="text-muted-foreground h-8 w-8" />
            </div>
            <div>
              <h3 className="text-foreground mb-2 text-lg font-semibold">
                Invalid Dashboard Data
              </h3>
              <p className="text-muted-foreground text-sm leading-relaxed">
                The dashboard data appears to be corrupted. Please try refreshing
                or ask the agent to regenerate the dashboard.
              </p>
            </div>
          </div>
        </div>
      );
    }
  } catch (error) {
    // If parsing fails, show error state
    return (
      <div className="flex flex-1 items-center justify-center">
        <div className="mx-auto flex max-w-md flex-col items-center gap-4 p-6 text-center">
          <div className="bg-muted/50 flex h-16 w-16 items-center justify-center rounded-full">
            <LayoutDashboard className="text-muted-foreground h-8 w-8" />
          </div>
          <div>
            <h3 className="text-foreground mb-2 text-lg font-semibold">
              Dashboard Parsing Error
            </h3>
            <p className="text-muted-foreground text-sm leading-relaxed">
              Unable to parse the dashboard data. Please try refreshing or ask the
              agent to regenerate the dashboard.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-full flex-1 flex-col">
      <ScrollArea className="flex-1">
        <div ref={dashboardContentRef} className="w-full p-6">
          {/* Dashboard Header */}
          {dashboardData?.title && (
            <DashboardHeader
              title={dashboardData.title}
              createdAt={dashboardData.created_at}
              onExportPDF={handleExportPDF}
            />
          )}

          {/* Dashboard Grid */}
          {dashboardData?.widgets && Array.isArray(dashboardData.widgets) && (
            <DashboardGrid
              widgets={dashboardData.widgets}
              gridConfig={dashboardData.grid_config || { columns: 12 }}
            />
          )}

          {/* Fallback if no structured content exists but dashboard has data */}
          {dashboardData &&
            !dashboardData.title &&
            !dashboardData.widgets && (
              <div className="prose prose-sm max-w-none">
                <pre className="bg-muted/30 text-foreground rounded-lg p-4 text-sm whitespace-pre-wrap">
                  {JSON.stringify(dashboardData, null, 2)}
                </pre>
              </div>
            )}
        </div>
      </ScrollArea>
    </div>
  );
};

export default DashboardTab;
