import React, { useMemo } from 'react';

import { Message as MessageType } from '@/components/chat/types';
import { Button } from '@/components/ui/button';
import { MessageLoading } from '@/components/ui/message-loading';
import { ScrollArea } from '@/components/ui/scroll-area';
import { InterruptConfirmation } from '@/hooks/message-stream';
import { cn } from '@/lib/utils';
import { AnimatePresence, motion } from 'framer-motion';
import { ArrowDown, Bell } from 'lucide-react';

import { useScrollHandling } from '../hooks/useScrollHandling';
import { highlightMentions } from '../utils/mention-highlighting';
import { AutonomousMessage } from './message';

// Constants
const ANIMATION_DURATION = {
  FADE: 0.2,
  SLIDE: 0.3,
};

interface MessageListProps {
  messages: MessageType[];
  isLoading?: boolean;
  onNextStepClick?: (content: string) => void;
  onRestore?: (messageId: string, content: string) => void;
  confirmation?: InterruptConfirmation | null;
  className?: string;
  disableThinking?: boolean;
  disableDisplayComponents?: boolean;
  disableToolCalls?: boolean;
  groupChatOnly?: boolean;
  isSharedView?: boolean;
}

export function AutonomousMessageList({
  messages,
  isLoading,
  onNextStepClick,
  onRestore,
  confirmation,
  disableThinking = false,
  disableDisplayComponents = false,
  disableToolCalls = false,
  groupChatOnly = false,
  isSharedView = false,
}: MessageListProps) {
  const messagesEndRef = React.useRef<HTMLDivElement>(null);

  // Filter out system messages for counting
  const visibleMessages = useMemo(() => {
    return messages.filter((message) => message.role !== 'system');
  }, [messages]);

  // Use the custom scroll handling hook
  const {
    scrollAreaRef,
    showScrollButton,
    showInputScrollButton,
    autoScroll,
    unreadCount,
    newMessageHighlight,
    scrollToBottom,
    highlightNewMessage,
  } = useScrollHandling({
    isLoading,
    messageCount: visibleMessages.length,
  });

  // Auto-scroll to bottom when new messages arrive
  React.useEffect(() => {
    if (unreadCount > 0 || (isLoading && visibleMessages.length > 0)) {
      scrollToBottom('smooth', true);
    }
  }, [unreadCount, isLoading, visibleMessages.length, scrollToBottom]);

  // Track if we should show the thinking state
  const showThinkingState = React.useMemo(() => {
    // Show thinking when loading and either:
    // 1. No messages yet, or
    // 2. Last message is from user (waiting for response), or
    // 3. Last assistant message is empty (stream hasn't started yet)
    if (!isLoading) return false;

    if (messages.length === 0) return true;

    const lastMessage = messages[messages.length - 1];

    if (lastMessage.role === 'user') return true;

    // If last message is from assistant but has no content yet, still show thinking
    if (
      lastMessage.role === 'assistant' &&
      (!lastMessage.content || lastMessage.content.trim() === '')
    )
      return true;

    return false;
  }, [messages, isLoading]);

  // Process messages to apply highlighting
  const processMessageContent = React.useCallback((content: string) => {
    const { highlightedText, hasMentions } = highlightMentions(content);
    return hasMentions ? highlightedText : content;
  }, []);

  // Memoize message rendering to prevent unnecessary re-renders
  const renderMessages = React.useMemo(() => {
    const filteredMessages = messages.filter(
      (message) => message.role !== 'system',
    );

    return filteredMessages.map((message, index) => {
      // Create a proper contentDisplay if needed
      let contentDisplay = undefined;

      // Only process if it's a user message (AI messages are handled by MessageContent)
      if (message.role === 'user' && typeof message.content === 'string') {
        const { highlightedText, hasMentions } = highlightMentions(
          message.content,
        );
        if (hasMentions) {
          contentDisplay = (
            <div className="whitespace-pre-wrap">{highlightedText}</div>
          );
        }
      }

      const isHighlighted = message.id === newMessageHighlight;

      return (
        <div
          key={message.id}
          className={cn(
            'w-full max-w-full',
            isHighlighted && 'animate-pulse-once',
          )}
        >
          <div
            className={cn(
              'mx-auto max-w-[90ch] transition-all duration-300',
              isHighlighted && 'bg-muted/40 rounded-lg',
            )}
          >
            <AutonomousMessage
              message={message}
              index={index}
              onNextStepClick={onNextStepClick}
              onRestore={onRestore}
              showStreaming={
                isLoading && message === messages[messages.length - 1]
              }
              confirmation={
                message === messages[messages.length - 1] ? confirmation : null
              }
              contentDisplay={contentDisplay}
              showThinking={!disableThinking}
              disableDisplayComponents={disableDisplayComponents}
              disableToolCalls={disableToolCalls}
              groupChatOnly={groupChatOnly}
              isSharedView={isSharedView}
            />
          </div>
        </div>
      );
    });
  }, [
    messages,
    isLoading,
    onNextStepClick,
    onRestore,
    confirmation,
    newMessageHighlight,
    disableThinking,
    disableDisplayComponents,
    disableToolCalls,
  ]);

  return (
    <div className="relative h-full flex-1 overflow-hidden">
      <ScrollArea ref={scrollAreaRef} className="h-full" type="hover">
        <div className="flex flex-col gap-3 pb-4" ref={messagesEndRef}>
          {renderMessages}
          {/* Thinking indicator at the bottom */}
          {showThinkingState && (
            <div className="w-full max-w-full py-1">
              <div className="mx-auto max-w-[90ch]">
                <div className="flex w-full justify-start py-4 pl-4">
                  <div className="text-left">
                    <div className="text-muted-foreground/80 flex items-center gap-2 text-base">
                      <MessageLoading />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </ScrollArea>

      {/* Single scroll button - positioned at the bottom right */}
      <AnimatePresence>
        {showScrollButton && messages.length > 0 && (
          <motion.div
            className={cn('absolute right-6 bottom-4', 'z-10')}
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{ duration: ANIMATION_DURATION.FADE }}
          >
            <Button
              size="sm"
              variant="secondary"
              onClick={() => scrollToBottom('smooth', true)}
              className="relative gap-2 rounded-full px-4 shadow-md will-change-transform"
            >
              {unreadCount > 0 && (
                <div className="bg-primary text-primary-foreground absolute -top-1 -right-1 flex h-5 min-w-5 items-center justify-center rounded-full text-xs font-medium">
                  {unreadCount}
                </div>
              )}
              <ArrowDown className="h-4 w-4" />
              <span className="text-xs">
                {unreadCount > 0
                  ? `${unreadCount} new message${unreadCount > 1 ? 's' : ''}`
                  : 'Latest'}
              </span>
            </Button>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
