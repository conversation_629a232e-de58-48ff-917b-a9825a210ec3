import React from 'react';

import { formatters } from '@/utils/date-formatters';

export interface DashboardHeaderProps {
  title: string;
  createdAt?: string;
  onExportPDF?: () => void;
}

const DashboardHeader: React.FC<DashboardHeaderProps> = ({
  title,
  createdAt,
}) => {
  const formatDate = formatters.withTime;

  return (
    <div className="border-b pb-6 mb-8">
      <div className="mb-4 flex items-start justify-between">
        <div className="flex-1">
          <h1 className="text-foreground text-3xl font-bold">
            {title}
          </h1>
        </div>
        <div className="ml-6 flex items-center gap-2">
          {createdAt && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <span>
                Generated on {formatDate(createdAt)}
              </span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DashboardHeader;
