import React from 'react';

import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from '@/components/chat/components/message/charts';
import { processChartData } from '@/components/chat/components/message/charts/common/functional-utils';
import { BarChart3 } from 'lucide-react';

import WidgetCard from './widget-card';

export interface ChartWidgetProps {
  widget: any;
  style?: React.CSSProperties;
}

const ChartWidget: React.FC<ChartWidgetProps> = ({ widget, style }) => {
  const {
    title,
    description,
    chart_type,
    categories,
    datasets,
    x_axis,
    y_axis,
    show_legend,
    show_grid,
    currency_format,
    percentage_format,
  } = widget;

  const hasValidData = chart_type === 'sankey'
    ? (widget.sankey_nodes?.length && widget.sankey_links?.length)
    : (datasets && categories && datasets.length > 0 && categories.length > 0);

  if (!hasValidData) {
    return (
      <WidgetCard
        title={title || 'Chart'}
        icon={<Bar<PERSON>hart3 className="h-4 w-4" />}
        style={style}
      >
        <div className="flex h-48 items-center justify-center">
          <p className="text-muted-foreground text-sm">No chart data available</p>
        </div>
      </WidgetCard>
    );
  }

  const chartData = {
    labels: categories,
    datasets: datasets.map((dataset: any) => ({
      ...dataset,
      backgroundColor: undefined,
    })),
    x_axis,
    y_axis,
    display_options: {
      show_legend: show_legend !== false,
      show_grid: show_grid !== false,
      currency_format: currency_format || false,
      percentage_format: percentage_format || false,
    },
  };

  const processedData = processChartData(chartData);

  const renderChart = () => {
    const commonProps = {
      data: processedData,
      chartData,
      height: 350,
      title,
    };

    const chartComponents = {
      bar: BarChart,
      line: LineChart,
      pie: PieChart,
      area: AreaChart,
      step_area: StepAreaChart,
      radar: RadarChart,
    };

    if (chart_type === 'sankey') {
      if (widget.sankey_nodes && widget.sankey_links) {
        const sankeyData = {
          sankey_nodes: widget.sankey_nodes,
          sankey_links: widget.sankey_links,
        };
        return <SankeyChart data={sankeyData} chartData={chartData} height={400} title={title} />;
      }
      return <BarChart {...commonProps} />;
    }

    const ChartComponent = chartComponents[chart_type as keyof typeof chartComponents] || BarChart;
    const chartHeight = ['pie', 'radar'].includes(chart_type) ? 400 : 350;

    return <ChartComponent {...commonProps} height={chartHeight} />;
  };

  return (
    <WidgetCard
      title={title}
      description={description}
      icon={<BarChart3 className="h-4 w-4" />}
      style={style}
    >
      <div className="relative">
        {renderChart()}
      </div>
    </WidgetCard>
  );
};

export default ChartWidget;
