'use client';

import { CSSProperties, useCallback, useEffect, useRef, useState } from 'react';

import { flushSync } from 'react-dom';

import { AgentExampleQuestions } from '@/app/(home)/agents/_components/agent-example-questions';
import { ActionButton } from '@/components/chat/components/action-button';
import { AgentMentionDropdown } from '@/components/chat/components/agent-mention';
import { resourceCategories } from '@/components/chat/components/data';
import { ResourceMentionDropdown } from '@/components/chat/components/resource-mention';
import { FilePreviewList } from '@/components/chat/file-preview';
import { MessageInputProps } from '@/components/chat/types';
import {
  getIncompleteAtCursor,
  highlightMentions,
} from '@/components/chat/utils/mention-highlighting';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { useBuiltinConnectors } from '@/hooks/use-builtin-connectors';
import { useFileUpload } from '@/hooks/use-file-upload';
import { useKnowledgeBases } from '@/hooks/use-knowledge-bases';
import { cn } from '@/lib/utils';
import debounce from 'lodash/debounce';
import { Paperclip, Settings } from 'lucide-react';

export function MessageInput({
  onSendMessage,
  disabled,
  value: externalValue,
  onChange,
  isStreaming,
  onStop,
  showExampleQuestions = false,
  isEmptyConversation = false,
  resourceType,
}: MessageInputProps) {
  // File upload hook
  const {
    files,
    addFiles,
    removeFile,
    clearAll: clearAllFiles,
    isUploading,
    hasValidationErrors,
    completedAttachmentIds,
  } = useFileUpload();

  // State
  const [localValue, setLocalValue] = useState(externalValue || '');

  // # Mention feature states
  const [showResourceMention, setShowResourceMention] = useState(false);
  const [resourceMentionPosition, setResourceMentionPosition] = useState({
    top: 0,
    left: 0,
  });
  const [resourceMentionFilter, setResourceMentionFilter] = useState('');

  // @ Mention feature states
  const [showAgentMention, setShowAgentMention] = useState(false);
  const [agentMentionPosition, setAgentMentionPosition] = useState({
    top: 0,
    left: 0,
  });
  const [agentMentionFilter, setAgentMentionFilter] = useState('');

  // Animation states for text limit feedback
  const [isShaking, setIsShaking] = useState(false);

  // Get knowledge bases
  const { kbs, isLoading: kbsLoading } = useKnowledgeBases();

  // Refs
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const isSubmittingRef = useRef(false);
  const overlayRef = useRef<HTMLDivElement>(null);

  // Derived state
  const hasContent = Boolean(localValue?.trim());

  // Fetch builtin tools
  const { data: builtinTools, isPending: isBuiltinToolsLoading } =
    useBuiltinConnectors();

  // Prepare dynamic resource categories by merging static data with dynamic KB collections and builtin tools
  const dynamicResourceCategories = [
    {
      id: 'tools',
      name: 'Tools',
      icon: <Settings className="h-5 w-5 text-purple-500" />,
      items: isBuiltinToolsLoading
        ? [
            {
              id: 'loading',
              title: 'Loading tools...',
              description: 'Please wait',
            },
          ]
        : builtinTools
            ?.filter((tool) => tool.is_active)
            .map((tool) => ({
              id: tool.id,
              title: tool.display_name || tool.name,
              description: tool.description || 'No description available',
            })) || [],
    },
    ...resourceCategories.map((category) => {
      if (category.isDynamic && category.source === 'kb_collections') {
        return {
          ...category,
          items: kbsLoading
            ? [
                {
                  id: 'loading',
                  title: 'Loading collections...',
                  description: 'Please wait',
                },
              ]
            : kbs,
        };
      }
      return category;
    }),
  ];

  // Effects
  // Sync with external value when it changes
  useEffect(() => {
    if (externalValue !== undefined) {
      setLocalValue(externalValue);
    }
  }, [externalValue]);

  // Debounced update to parent
  const debouncedOnChange = useCallback(
    debounce((value: string) => {
      onChange?.(value);
    }, 100),
    [onChange],
  );

  // Auto-resize textarea with debounce
  const debouncedResize = useCallback(
    debounce(() => {
      if (textareaRef.current) {
        textareaRef.current.style.height = 'auto';
        const maxHeight = window.innerWidth < 640 ? 200 : 250; // Responsive max height
        const newHeight = Math.min(textareaRef.current.scrollHeight, maxHeight);
        textareaRef.current.style.height = `${newHeight}px`;
      }
    }, 50),
    [],
  );

  useEffect(() => {
    debouncedResize();
    return () => {
      debouncedResize.cancel();
    };
  }, [localValue, debouncedResize]);

  // Add this function to sync scrolling
  const syncScroll = () => {
    if (overlayRef.current && textareaRef.current) {
      overlayRef.current.scrollTop = textareaRef.current.scrollTop;
      overlayRef.current.scrollLeft = textareaRef.current.scrollLeft;
    }
  };

  // Event handlers
  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    // Don't update if we're in the middle of submitting
    if (isSubmittingRef.current) return;

    const newValue = e.target.value;
    const maxLength = 10000; // Set reasonable text limit

    // Check for text length limit
    if (newValue.length > maxLength) {
      // Trigger shake animation
      setIsShaking(true);
      setTimeout(() => setIsShaking(false), 600);
      return; // Don't update if over limit
    }

    setLocalValue(newValue);
    debouncedOnChange(newValue);

    // Check for mention triggers
    checkForMentions(newValue, e.target.selectionStart);

    // Ensure textarea resizes properly after content changes
    debouncedResize();
  };

  // Handle paste events with animation and file support
  const handlePaste = useCallback(
    (e: React.ClipboardEvent<HTMLTextAreaElement>) => {
      // First check for files
      const items = Array.from(e.clipboardData.items);
      const files = items
        .filter((item) => item.kind === 'file')
        .map((item) => item.getAsFile())
        .filter((file): file is File => file !== null);

      if (files.length > 0) {
        e.preventDefault();
        addFiles(files);
        return;
      }

      // Handle text paste
      const pastedText = e.clipboardData.getData('text');
      const currentLength = localValue.length;
      const newLength = currentLength + pastedText.length;
      const maxLength = 10000;

      // If pasting would exceed limit, show shake animation
      if (newLength > maxLength) {
        e.preventDefault();
        setIsShaking(true);
        setTimeout(() => setIsShaking(false), 600);
        return;
      }

      // If pasting a lot of text, trigger scroll animation
      if (pastedText.length > 100) {
        setTimeout(() => {
          if (textareaRef.current) {
            // Smooth scroll to show new content
            textareaRef.current.scrollTo({
              top: textareaRef.current.scrollHeight,
              behavior: 'smooth',
            });
          }
        }, 100);
      }
    },
    [localValue, addFiles],
  );

  // Check for # and @ mentions in the text
  const checkForMentions = (text: string, cursorPos: number) => {
    const mentionInfo = getIncompleteAtCursor(text, cursorPos);

    if (!mentionInfo) {
      // If no mentions, hide both dropdowns
      setShowResourceMention(false);
      setShowAgentMention(false);
      return;
    }

    if (mentionInfo.type === 'resource') {
      // Show resource dropdown
      setResourceMentionFilter(mentionInfo.filter.toLowerCase());

      if (textareaRef.current) {
        const textareaRect = textareaRef.current.getBoundingClientRect();
        const cursorCoords = getCaretCoordinates(
          textareaRef.current,
          cursorPos,
        );

        setResourceMentionPosition({
          top: window.scrollY + textareaRect.top + cursorCoords.top - 180,
          left: Math.max(
            16,
            Math.min(
              window.scrollX + textareaRect.left + cursorCoords.left,
              window.innerWidth - 320,
            ),
          ),
        });
        setShowResourceMention(true);
      }

      // Hide agent mention
      setShowAgentMention(false);
    } else if (mentionInfo.type === 'agent') {
      // Show agent dropdown
      setAgentMentionFilter(mentionInfo.filter.toLowerCase());

      if (textareaRef.current) {
        const textareaRect = textareaRef.current.getBoundingClientRect();
        const cursorCoords = getCaretCoordinates(
          textareaRef.current,
          cursorPos,
        );

        setAgentMentionPosition({
          top: window.scrollY + textareaRect.top + cursorCoords.top - 180,
          left: Math.max(
            16,
            Math.min(
              window.scrollX + textareaRect.left + cursorCoords.left,
              window.innerWidth - 320,
            ),
          ),
        });
        setShowAgentMention(true);
      }

      // Hide resource mention
      setShowResourceMention(false);
    }
  };

  // Helper function to get caret coordinates
  const getCaretCoordinates = (
    element: HTMLTextAreaElement,
    position: number,
  ) => {
    // Create a temporary div to measure text
    const div = document.createElement('div');
    const style = window.getComputedStyle(element);
    const properties = [
      'fontFamily',
      'fontSize',
      'fontWeight',
      'wordWrap',
      'whiteSpace',
      'borderLeftWidth',
      'borderTopWidth',
      'borderRightWidth',
      'borderBottomWidth',
      'paddingLeft',
      'paddingTop',
      'paddingRight',
      'paddingBottom',
      'lineHeight',
    ] as const satisfies (keyof CSSProperties)[];

    // Copy styles from textarea to div
    properties.forEach((prop) => {
      div.style[prop] = style[prop];
    });

    // Set div content and styles
    div.textContent = element.value.substring(0, position);
    div.style.position = 'absolute';
    div.style.visibility = 'hidden';
    div.style.whiteSpace = 'pre-wrap';
    div.style.wordWrap = 'break-word';
    div.style.overflow = 'hidden';
    div.style.width = element.offsetWidth + 'px';

    // Add a span to mark cursor position
    const span = document.createElement('span');
    span.textContent = element.value.charAt(position) || '.';
    div.appendChild(span);

    // Add to document, measure, then remove
    document.body.appendChild(div);
    const coordinates = {
      top:
        span.offsetTop +
        parseInt(style.borderTopWidth) +
        parseInt(style.paddingTop),
      left:
        span.offsetLeft +
        parseInt(style.borderLeftWidth) +
        parseInt(style.paddingLeft),
    };
    document.body.removeChild(div);

    return coordinates;
  };

  const handleSubmit = (e?: React.FormEvent) => {
    e?.preventDefault();

    // Block submission if files are still uploading/validating or have errors
    if (isUploading || hasValidationErrors) {
      return;
    }

    if (!hasContent || disabled || isSubmittingRef.current) return;

    // Set the submitting flag to prevent race conditions
    isSubmittingRef.current = true;

    // Capture message before clearing
    const messageContent = localValue.trim();

    try {
      // Use flushSync to ensure state updates are processed immediately
      flushSync(() => {
        setLocalValue('');
        // Call onChange directly instead of using the debounced version
        onChange?.('');
      });

      // Send the message with attachment IDs
      onSendMessage(messageContent, completedAttachmentIds);

      // Clear uploaded files after sending
      clearAllFiles();

      // Reset textarea height
      if (textareaRef.current) {
        textareaRef.current.style.height = 'auto';
      }
    } finally {
      // Reset the submitting flag
      isSubmittingRef.current = false;
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // Let the dropdowns handle their own keyboard events
    if (showResourceMention || showAgentMention) {
      return;
    }

    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();

      if (!isStreaming) {
        handleSubmit();
      }
    }
  };

  // Handle resource mention selection
  const handleResourceSelect = (itemId: string, fullPath: string) => {
    if (textareaRef.current) {
      const cursorPos = textareaRef.current.selectionStart;
      const textBeforeCursor = localValue.substring(0, cursorPos);
      const textAfterCursor = localValue.substring(cursorPos);

      // Get mention being typed using the utility function
      const mentionInfo = getIncompleteAtCursor(localValue, cursorPos);

      if (mentionInfo && mentionInfo.type === 'resource') {
        // Construct new text with the replacement
        const cleanPath = fullPath.replace(/\//g, '/').toLowerCase();
        const newText =
          textBeforeCursor.substring(0, mentionInfo.startIndex) +
          `#${cleanPath}` +
          ' ' + // Add a space AFTER the entire mention
          textAfterCursor;

        // Update the input value
        setLocalValue(newText);
        onChange?.(newText);
        setShowResourceMention(false);

        // Position the cursor after the mention and space
        setTimeout(() => {
          if (textareaRef.current) {
            const newCursorPos =
              mentionInfo.startIndex + `#${cleanPath} `.length;
            textareaRef.current.focus();
            textareaRef.current.setSelectionRange(newCursorPos, newCursorPos);
          }
        }, 0);
      }
    }
  };

  // Handle agent mention selection
  const handleAgentSelect = (agentId: string, agentName: string) => {
    if (textareaRef.current) {
      const cursorPos = textareaRef.current.selectionStart;
      const textBeforeCursor = localValue.substring(0, cursorPos);
      const textAfterCursor = localValue.substring(cursorPos);

      // Get mention being typed using the utility function
      const mentionInfo = getIncompleteAtCursor(localValue, cursorPos);

      if (mentionInfo && mentionInfo.type === 'agent') {
        // Prepare agent name (removing any parenthetical information)
        const displayName = agentName.split(' (')[0];

        // Construct new text with the replacement
        const newText =
          textBeforeCursor.substring(0, mentionInfo.startIndex) +
          `@${displayName}` +
          ' ' + // Add a space AFTER the entire mention
          textAfterCursor;

        // Update the input value
        setLocalValue(newText);
        onChange?.(newText);
        setShowAgentMention(false);

        // Position the cursor after the mention and space
        setTimeout(() => {
          if (textareaRef.current) {
            const newCursorPos =
              mentionInfo.startIndex + `@${displayName} `.length;
            textareaRef.current.focus();
            textareaRef.current.setSelectionRange(newCursorPos, newCursorPos);
          }
        }, 0);
      }
    }
  };

  // File upload handlers
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = () => {
    fileInputRef.current?.click();
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length > 0) {
      addFiles(files);
    }
    // Reset input to allow selecting the same file again
    e.target.value = '';
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      addFiles(files);
    }
  };

  // Add autofocus functionality
  useEffect(() => {
    // Auto focus the textarea when component mounts
    if (textareaRef.current && !disabled) {
      // Small delay to ensure focus works properly after page load/navigation
      const timer = setTimeout(() => {
        textareaRef.current?.focus();
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [disabled]);

  const { highlightedText, hasMentions } = highlightMentions(localValue);

  return (
    <div className="flex grow flex-col justify-end overflow-y-auto pt-2">
      {/* Show example questions when conversation is empty AND showExampleQuestions is true */}
      {showExampleQuestions && isEmptyConversation && !localValue && (
        <div className="mb-4 flex w-full grow items-center justify-center overflow-y-auto">
          <div className="size-full">
            <AgentExampleQuestions
              resourceType={resourceType}
              onQuestionClick={(question) => {
                setLocalValue(question);
                if (onChange) onChange(question);
                // Focus on the textarea after selecting a question
                setTimeout(() => {
                  if (textareaRef.current) {
                    textareaRef.current.focus();
                    // Move cursor to the end of the text
                    const length = question.length;
                    textareaRef.current.setSelectionRange(length, length);
                  }
                }, 100);
              }}
            />
          </div>
        </div>
      )}
      <div className="border-muted-foreground/10 bg-muted/30 hover:border-muted-foreground/20 dark:border-muted-foreground/20 dark:bg-muted/30 dark:hover:border-muted-foreground/30 shrink-0 overflow-hidden rounded-lg border shadow-md transition-all duration-200 hover:shadow-lg sm:rounded-lg">
        {/* File Upload Area */}
        {files.length > 0 && (
          <div className="border-muted-foreground/10 border-b p-3">
            <FilePreviewList
              files={files}
              showPreview={true}
              onRemove={removeFile}
            />
          </div>
        )}

        {/* Message Input Row */}
        <form
          onSubmit={handleSubmit}
          className="relative"
          onDragOver={handleDragOver}
          onDrop={handleDrop}
        >
          {/* Resource Mention Dropdown */}
          <ResourceMentionDropdown
            isVisible={showResourceMention}
            position={resourceMentionPosition}
            filter={resourceMentionFilter}
            categories={dynamicResourceCategories}
            onSelect={handleResourceSelect}
            onClose={() => setShowResourceMention(false)}
          />

          {/* Agent Mention Dropdown */}
          <AgentMentionDropdown
            isVisible={showAgentMention}
            position={agentMentionPosition}
            filter={agentMentionFilter}
            onSelect={handleAgentSelect}
            onClose={() => setShowAgentMention(false)}
          />

          {/* Message Input Container */}
          <div
            className={cn(
              'relative overflow-hidden transition-transform duration-150',
              isShaking && 'animate-shake',
            )}
          >
            {/* Display the textarea for input */}
            <Textarea
              ref={textareaRef}
              value={localValue}
              onChange={handleChange}
              onKeyDown={handleKeyDown}
              onPaste={handlePaste}
              onScroll={syncScroll}
              placeholder=""
              disabled={isStreaming ? false : disabled}
              autoFocus={!disabled}
              className={cn(
                'w-full',
                'max-h-[200px] min-h-[50px] sm:max-h-[250px] sm:min-h-[80px]',
                'custom-scrollbar resize-none overflow-y-auto',
                'px-3 py-3 sm:px-4 sm:py-4',
                'border-none focus-visible:ring-0',
                'bg-transparent',
                'text-sm leading-relaxed sm:text-base',
                // Only make text transparent when there are mentions, otherwise use normal text color
                hasMentions
                  ? 'caret-foreground selection:bg-primary/20 text-transparent'
                  : 'text-foreground caret-foreground',
                // Don't hide when empty to ensure cursor is visible
                'placeholder:opacity-0', // Use placeholder opacity instead
              )}
              rows={1}
            />

            {/* Display highlighted text overlay when there are mentions */}
            {hasMentions && (
              <div
                ref={overlayRef}
                className="text-foreground pointer-events-none absolute inset-0 h-full w-full px-3 py-3 text-sm leading-relaxed break-words whitespace-pre-wrap sm:px-4 sm:py-4 sm:text-base"
                style={{
                  fontFamily: 'inherit',
                  fontSize: 'inherit',
                  letterSpacing: 'inherit',
                  wordSpacing: 'inherit',
                  lineHeight: 'inherit',
                  textIndent: 'inherit',
                  tabSize: 'inherit',
                  overflow: 'hidden',
                }}
              >
                {highlightedText}
              </div>
            )}

            {!localValue && files.length === 0 && (
              <div className="pointer-events-none absolute inset-0 px-3 py-3 text-gray-400 sm:px-4 sm:py-4 dark:text-gray-500">
                <div className="text-sm leading-relaxed sm:text-base">
                  Type{' '}
                  <span className="text-primary mx-0.5 font-medium">#</span> to
                  mention a tool, or document, or{' '}
                  <span className="text-primary mx-0.5 font-medium">@</span> to
                  mention an agent.
                </div>
              </div>
            )}

            {!localValue && files.length > 0 && (
              <div className="pointer-events-none absolute inset-0 px-3 py-3 text-gray-400 sm:px-4 sm:py-4 dark:text-gray-500">
                <div className="text-sm leading-relaxed sm:text-base">
                  {isUploading
                    ? 'Processing files...'
                    : hasValidationErrors
                      ? 'Fix file errors before sending'
                      : 'Type your message...'}
                </div>
              </div>
            )}
          </div>
        </form>

        {/* Model and Tools Row */}
        <div className="flex items-center gap-1 bg-transparent p-2 sm:gap-2 sm:p-3">
          {/* File Upload Button */}
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={handleFileSelect}
            disabled={disabled || isStreaming}
            className="h-8 w-8 p-0"
            title="Attach files"
          >
            <Paperclip className="h-4 w-4" />
          </Button>

          {/* Hidden File Input */}
          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept="image/*,.pdf,.txt,.csv,.json,.docx,.xlsx"
            onChange={handleFileInputChange}
            className="hidden"
          />

          <div className="flex-1" />

          {/* Send/Stop Button */}
          <ActionButton
            isStreaming={!!isStreaming}
            hasContent={hasContent}
            disabled={disabled || isUploading || hasValidationErrors}
            onSubmit={handleSubmit}
            onStop={onStop}
          />
        </div>
      </div>
    </div>
  );
}
