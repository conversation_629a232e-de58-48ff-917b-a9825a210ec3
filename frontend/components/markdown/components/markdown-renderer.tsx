import React, { useEffect, useState } from 'react';
import ReactMarkdown, { Components } from 'react-markdown';
import rehypeRaw from 'rehype-raw';
import remarkGfm from 'remark-gfm';
import { MarkdownCodeBlock } from './markdown-code-block';
import { MarkdownStreaming } from './markdown-streaming';
import { MarkdownRendererProps } from '../types';
import { getTheme } from '../config/markdown-themes';
import { hasMarkdown, highlightMentions, sanitizeContent, normalizeHeaders } from '../utils';

/**
 * Main configurable markdown renderer component
 */
export const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({
  content,
  enableStreaming = false,
  enableMentions = false,
  enableMermaid = true,
  isStreaming = false,
  isStatusMessage = false, // Reserved for future use
  className = '',
  theme = 'default',
}) => {
  const [processedContent, setProcessedContent] = useState(content);
  const [hasError, setHasError] = useState(false);
  const themeClasses = getTheme(theme);

  useEffect(() => {
    try {
      const cleanContent = sanitizeContent(content);
      const normalizedContent = normalizeHeaders(cleanContent);
      setProcessedContent(normalizedContent);
      setHasError(false);
    } catch (error) {
      console.error('Error processing content:', error);
      setHasError(true);
    }
  }, [content]);

  if (!processedContent) {
    return null;
  }

  if (hasError) {
    // Fallback to plain text if markdown rendering fails
    const { highlightedText } = enableMentions
      ? highlightMentions(processedContent)
      : { highlightedText: processedContent };
    return (
      <div className="break-words whitespace-pre-wrap">{highlightedText}</div>
    );
  }

  // Handle streaming content
  if (enableStreaming && isStreaming) {
    return (
      <MarkdownStreaming
        content={processedContent}
        isStreaming={isStreaming}
        className={className}
      />
    );
  }

  // If content doesn't have markdown, render as plain text
  if (!hasMarkdown(processedContent)) {
    const { highlightedText } = enableMentions
      ? highlightMentions(processedContent)
      : { highlightedText: processedContent };
    return (
      <div className={`break-words whitespace-pre-wrap ${className}`}>
        {highlightedText}
      </div>
    );
  }

  // Define custom components for ReactMarkdown
  const components: Components = {
    code(props) {
      const { className, children } = props;
      // Extract inline property safely
      const inline = Boolean('inline' in props ? props.inline : false);
      return (
        <MarkdownCodeBlock
          className={className}
          inline={inline}
          enableMermaid={enableMermaid}
          isStreaming={isStreaming}
        >
          {children}
        </MarkdownCodeBlock>
      );
    },
    p(props) {
      const { children, node } = props;
      const firstChild = node?.children?.[0];
      
      if (firstChild?.type === 'text' && firstChild.value?.match(/^#{1,6}\s/)) {
        const matches = firstChild.value.match(/^(#{1,6})\s/);
        if (!matches) return null;

        const headingLevel = matches[1].length;
        const headingText = firstChild.value.substring(headingLevel + 1);
        
        // Use conditional rendering for heading levels
        if (headingLevel === 1) return <h1>{headingText}</h1>;
        if (headingLevel === 2) return <h2>{headingText}</h2>;
        if (headingLevel === 3) return <h3>{headingText}</h3>;
        if (headingLevel === 4) return <h4>{headingText}</h4>;
        if (headingLevel === 5) return <h5>{headingText}</h5>;
        if (headingLevel === 6) return <h6>{headingText}</h6>;
        
        return <h1>{headingText}</h1>; // fallback
      }

      // Process mentions in paragraph text if enabled
      if (enableMentions && typeof children === 'string') {
        const { highlightedText } = highlightMentions(children);
        return <p>{highlightedText}</p>;
      }

      return <p>{children}</p>;
    },
  };

  try {
    return (
      <ReactMarkdown
        rehypePlugins={[rehypeRaw]}
        remarkPlugins={[remarkGfm]}
        components={components}
        className={`${themeClasses} ${className}`}
      >
        {processedContent}
      </ReactMarkdown>
    );
  } catch (error) {
    console.error('Error rendering markdown:', error);
    setHasError(true);
    const { highlightedText } = enableMentions
      ? highlightMentions(processedContent)
      : { highlightedText: processedContent };
    return (
      <div className="break-words whitespace-pre-wrap">{highlightedText}</div>
    );
  }
};
