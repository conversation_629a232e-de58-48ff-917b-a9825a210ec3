// Simplified theme system using Tailwind Typography plugin
export const themes = {
  default: 'prose prose-neutral max-w-none dark:prose-invert',
  compact: 'prose prose-neutral prose-sm max-w-none dark:prose-invert',
  enhanced: 'prose prose-neutral prose-lg max-w-none dark:prose-invert',
} as const;

export const getTheme = (themeName: keyof typeof themes = 'default'): string => {
  return themes[themeName];
};
