# Frontend Features Architecture Analysis

## Overview

This document provides a comprehensive analysis of the architectural patterns used in the `frontend/features` directory of the cloud cost optimization application. The analysis covers file naming conventions, folder organization, and code patterns across different features.

## Feature Directory Structure

The features directory contains 9 main feature modules:
- `agent`
- `builtin-tools`
- `conversation`
- `recommendation`
- `resource`
- `task`
- `task-template`
- `user`
- `workspaces`

## Common Architectural Patterns

### 1. Folder Organization Pattern

Each feature follows a consistent folder structure with the following common directories:

```
feature-name/
├── components/          # React components
├── hooks/              # React Query hooks
├── services/           # API service layer
├── models/             # TypeScript type definitions
├── schema/             # Zod validation schemas
├── config/             # Configuration constants
├── provider/           # React Context providers
└── data/              # Static data/constants
```

**Observations:**
- Not all features have all directories (e.g., `conversation` only has `api/`)
- Directory names are consistently plural
- Clear separation of concerns between different layers

### 2. File Naming Conventions

#### API Services
- Pattern: `{feature-name}.api.ts`
- Examples: `user.api.ts`, `task.api.ts`, `workspace.api.ts`

#### React Query Hooks
- Pattern: `{feature-name}.query.ts`
- Examples: `user.query.ts`, `task.query.ts`, `workspace.query.ts`

#### Type Definitions
- Pattern: `{feature-name}.type.ts`
- Examples: `user.type.ts`, `task.type.ts`, `resource.type.ts`

#### Validation Schemas
- Pattern: `{feature-name}.schema.ts`
- Examples: `user.schema.ts`, `workspace.schema.ts`, `task-template.schema.ts`

#### Configuration Files
- Pattern: `{feature-name}-{config-type}.config.ts`
- Examples: `task-status.config.ts`, `workspace-provider.config.tsx`

#### Components
- Pattern: `{component-name}.tsx` or organized in subdirectories
- Examples: `user-form.tsx`, `create-user-dialog.tsx`
- Table components often organized in subdirectories: `{feature}-table/`

### 3. API Service Architecture

All features follow a consistent API service pattern:

```typescript
export const featureApi = {
  // Basic CRUD operations
  list: (query: QueryParams) => fetchData(api.GET('/api/v1/endpoint', { params: { query } })),
  
  // Detailed operations with nested structure
  detail: (id: string) => ({
    update: (body: UpdateSchema) => api.PUT('/api/v1/endpoint/{id}', { params: { path: { id } }, body }),
    delete: () => api.DELETE('/api/v1/endpoint/{id}', { params: { path: { id } } }),
    getInfo: () => fetchData(api.GET('/api/v1/endpoint/{id}', { params: { path: { id } } })),
  }),
};
```

**Key Patterns:**
- Use of `fetchData` wrapper for GET operations
- Nested structure for detail operations
- Consistent parameter passing with `params.path` and `params.query`
- Timezone handling for date-related operations

### 4. React Query Hook Architecture

Features implement a layered hook architecture:

```typescript
// Query keys factory
const featureQueryKeys = createQueryKeys('feature', {
  list: (params: QueryParams) => ({
    queryKey: [params],
    queryFn: () => featureApi.list(handleSkipOfPagination(params)),
  }),
});

// Individual hooks
const useList = (params: QueryParams) => {
  return useQuery({
    ...featureQueryKeys.list(params),
    placeholderData: keepPreviousData,
  });
};

// Exported query object
export const featureQuery = {
  query: {
    useList,
    useDetail,
  },
  mutation: {
    useCreate,
    useUpdate,
    useDelete,
  },
};
```

**Key Patterns:**
- Separation of queries and mutations
- Use of `createQueryKeys` factory for consistent caching
- `keepPreviousData` for smooth pagination
- Consistent naming: `use{Operation}`

### 5. Component Architecture

#### Table Components
Tables follow a consistent pattern with separate files:
- `{feature}.column.tsx` - Column definitions
- `{feature}-table.tsx` - Main table component
- Often organized in subdirectories for complex tables

Example structure:
```typescript
// Column definitions
export const userColumns: ColumnDef<SchemaUserPublic>[] = [
  {
    accessorKey: 'full_name',
    header: 'Full Name',
  },
  // ...
];

// Table component with props interface
interface TableProps {
  // props
}

export function FeatureTable({ ...props }: TableProps) {
  // implementation
}
```

#### Form Components
- Consistent use of React Hook Form with Zod validation
- Props interface pattern with `Props` type alias
- Common pattern of `onSubmit` and `isPending` props

#### Dialog Components
- Consistent pattern using shadcn/ui Dialog components
- `useToggle` hook for open/close state
- Child render prop pattern for triggers

### 6. Schema and Validation

Zod schemas follow consistent patterns:

```typescript
export const featureSchema = z.object({
  field1: z.string().min(1),
  field2: z.string().email(),
  // ...
});

export type FeatureSchema = z.infer<typeof featureSchema>;
```

**Key Patterns:**
- Export both schema and inferred type
- Consistent naming: `{feature}Schema`
- Use of Zod validation rules

### 7. Configuration Patterns

Configuration files use utility functions for consistent structure:

```typescript
export const FEATURE_STATUS_CONFIG = createUtilityConfig({
  [Status.active]: {
    label: 'Active',
    variant: 'success',
  },
  [Status.inactive]: {
    label: 'Inactive',
    variant: 'destructive',
  },
} satisfies Record<Status, ConfigType>);
```

**Key Patterns:**
- Use of `createUtilityConfig` utility
- `satisfies` for type safety
- Consistent naming: `{FEATURE}_{CONFIG_TYPE}_CONFIG`

### 8. Provider Pattern

Context providers follow a consistent pattern:

```typescript
const FeatureContext = createContext<FeatureContextType | null>(null);

export const FeatureProvider = ({ children, ...props }: PropsWithChildren<Props>) => {
  // context logic
  return (
    <FeatureContext.Provider value={contextValue}>
      {children}
    </FeatureContext.Provider>
  );
};

export const useFeatureContext = () => {
  const context = useContext(FeatureContext);
  if (!context) throw new Error('useFeatureContext must be used within FeatureProvider');
  return context;
};
```

### 9. Type Definition Patterns

Type definitions consistently use OpenAPI generated types:

```typescript
export type FeatureQueryParams = NonNullable<
  PathsRequestQueryDto<'/api/v1/endpoint/'>
>;
```

**Key Patterns:**
- Leverage OpenAPI generated types
- Use `NonNullable` utility type
- Consistent with API endpoint paths

## Feature-Specific Observations

### User Feature
- Complete CRUD operations
- Profile management
- Authentication context
- User table with role-based rendering

### Task Feature
- Complex nested API structure
- Status configuration with badges
- Activity tracking
- Enable/disable functionality

### Workspaces Feature
- Provider configuration pattern
- Card-based UI components
- Default workspace handling

### Builtin-tools Feature
- Permission-based architecture
- Tool card components with UI subdirectory
- Agent integration

### Resource Feature
- Chat integration
- Multiple table types
- Resource type configurations

## Architectural Strengths

1. **Consistency**: All features follow the same structural patterns
2. **Separation of Concerns**: Clear separation between API, hooks, components, and types
3. **Type Safety**: Heavy use of TypeScript and Zod for runtime validation
4. **Reusability**: Common patterns and utility functions
5. **Maintainability**: Predictable structure makes navigation easy
6. **Scalability**: Feature-based organization supports growth

## Recommendations

1. **Documentation**: Consider adding feature-level README files for complex features
2. **Testing**: Establish testing patterns for each layer (API, hooks, components)
3. **Barrel Exports**: Consider index files for cleaner imports
4. **Error Handling**: Standardize error handling patterns across features
5. **Loading States**: Implement consistent loading state patterns

## Conclusion

The frontend features architecture demonstrates a mature, well-structured approach to feature organization. The consistent patterns across all features make the codebase predictable and maintainable, while the clear separation of concerns ensures good architectural boundaries. The heavy use of TypeScript and modern React patterns (React Query, Context API, Hook Form) shows adherence to current best practices.