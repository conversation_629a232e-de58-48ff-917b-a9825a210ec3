from fastapi import APIRouter, HTTPException

from app.core.config import settings
from app.logger import logger
from app.models import (
    ExecuteConnectionToolRequest,
    ExecuteConnectionToolResponse,
    ListConnectionToolsRequest,
    ListConnectionToolsResponse,
)

router = APIRouter()


@router.post("/tools", response_model=ListConnectionToolsResponse)
async def list_connection_tools(request: ListConnectionToolsRequest) -> ListConnectionToolsResponse:
    try:
        logger.info(f"Getting connection tools for {request.connection_config}")
        tools = await settings.connection_manager.get_connection_tools(request.connection_config)
        return ListConnectionToolsResponse(tools=tools)
    except Exception as e:
        logger.exception(f"Error in get_connection_tools: {e}")
        raise HTTPException(status_code=500, detail="Error in get_connection_tools") from e


@router.post("/execute", response_model=ExecuteConnectionToolResponse)
async def call_connection_tool(
    request: ExecuteConnectionToolRequest,
) -> ExecuteConnectionToolResponse:
    try:
        logger.info(
            f"Calling connection tool {request.tool_name} with arguments {request.arguments}"
        )
        tool_result = await settings.connection_manager.call_connection_tool(
            request.connection_config, request.tool_name, request.arguments
        )
        return ExecuteConnectionToolResponse(tool_result=tool_result)
    except Exception as e:
        logger.exception(f"Error in call_connection_tool: {e}")
        raise HTTPException(status_code=500, detail="Error in call_connection_tool") from e
