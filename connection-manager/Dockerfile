# Use official Python slim Bookworm image
FROM python:3.12-slim-bookworm

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
        build-essential \
        curl \
        git \
        ca-certificates \
        unzip \
    && rm -rf /var/lib/apt/lists/*

# Copy uv and uvx binaries from the official uv image
COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /bin/


# Copy application code
COPY ./app /app/app
COPY ./pyproject.toml /app/
COPY ./uv.lock /app/

# Sync the project
# Ref: https://docs.astral.sh/uv/guides/integration/docker/#intermediate-layers
RUN --mount=type=cache,target=/root/.cache/uv uv sync

# Set environment variable for unbuffered output (useful for logs)
ENV PYTHONUNBUFFERED=1

# Run the FastAPI application with uvicorn directly
CMD ["uv", "run", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "4"]
